<?xml version="1.0"?>
<doc>
    <assembly>
        <name>BlueTape.PaymentService.API</name>
    </assembly>
    <members>
        <member name="T:BlueTape.PaymentService.API.Attributes.DevelopmentOnlyAttribute">
            <summary>
            Add if you want hide from production controller of endpoint
            </summary>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.AdminController.UpdateSequenceOrder(BlueTape.PaymentService.API.ViewModels.UpdateSequenceOrderViewModel,System.String,System.Threading.CancellationToken)">
            <summary>
            Updates the sequence order of payment requests in disbursement queues
            </summary>
            <param name="request">Request containing the ordered list of payment request IDs</param>
            <param name="updatedBy">User ID performing the update</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Result of the sequence update operation</returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.PaymentRequestsController.GetById(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets a single payment request full details
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.PaymentRequestsController.CancelById(System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Cancels a payment request
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.PaymentRequestsController.CancelByDrawId(System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Cancels a payment request
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.PaymentRequestsController.GetByFilter(BlueTape.PaymentService.API.Queries.PaymentRequestFilterQuery,System.Threading.CancellationToken)">
            <summary>
            Lists payment requests by various filtering options
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.PaymentRequestsController.Create(BlueTape.PaymentService.API.ViewModels.CreatePaymentRequestViewModel,System.String,System.Threading.CancellationToken)">
            <summary>
            Create a payment request
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.PaymentRequestsController.CreateTransaction(System.Guid,BlueTape.PaymentService.API.ViewModels.CreatePaymentRequestTransactionViewModel,System.String,System.Threading.CancellationToken)">
            <summary>
            Creates a transaction for payment request
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.PaymentRequestsController.GetPaymentRequestCommand(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Get commands
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.PaymentRequestsController.ExecuteTransaction(System.Guid,System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Executes a transaction
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.PaymentRequestsController.IsPaymentRequestExists(System.String,System.Threading.CancellationToken)">
            <summary>
            Checks whether a payment request exists for a payable
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.PaymentRequestsController.RetryFailedTransaction(System.Guid,System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Manually retry a failed transaction
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.PaymentRequestsController.RetryTransactionByPaymentRequestId(System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Retry transaction by payment request id
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.PaymentRequestsController.RetryFailedTransaction(System.Guid,System.Guid,System.String,BlueTape.PaymentService.API.ViewModels.RetryFailedTransactionRequest,System.Threading.CancellationToken)">
            <summary>
            Manually retry a failed pull from customer transaction
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.PaymentRequestsController.ApprovePaymentRequest(System.Guid,System.String,BlueTape.PaymentService.API.ViewModels.PaymentApprovalRequestViewModel,System.Threading.CancellationToken)">
            <summary>
            Approve manual payment approvalRequest
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.PaymentRequestsController.MarkPaymentRequestSucceeded(System.Guid,BlueTape.PaymentService.API.ViewModels.MarkPaymentRequestSucceededViewModel,System.String,System.Threading.CancellationToken)">
            <summary>
            Mark payment request as succeeded
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.PaymentRequestsController.GetAccount(System.String,System.Threading.CancellationToken)">
            <summary>
            Get accounts
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.QueueEventsController.HandlePaymentRequestMessage(System.Text.Json.JsonElement,System.Threading.CancellationToken)">
            <summary>
            Handles message from queue to create Pay Now payment request
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.QueueEventsController.TriggerPaymentScheduledJob(System.Threading.CancellationToken)">
            <summary>
            Triggers payment scheduled job
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.QueueEventsController.GetById(System.Threading.CancellationToken)">
            <summary>
            Triggers transaction status update scheduled job
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.QueueEventsController.TriggerNotificationConsumer(BlueTape.PaymentService.Domain.Messages.NotificationMessagePayloadV2,System.Threading.CancellationToken)">
            <summary>
            Triggers notification consumer
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.QueueEventsController.TriggerTransactionStatusUpdateJob(BlueTape.PaymentService.Domain.Messages.TransactionStatusMessagePayload,System.Threading.CancellationToken)">
            <summary>
            Triggers transaction status update job
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.QueueEventsController.TriggerOperationSync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Triggers legacy operation sync in MongoDB by payment request id
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.QueueEventsController.TriggerPerformOperation(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Performs legacy operation in MongoDB by payment request id
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.QueuesController.GetDisbursementQueuesPaymentRequests(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Gets disbursement queues payment requests
            </summary>
            <param name="provider">The payment provider (aion, cbw)</param>
            <param name="subscriptionCode">The subscription code of the payment provider as string</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>The queues with requests, without pagination</returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.TransactionsController.GetById(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets a single transactions full details
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.TransactionsController.CancelById(System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            Cancels a transaction
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.TransactionsController.GetTransactionExecutionHistory(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Get transaction request history by transaction id
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.TransactionsController.GetByFilter(BlueTape.PaymentService.API.Queries.TransactionFilterQuery,System.Threading.CancellationToken)">
            <summary>
            Returns list transactions by various filtering options
            </summary>
            <returns></returns>
        </member>
        <member name="M:BlueTape.PaymentService.API.Controllers.TransactionsController.GetTransactionByAccountCode(BlueTape.Integrations.Aion.Transactions.TransactionsQuery,System.Threading.CancellationToken)">
            <summary>
            Get transaction request history by transaction id
            </summary>
            <returns></returns>
        </member>
        <member name="P:BlueTape.PaymentService.API.ViewModels.PaymentRequestDetailsViewModel.InvoiceNumber">
            <summary>
                User-entered invoice number
            </summary>
        </member>
        <member name="P:BlueTape.PaymentService.API.ViewModels.PaymentRequestDetailsViewModel.Reason">
            <summary>
                User-entered reason for payment request
            </summary>
        </member>
        <member name="P:BlueTape.PaymentService.API.ViewModels.UpdateSequenceOrderViewModel.PaymentRequestIds">
            <summary>
            List of payment request IDs in their desired order
            </summary>
        </member>
    </members>
</doc>
