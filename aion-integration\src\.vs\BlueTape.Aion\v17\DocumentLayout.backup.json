{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\controllers\\transactioncontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\controllers\\transactioncontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\service\\achservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\service\\achservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\constants\\aionconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\constants\\aionconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.application.tests\\services\\achservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|solutionrelative:bluetape.application.tests\\services\\achservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\models\\ach\\pull\\createach.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\models\\ach\\pull\\createach.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\service\\transactionservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\service\\transactionservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\service\\dailyachprocessingservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\service\\dailyachprocessingservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\aionhttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\aionhttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\service\\reportservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\service\\reportservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.functions.aion.transactionstatusreport\\internaltransactionconsumer.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|solutionrelative:bluetape.functions.aion.transactionstatusreport\\internaltransactionconsumer.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\service\\companyservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\service\\companyservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\service\\bankaccountservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\service\\bankaccountservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\controllers\\achcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\controllers\\achcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\service\\internaltransferservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\service\\internaltransferservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\controllers\\internalcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\controllers\\internalcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\controllers\\helpercontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\controllers\\helpercontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.functions.aion.transactionstatusreport\\internaltransactionstatusreportfunction.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|solutionrelative:bluetape.functions.aion.transactionstatusreport\\internaltransactionstatusreportfunction.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.functions.aion.transactionstatusreport\\externaltransactionstatusreportfunction.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|solutionrelative:bluetape.functions.aion.transactionstatusreport\\externaltransactionstatusreportfunction.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.functions.aion.transactionstatusreport\\externaltransactionconsumer.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|solutionrelative:bluetape.functions.aion.transactionstatusreport\\externaltransactionconsumer.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.functions.aion.transactionstatusreport\\dailyachprocessingfunction.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|solutionrelative:bluetape.functions.aion.transactionstatusreport\\dailyachprocessingfunction.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\service\\errornotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\service\\errornotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.application.tests\\services\\errornotificationservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|solutionrelative:bluetape.application.tests\\services\\errornotificationservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\appsettings.qa.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\appsettings.qa.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\controllers\\accountcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\controllers\\accountcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\middlewares\\exceptionmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\middlewares\\exceptionmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\middlewares\\loggingmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\middlewares\\loggingmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\appsettings.beta.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\appsettings.beta.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}|BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\buetape.aion.infrastructure\\errorcodes.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}|BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj|solutionrelative:buetape.aion.infrastructure\\errorcodes.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A25CE271-9BB9-4104-9B19-CD4285804EA7}|BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.domain\\constants\\aionfunctionconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A25CE271-9BB9-4104-9B19-CD4285804EA7}|BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj|solutionrelative:bluetape.aion.domain\\constants\\aionfunctionconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A25CE271-9BB9-4104-9B19-CD4285804EA7}|BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.domain\\constants\\environmentconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A25CE271-9BB9-4104-9B19-CD4285804EA7}|BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj|solutionrelative:bluetape.aion.domain\\constants\\environmentconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A25CE271-9BB9-4104-9B19-CD4285804EA7}|BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.domain\\constants\\loggerconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A25CE271-9BB9-4104-9B19-CD4285804EA7}|BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj|solutionrelative:bluetape.aion.domain\\constants\\loggerconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}|BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\buetape.aion.infrastructure\\extensions\\decimalextension.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}|BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj|solutionrelative:buetape.aion.infrastructure\\extensions\\decimalextension.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}|BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\buetape.aion.infrastructure\\extensions\\paymentsubscriptionextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}|BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj|solutionrelative:buetape.aion.infrastructure\\extensions\\paymentsubscriptionextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}|BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\buetape.aion.infrastructure\\extensions\\variablenullexception.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}|BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj|solutionrelative:buetape.aion.infrastructure\\extensions\\variablenullexception.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.functions.aion.transactionstatusreport\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|solutionrelative:bluetape.functions.aion.transactionstatusreport\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}|BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\buetape.aion.infrastructure\\exceptions\\parameternotexistexception.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}|BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj|solutionrelative:buetape.aion.infrastructure\\exceptions\\parameternotexistexception.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}|BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\buetape.aion.infrastructure\\exceptions\\decryptexception.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}|BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj|solutionrelative:buetape.aion.infrastructure\\exceptions\\decryptexception.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}|BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\buetape.aion.infrastructure\\buetape.aion.infrastructure.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}|BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj|solutionrelative:buetape.aion.infrastructure\\buetape.aion.infrastructure.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\constants\\userconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\constants\\userconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\constants\\keyvaultkeysconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\constants\\keyvaultkeysconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\constants\\cryptoconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\constants\\cryptoconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\constants\\routeconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\constants\\routeconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\constants\\authenticationconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\constants\\authenticationconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\constants\\configconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\constants\\configconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.mongodb\\repositories\\companyrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|solutionrelative:bluetape.aion.dataaccess.mongodb\\repositories\\companyrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.application.tests\\services\\reportservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|solutionrelative:bluetape.application.tests\\services\\reportservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\abstractions\\ierrornotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\abstractions\\ierrornotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.functions.aion.transactionstatusreport\\host.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|solutionrelative:bluetape.functions.aion.transactionstatusreport\\host.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\extensions\\fluentvalidationextension.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\extensions\\fluentvalidationextension.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\service\\helperservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\service\\helperservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\extensions\\aionsettingsextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\extensions\\aionsettingsextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.mongodb\\repositories\\bankaccountrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|solutionrelative:bluetape.aion.dataaccess.mongodb\\repositories\\bankaccountrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.application.tests\\services\\companyservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|solutionrelative:bluetape.application.tests\\services\\companyservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.mongodb\\entities\\banksaccounts\\bankaccountaionsettingsentity.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|solutionrelative:bluetape.aion.dataaccess.mongodb\\entities\\banksaccounts\\bankaccountaionsettingsentity.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.mongodb\\entities\\banksaccounts\\bankaccountnumberentity.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|solutionrelative:bluetape.aion.dataaccess.mongodb\\entities\\banksaccounts\\bankaccountnumberentity.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.mongodb\\entities\\banksaccounts\\bankaccountentity.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|solutionrelative:bluetape.aion.dataaccess.mongodb\\entities\\banksaccounts\\bankaccountentity.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.mongodb\\entities\\companies\\companyentity.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|solutionrelative:bluetape.aion.dataaccess.mongodb\\entities\\companies\\companyentity.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.mongodb\\entities\\companies\\companyaionsettingsentity.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|solutionrelative:bluetape.aion.dataaccess.mongodb\\entities\\companies\\companyaionsettingsentity.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\models\\login\\aioncredentialsecretsmodel.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\models\\login\\aioncredentialsecretsmodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\aioncredentialsmanager.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\aioncredentialsmanager.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\c59c6e0a029705cef818236b76b5baf2af9cc664d8358ac2c57af0271a1c7d11\\ObjectGraphTestExtensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\appsettings.prod.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\appsettings.prod.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\appsettings.dev.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\appsettings.dev.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.application.tests\\globalusings.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|solutionrelative:bluetape.application.tests\\globalusings.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A25CE271-9BB9-4104-9B19-CD4285804EA7}|BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.domain\\dtos\\bankaccount\\bankaccountaionsettingsdto.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A25CE271-9BB9-4104-9B19-CD4285804EA7}|BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj|solutionrelative:bluetape.aion.domain\\dtos\\bankaccount\\bankaccountaionsettingsdto.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\swagger\\paymentsubscriptionheaderfilter.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\swagger\\paymentsubscriptionheaderfilter.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\48e6f0d706c743cf820a22820b7abba41800\\c0\\e7f5f2eb\\PaymentSubscriptionType.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\models\\internaltransfer\\baseinternalaccountdetails.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\models\\internaltransfer\\baseinternalaccountdetails.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\models\\achtransfer\\basegetachtransfers.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\models\\achtransfer\\basegetachtransfers.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\models\\transactions\\transactionlistobj.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\models\\transactions\\transactionlistobj.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\models\\baseaionpageableresponsemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\models\\baseaionpageableresponsemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\models\\baseaionresponsemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\models\\baseaionresponsemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\models\\transactions\\gettransactionsresponsemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\models\\transactions\\gettransactionsresponsemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\models\\transactions\\gettransactionsrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\models\\transactions\\gettransactionsrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\models\\createachtransfer\\createachtransferrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\models\\createachtransfer\\createachtransferrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\models\\createachtransfer\\achobjectrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\models\\createachtransfer\\achobjectrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\bluetape.aion.dataaccess.external.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\bluetape.aion.dataaccess.external.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\models\\createcounterparty\\counterpartyresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\models\\createcounterparty\\counterpartyresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\models\\createcounterparty\\counterpartyobjectresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\models\\createcounterparty\\counterpartyobjectresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\models\\ach\\pull\\originator.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\models\\ach\\pull\\originator.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\abstractions\\iaionhttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\abstractions\\iaionhttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.application.tests\\services\\transactionservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|solutionrelative:bluetape.application.tests\\services\\transactionservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.application.tests\\services\\internaltransferservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|solutionrelative:bluetape.application.tests\\services\\internaltransferservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.mongodb\\repositories\\aionloggingrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|solutionrelative:bluetape.aion.dataaccess.mongodb\\repositories\\aionloggingrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.mongodb\\repositories\\userrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|solutionrelative:bluetape.aion.dataaccess.mongodb\\repositories\\userrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.functions.aion.transactionstatusreport\\appsettings.prod.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|solutionrelative:bluetape.functions.aion.transactionstatusreport\\appsettings.prod.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\constants\\pathconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\constants\\pathconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\models\\achtransfer\\response\\getachtransferresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\models\\achtransfer\\response\\getachtransferresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\models\\achtransfer\\getachtransfersrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\models\\achtransfer\\getachtransfersrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\models\\accounts\\getaccountsresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\models\\accounts\\getaccountsresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\models\\accounts\\getaccountsrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\models\\accounts\\getaccountsrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\models\\createcounterparty\\counterpartyrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\models\\createcounterparty\\counterpartyrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\extensions\\servicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\extensions\\servicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.mongodb\\repositories\\userrolerepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|solutionrelative:bluetape.aion.dataaccess.mongodb\\repositories\\userrolerepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.functions.aion.transactionstatusreport\\appsettings.qa.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|solutionrelative:bluetape.functions.aion.transactionstatusreport\\appsettings.qa.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\enums\\counterpartytype.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\enums\\counterpartytype.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\abstractions\\iachservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\abstractions\\iachservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.application.tests\\services\\bankaccountservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|solutionrelative:bluetape.application.tests\\services\\bankaccountservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\models\\ach\\pull\\response\\originatorresponsemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\models\\ach\\pull\\response\\originatorresponsemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.application.tests\\bluetape.application.tests.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|solutionrelative:bluetape.application.tests\\bluetape.application.tests.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{A25CE271-9BB9-4104-9B19-CD4285804EA7}|BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.domain\\extensions\\environmentstringextension.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{A25CE271-9BB9-4104-9B19-CD4285804EA7}|BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj|solutionrelative:bluetape.aion.domain\\extensions\\environmentstringextension.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.functions.aion.transactionstatusreport\\appsettings.dev.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|solutionrelative:bluetape.functions.aion.transactionstatusreport\\appsettings.dev.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.functions.aion.transactionstatusreport\\appsettings.beta.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|solutionrelative:bluetape.functions.aion.transactionstatusreport\\appsettings.beta.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.functions.aion.transactionstatusreport\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|solutionrelative:bluetape.functions.aion.transactionstatusreport\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.functions.aion.transactionstatusreport\\local.settings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|solutionrelative:bluetape.functions.aion.transactionstatusreport\\local.settings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\abstractions\\iaionmemorycache.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\abstractions\\iaionmemorycache.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.api\\validators\\ach\\createachpullmodelvalidator.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F0AFA73D-76F3-4C43-AF99-027BC85488E6}|BlueTape.Aion.API\\BlueTape.Aion.API.csproj|solutionrelative:bluetape.aion.api\\validators\\ach\\createachpullmodelvalidator.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.functions.aion.transactionstatusreport\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|solutionrelative:bluetape.functions.aion.transactionstatusreport\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\models\\ach\\pull\\response\\createachresponsemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\models\\ach\\pull\\response\\createachresponsemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\models\\ach\\pull\\response\\originalresponsemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\models\\ach\\pull\\response\\originalresponsemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\models\\internaltransfer\\createinternal.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\models\\internaltransfer\\createinternal.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.functions.aion.transactionstatusreport\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|solutionrelative:bluetape.functions.aion.transactionstatusreport\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}|BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\buetape.aion.infrastructure\\options\\aionreportoptions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}|BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj|solutionrelative:buetape.aion.infrastructure\\options\\aionreportoptions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\bluetape.aion.application.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\bluetape.aion.application.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\abstractions\\itransactionservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\abstractions\\itransactionservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\models\\ach\\pull\\response\\receiverresponsemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\models\\ach\\pull\\response\\receiverresponsemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\abstractions\\ireportservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\abstractions\\ireportservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\constants\\aionerrorcodes.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\constants\\aionerrorcodes.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\TFSTemp\\vctmp52464_351481.TransactionService.00000000.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.external\\models\\createcounterparty\\createcounterpartyrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{30DB309D-FC02-4E8C-8882-05FC39BE874F}|BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj|solutionrelative:bluetape.aion.dataaccess.external\\models\\createcounterparty\\createcounterpartyrequest.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\abstractions\\ibankaccountservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\abstractions\\ibankaccountservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\abstractions\\icompanyservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\abstractions\\icompanyservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.functions.aion.transactionstatusreport\\.gitignore||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{7727D902-4A57-4FA2-8B15-AF65172F214D}|BlueTape.Functions.Aion.TransactionStatusReport\\BlueTape.Functions.Aion.TransactionStatusReport.csproj|solutionrelative:bluetape.functions.aion.transactionstatusreport\\.gitignore||{3B902123-F8A7-4915-9F01-361F908088D0}"}, {"AbsoluteMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.application.tests\\helper\\mockhelper.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}|BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj|solutionrelative:bluetape.application.tests\\helper\\mockhelper.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A25CE271-9BB9-4104-9B19-CD4285804EA7}|BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.domain\\bluetape.aion.domain.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{A25CE271-9BB9-4104-9B19-CD4285804EA7}|BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj|solutionrelative:bluetape.aion.domain\\bluetape.aion.domain.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.mongodb\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|solutionrelative:bluetape.aion.dataaccess.mongodb\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\abstractions\\iinternaltransferservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\abstractions\\iinternaltransferservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.application\\abstractions\\ihelperservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{9966E9B8-4583-4F6D-9C19-F053E58B719F}|BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj|solutionrelative:bluetape.aion.application\\abstractions\\ihelperservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.mongodb\\abstractions\\icompanyrepository.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|solutionrelative:bluetape.aion.dataaccess.mongodb\\abstractions\\icompanyrepository.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\aion-integration\\src\\bluetape.aion.dataaccess.mongodb\\abstractions\\ibankaccountrepository.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{517D535F-7C3A-49DD-A01A-E607CEB7EC77}|BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj|solutionrelative:bluetape.aion.dataaccess.mongodb\\abstractions\\ibankaccountrepository.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\7bca74a4b600470d83f6f6d102b1ac847000\\9c\\150108d2\\TransactionEntity.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 8, "SelectedChildIndex": 8, "Children": [{"$type": "Bookmark", "Name": "ST:133:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:134:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:135:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "AchService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Service\\AchService.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Service\\AchService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Service\\AchService.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Service\\AchService.cs", "ViewState": "AgIAAKcCAAAAAAAAAADwv6wCAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-21T14:07:53.195Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "TransactionService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Service\\TransactionService.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Service\\TransactionService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Service\\TransactionService.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Service\\TransactionService.cs", "ViewState": "AgIAAEkAAAAAAAAAAADwv1UAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T12:36:17.826Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "AionHttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\AionHttpClient.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\AionHttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\AionHttpClient.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\AionHttpClient.cs", "ViewState": "AgIAAJQAAAAAAAAAAAAgwJ4AAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-06T14:58:33.209Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "TransactionController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Controllers\\TransactionController.cs", "RelativeDocumentMoniker": "BlueTape.Aion.API\\Controllers\\TransactionController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Controllers\\TransactionController.cs", "RelativeToolTip": "BlueTape.Aion.API\\Controllers\\TransactionController.cs", "ViewState": "AgIAAFIAAAAAAAAAAAAqwHoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-07T14:44:03.543Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "CreateAch.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Models\\Ach\\Pull\\CreateAch.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Models\\Ach\\Pull\\CreateAch.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Models\\Ach\\Pull\\CreateAch.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Models\\Ach\\Pull\\CreateAch.cs", "ViewState": "AgIAABAAAAAAAAAAAIAywCAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-22T14:09:17.322Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "AchServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\Services\\AchServiceTests.cs", "RelativeDocumentMoniker": "BlueTape.Application.Tests\\Services\\AchServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\Services\\AchServiceTests.cs", "RelativeToolTip": "BlueTape.Application.Tests\\Services\\AchServiceTests.cs", "ViewState": "AgIAAC8BAAAAAAAAAAAAAEIBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-14T09:31:51.777Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "AionConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Constants\\AionConstants.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Constants\\AionConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Constants\\AionConstants.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Constants\\AionConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-19T09:02:08.55Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "ReportService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Service\\ReportService.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Service\\ReportService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Service\\ReportService.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Service\\ReportService.cs", "ViewState": "AgIAAF8AAAAAAAAAAAAAAHEAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-13T12:20:11.959Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "InternalTransactionConsumer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\InternalTransactionConsumer.cs", "RelativeDocumentMoniker": "BlueTape.Functions.Aion.TransactionStatusReport\\InternalTransactionConsumer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\InternalTransactionConsumer.cs", "RelativeToolTip": "BlueTape.Functions.Aion.TransactionStatusReport\\InternalTransactionConsumer.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAqwDsAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-13T12:23:26.058Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "CompanyService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Service\\CompanyService.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Service\\CompanyService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Service\\CompanyService.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Service\\CompanyService.cs", "ViewState": "AgIAAD4AAAAAAAAAAADgv08AAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-28T07:35:18.79Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "DailyAchProcessingService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Service\\DailyAchProcessingService.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Service\\DailyAchProcessingService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Service\\DailyAchProcessingService.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Service\\DailyAchProcessingService.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAiwE4AAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T08:26:59.386Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "BankAccountService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Service\\BankAccountService.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Service\\BankAccountService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Service\\BankAccountService.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Service\\BankAccountService.cs", "ViewState": "AgIAAKUAAAAAAAAAAAAWwC0AAABIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-29T11:38:06.976Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "AchController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Controllers\\AchController.cs", "RelativeDocumentMoniker": "BlueTape.Aion.API\\Controllers\\AchController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Controllers\\AchController.cs", "RelativeToolTip": "BlueTape.Aion.API\\Controllers\\AchController.cs", "ViewState": "AgIAAFAAAAAAAAAAAAAUwFAAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-20T08:17:49.318Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "InternalTransferService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Service\\InternalTransferService.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Service\\InternalTransferService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Service\\InternalTransferService.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Service\\InternalTransferService.cs", "ViewState": "AgIAACkAAAAAAAAAAAAAADsAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-13T09:52:50.565Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "DependencyRegistrar.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\DI\\DependencyRegistrar.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\DI\\DependencyRegistrar.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\DI\\DependencyRegistrar.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\DI\\DependencyRegistrar.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAACYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T08:50:58.482Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "DependencyRegistrar.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\DI\\DependencyRegistrar.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\DI\\DependencyRegistrar.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\DI\\DependencyRegistrar.cs", "RelativeToolTip": "BlueTape.Aion.Application\\DI\\DependencyRegistrar.cs", "ViewState": "AgIAABEAAAAAAAAAAAAqwBoAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-23T09:24:48.538Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "InternalController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Controllers\\InternalController.cs", "RelativeDocumentMoniker": "BlueTape.Aion.API\\Controllers\\InternalController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Controllers\\InternalController.cs", "RelativeToolTip": "BlueTape.Aion.API\\Controllers\\InternalController.cs", "ViewState": "AgIAACEAAAAAAAAAAAAQwC4AAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-23T13:02:38.261Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "HelperController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Controllers\\HelperController.cs", "RelativeDocumentMoniker": "BlueTape.Aion.API\\Controllers\\HelperController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Controllers\\HelperController.cs", "RelativeToolTip": "BlueTape.Aion.API\\Controllers\\HelperController.cs", "ViewState": "AgIAAC0AAAAAAAAAAAAAwD4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-23T13:02:39.706Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "InternalTransactionStatusReportFunction.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\InternalTransactionStatusReportFunction.cs", "RelativeDocumentMoniker": "BlueTape.Functions.Aion.TransactionStatusReport\\InternalTransactionStatusReportFunction.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\InternalTransactionStatusReportFunction.cs", "RelativeToolTip": "BlueTape.Functions.Aion.TransactionStatusReport\\InternalTransactionStatusReportFunction.cs", "ViewState": "AgIAACUAAAAAAAAAAAAgwDEAAABiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-13T15:23:32.997Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "ExternalTransactionStatusReportFunction.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\ExternalTransactionStatusReportFunction.cs", "RelativeDocumentMoniker": "BlueTape.Functions.Aion.TransactionStatusReport\\ExternalTransactionStatusReportFunction.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\ExternalTransactionStatusReportFunction.cs", "RelativeToolTip": "BlueTape.Functions.Aion.TransactionStatusReport\\ExternalTransactionStatusReportFunction.cs", "ViewState": "AgIAABAAAAAAAAAAAAAkwCUAAABdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-06T07:42:59.205Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "ExternalTransactionConsumer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\ExternalTransactionConsumer.cs", "RelativeDocumentMoniker": "BlueTape.Functions.Aion.TransactionStatusReport\\ExternalTransactionConsumer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\ExternalTransactionConsumer.cs", "RelativeToolTip": "BlueTape.Functions.Aion.TransactionStatusReport\\ExternalTransactionConsumer.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAjwDkAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-06T07:42:56.782Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "DailyAchProcessingFunction.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\DailyAchProcessingFunction.cs", "RelativeDocumentMoniker": "BlueTape.Functions.Aion.TransactionStatusReport\\DailyAchProcessingFunction.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\DailyAchProcessingFunction.cs", "RelativeToolTip": "BlueTape.Functions.Aion.TransactionStatusReport\\DailyAchProcessingFunction.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAwBkAAABoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-22T17:21:04.112Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "ErrorNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Service\\ErrorNotificationService.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Service\\ErrorNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Service\\ErrorNotificationService.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Service\\ErrorNotificationService.cs", "ViewState": "AgIAADgAAAAAAAAAAAAowCoAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-18T10:27:48.277Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "ErrorNotificationServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\Services\\ErrorNotificationServiceTests.cs", "RelativeDocumentMoniker": "BlueTape.Application.Tests\\Services\\ErrorNotificationServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\Services\\ErrorNotificationServiceTests.cs", "RelativeToolTip": "BlueTape.Application.Tests\\Services\\ErrorNotificationServiceTests.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAQwBoAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-13T10:13:29.668Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "appsettings.qa.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\appsettings.qa.json", "RelativeDocumentMoniker": "BlueTape.Aion.API\\appsettings.qa.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\appsettings.qa.json", "RelativeToolTip": "BlueTape.Aion.API\\appsettings.qa.json", "ViewState": "AgIAAAYAAAAAAAAAAAAAABYAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-05-23T13:24:28.559Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\appsettings.json", "RelativeDocumentMoniker": "BlueTape.Aion.API\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\appsettings.json", "RelativeToolTip": "BlueTape.Aion.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-12-06T12:20:18.204Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "AccountController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Controllers\\AccountController.cs", "RelativeDocumentMoniker": "BlueTape.Aion.API\\Controllers\\AccountController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Controllers\\AccountController.cs", "RelativeToolTip": "BlueTape.Aion.API\\Controllers\\AccountController.cs", "ViewState": "AgIAACQAAAAAAAAAAAASwDIAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-11T07:58:07.511Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "ExceptionMiddleware.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Middlewares\\ExceptionMiddleware.cs", "RelativeDocumentMoniker": "BlueTape.Aion.API\\Middlewares\\ExceptionMiddleware.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Middlewares\\ExceptionMiddleware.cs", "RelativeToolTip": "BlueTape.Aion.API\\Middlewares\\ExceptionMiddleware.cs", "ViewState": "AgIAACcAAAAAAAAAAAAQwDkAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T07:45:17.916Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "LoggingMiddleware.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Middlewares\\LoggingMiddleware.cs", "RelativeDocumentMoniker": "BlueTape.Aion.API\\Middlewares\\LoggingMiddleware.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Middlewares\\LoggingMiddleware.cs", "RelativeToolTip": "BlueTape.Aion.API\\Middlewares\\LoggingMiddleware.cs", "ViewState": "AgIAABMAAAAAAAAAAAAmwCUAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-08-26T09:08:40.916Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "appsettings.beta.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\appsettings.beta.json", "RelativeDocumentMoniker": "BlueTape.Aion.API\\appsettings.beta.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\appsettings.beta.json", "RelativeToolTip": "BlueTape.Aion.API\\appsettings.beta.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAADAAAABGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-12-06T12:20:21.048Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "ErrorCodes.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\ErrorCodes.cs", "RelativeDocumentMoniker": "BueTape.Aion.Infrastructure\\ErrorCodes.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\ErrorCodes.cs", "RelativeToolTip": "BueTape.Aion.Infrastructure\\ErrorCodes.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T09:58:12.288Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "LoggerConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\Constants\\LoggerConstants.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Domain\\Constants\\LoggerConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\Constants\\LoggerConstants.cs", "RelativeToolTip": "BlueTape.Aion.Domain\\Constants\\LoggerConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-18T10:01:20.666Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "AionFunctionConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\Constants\\AionFunctionConstants.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Domain\\Constants\\AionFunctionConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\Constants\\AionFunctionConstants.cs", "RelativeToolTip": "BlueTape.Aion.Domain\\Constants\\AionFunctionConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-18T10:01:17.247Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "EnvironmentConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\Constants\\EnvironmentConstants.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Domain\\Constants\\EnvironmentConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\Constants\\EnvironmentConstants.cs", "RelativeToolTip": "BlueTape.Aion.Domain\\Constants\\EnvironmentConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-18T10:01:19.578Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "VariableNullException.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\Extensions\\VariableNullException.cs", "RelativeDocumentMoniker": "BueTape.Aion.Infrastructure\\Extensions\\VariableNullException.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\Extensions\\VariableNullException.cs", "RelativeToolTip": "BueTape.Aion.Infrastructure\\Extensions\\VariableNullException.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T08:32:42.195Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "DecimalExtension.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\Extensions\\DecimalExtension.cs", "RelativeDocumentMoniker": "BueTape.Aion.Infrastructure\\Extensions\\DecimalExtension.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\Extensions\\DecimalExtension.cs", "RelativeToolTip": "BueTape.Aion.Infrastructure\\Extensions\\DecimalExtension.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T08:18:38.504Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "PaymentSubscriptionExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\Extensions\\PaymentSubscriptionExtensions.cs", "RelativeDocumentMoniker": "BueTape.Aion.Infrastructure\\Extensions\\PaymentSubscriptionExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\Extensions\\PaymentSubscriptionExtensions.cs", "RelativeToolTip": "BueTape.Aion.Infrastructure\\Extensions\\PaymentSubscriptionExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-17T12:50:20.548Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "DeCryptException.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\Exceptions\\DeCryptException.cs", "RelativeDocumentMoniker": "BueTape.Aion.Infrastructure\\Exceptions\\DeCryptException.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\Exceptions\\DeCryptException.cs", "RelativeToolTip": "BueTape.Aion.Infrastructure\\Exceptions\\DeCryptException.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T09:57:59.207Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "CryptoConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Constants\\CryptoConstants.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Constants\\CryptoConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Constants\\CryptoConstants.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Constants\\CryptoConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-23T13:02:58.326Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\Program.cs", "RelativeDocumentMoniker": "BlueTape.Functions.Aion.TransactionStatusReport\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\Program.cs", "RelativeToolTip": "BlueTape.Functions.Aion.TransactionStatusReport\\Program.cs", "ViewState": "AgIAAEEAAAAAAAAAAAAiwDoAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-14T13:04:16.78Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "ParameterNotExistException.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\Exceptions\\ParameterNotExistException.cs", "RelativeDocumentMoniker": "BueTape.Aion.Infrastructure\\Exceptions\\ParameterNotExistException.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\Exceptions\\ParameterNotExistException.cs", "RelativeToolTip": "BueTape.Aion.Infrastructure\\Exceptions\\ParameterNotExistException.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T09:57:59.873Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "RouteConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Constants\\RouteConstants.cs", "RelativeDocumentMoniker": "BlueTape.Aion.API\\Constants\\RouteConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Constants\\RouteConstants.cs", "RelativeToolTip": "BlueTape.Aion.API\\Constants\\RouteConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-23T13:02:52.999Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "AuthenticationConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Constants\\AuthenticationConstants.cs", "RelativeDocumentMoniker": "BlueTape.Aion.API\\Constants\\AuthenticationConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Constants\\AuthenticationConstants.cs", "RelativeToolTip": "BlueTape.Aion.API\\Constants\\AuthenticationConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-23T13:02:51.555Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "ConfigConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Constants\\ConfigConstants.cs", "RelativeDocumentMoniker": "BlueTape.Aion.API\\Constants\\ConfigConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Constants\\ConfigConstants.cs", "RelativeToolTip": "BlueTape.Aion.API\\Constants\\ConfigConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-23T13:02:52.214Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "KeyVaultKeysConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Constants\\KeyVaultKeysConstants.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Constants\\KeyVaultKeysConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Constants\\KeyVaultKeysConstants.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Constants\\KeyVaultKeysConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-23T13:03:01.788Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "UserConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Constants\\UserConstants.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Constants\\UserConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Constants\\UserConstants.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Constants\\UserConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-23T13:02:31.993Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "BueTape.Aion.Infrastructure.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj", "RelativeDocumentMoniker": "BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj", "RelativeToolTip": "BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2024-05-23T09:25:09.612Z"}, {"$type": "Document", "DocumentIndex": 135, "Title": "TransactionEntity.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\7bca74a4b600470d83f6f6d102b1ac847000\\9c\\150108d2\\TransactionEntity.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\7bca74a4b600470d83f6f6d102b1ac847000\\9c\\150108d2\\TransactionEntity.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\7bca74a4b600470d83f6f6d102b1ac847000\\9c\\150108d2\\TransactionEntity.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\7bca74a4b600470d83f6f6d102b1ac847000\\9c\\150108d2\\TransactionEntity.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-29T07:34:27.441Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "CompanyRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Repositories\\CompanyRepository.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.MongoDB\\Repositories\\CompanyRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Repositories\\CompanyRepository.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.MongoDB\\Repositories\\CompanyRepository.cs", "ViewState": "AgIAADUAAAAAAAAAAAAwwDoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-30T18:03:37.3Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "BlueTape.Aion.API\\Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Properties\\launchSettings.json", "RelativeToolTip": "BlueTape.Aion.API\\Properties\\launchSettings.json", "ViewState": "AgIAACQAAAAAAAAAAAAiwDcAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-12-08T20:21:40.564Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "ReportServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\Services\\ReportServiceTests.cs", "RelativeDocumentMoniker": "BlueTape.Application.Tests\\Services\\ReportServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\Services\\ReportServiceTests.cs", "RelativeToolTip": "BlueTape.Application.Tests\\Services\\ReportServiceTests.cs", "ViewState": "AgIAAGQAAAAAAAAAAADgv3gAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-29T08:33:02.494Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "IErrorNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Abstractions\\IErrorNotificationService.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Abstractions\\IErrorNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Abstractions\\IErrorNotificationService.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Abstractions\\IErrorNotificationService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAgAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-18T10:27:44.711Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "host.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\host.json", "RelativeDocumentMoniker": "BlueTape.Functions.Aion.TransactionStatusReport\\host.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\host.json", "RelativeToolTip": "BlueTape.Functions.Aion.TransactionStatusReport\\host.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-06-17T07:19:18.202Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "FluentValidationExtension.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Extensions\\FluentValidationExtension.cs", "RelativeDocumentMoniker": "BlueTape.Aion.API\\Extensions\\FluentValidationExtension.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Extensions\\FluentValidationExtension.cs", "RelativeToolTip": "BlueTape.Aion.API\\Extensions\\FluentValidationExtension.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAIwBYAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-07T15:37:23.091Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "BankAccountRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Repositories\\BankAccountRepository.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.MongoDB\\Repositories\\BankAccountRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Repositories\\BankAccountRepository.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.MongoDB\\Repositories\\BankAccountRepository.cs", "ViewState": "AgIAACUAAAAAAAAAAAAYwDQAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-28T19:55:54.14Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "AionSettingsExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Extensions\\AionSettingsExtensions.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Extensions\\AionSettingsExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Extensions\\AionSettingsExtensions.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Extensions\\AionSettingsExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAABDAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T11:42:18.323Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "HelperService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Service\\HelperService.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Service\\HelperService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Service\\HelperService.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Service\\HelperService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-07-10T12:12:00.729Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 58, "Title": "BankAccountNumberEntity.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Entities\\BanksAccounts\\BankAccountNumberEntity.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.MongoDB\\Entities\\BanksAccounts\\BankAccountNumberEntity.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Entities\\BanksAccounts\\BankAccountNumberEntity.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.MongoDB\\Entities\\BanksAccounts\\BankAccountNumberEntity.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-28T19:55:27.759Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "CompanyServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\Services\\CompanyServiceTests.cs", "RelativeDocumentMoniker": "BlueTape.Application.Tests\\Services\\CompanyServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\Services\\CompanyServiceTests.cs", "RelativeToolTip": "BlueTape.Application.Tests\\Services\\CompanyServiceTests.cs", "ViewState": "AgIAAE0AAAAAAAAAAAAYwFwAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-07-26T14:08:30.098Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "BankAccountAionSettingsEntity.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Entities\\BanksAccounts\\BankAccountAionSettingsEntity.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.MongoDB\\Entities\\BanksAccounts\\BankAccountAionSettingsEntity.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Entities\\BanksAccounts\\BankAccountAionSettingsEntity.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.MongoDB\\Entities\\BanksAccounts\\BankAccountAionSettingsEntity.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-28T19:55:30.987Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "BankAccountEntity.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Entities\\BanksAccounts\\BankAccountEntity.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.MongoDB\\Entities\\BanksAccounts\\BankAccountEntity.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Entities\\BanksAccounts\\BankAccountEntity.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.MongoDB\\Entities\\BanksAccounts\\BankAccountEntity.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAQwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-28T19:55:22.069Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "CompanyEntity.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Entities\\Companies\\CompanyEntity.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.MongoDB\\Entities\\Companies\\CompanyEntity.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Entities\\Companies\\CompanyEntity.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.MongoDB\\Entities\\Companies\\CompanyEntity.cs", "ViewState": "AgIAABoAAAAAAAAAAAAAAB8AAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-28T19:54:47.765Z"}, {"$type": "Document", "DocumentIndex": 62, "Title": "AionCredentialSecretsModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\Login\\AionCredentialSecretsModel.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Models\\Login\\AionCredentialSecretsModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\Login\\AionCredentialSecretsModel.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Models\\Login\\AionCredentialSecretsModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-28T19:36:38.279Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "CompanyAionSettingsEntity.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Entities\\Companies\\CompanyAionSettingsEntity.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.MongoDB\\Entities\\Companies\\CompanyAionSettingsEntity.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Entities\\Companies\\CompanyAionSettingsEntity.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.MongoDB\\Entities\\Companies\\CompanyAionSettingsEntity.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-14T08:34:00.793Z"}, {"$type": "Document", "DocumentIndex": 64, "Title": "ObjectGraphTestExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\c59c6e0a029705cef818236b76b5baf2af9cc664d8358ac2c57af0271a1c7d11\\ObjectGraphTestExtensions.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\c59c6e0a029705cef818236b76b5baf2af9cc664d8358ac2c57af0271a1c7d11\\ObjectGraphTestExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\c59c6e0a029705cef818236b76b5baf2af9cc664d8358ac2c57af0271a1c7d11\\ObjectGraphTestExtensions.cs [Read Only]", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\c59c6e0a029705cef818236b76b5baf2af9cc664d8358ac2c57af0271a1c7d11\\ObjectGraphTestExtensions.cs [Read Only]", "ViewState": "AgIAAKoAAAAAAAAAAAAEwLgAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-28T16:07:51.059Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 63, "Title": "AionCredentialsManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\AionCredentialsManager.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\AionCredentialsManager.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\AionCredentialsManager.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\AionCredentialsManager.cs", "ViewState": "AgIAABYAAAAAAAAAAAAYwCkAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-19T13:49:48.895Z"}, {"$type": "Document", "DocumentIndex": 65, "Title": "appsettings.prod.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\appsettings.prod.json", "RelativeDocumentMoniker": "BlueTape.Aion.API\\appsettings.prod.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\appsettings.prod.json", "RelativeToolTip": "BlueTape.Aion.API\\appsettings.prod.json", "ViewState": "AgIAAC0AAAAAAAAAAAAAAEgAAABCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-12-06T12:20:24.03Z"}, {"$type": "Document", "DocumentIndex": 66, "Title": "appsettings.dev.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\appsettings.dev.json", "RelativeDocumentMoniker": "BlueTape.Aion.API\\appsettings.dev.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\appsettings.dev.json", "RelativeToolTip": "BlueTape.Aion.API\\appsettings.dev.json", "ViewState": "AgIAACoAAAAAAAAAAADwv0UAAABCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-06-17T10:43:06.017Z"}, {"$type": "Document", "DocumentIndex": 67, "Title": "GlobalUsings.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\GlobalUsings.cs", "RelativeDocumentMoniker": "BlueTape.Application.Tests\\GlobalUsings.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\GlobalUsings.cs", "RelativeToolTip": "BlueTape.Application.Tests\\GlobalUsings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-23T13:02:34.945Z"}, {"$type": "Document", "DocumentIndex": 68, "Title": "BankAccountAionSettingsDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\DTOs\\BankAccount\\BankAccountAionSettingsDto.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Domain\\DTOs\\BankAccount\\BankAccountAionSettingsDto.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\DTOs\\BankAccount\\BankAccountAionSettingsDto.cs", "RelativeToolTip": "BlueTape.Aion.Domain\\DTOs\\BankAccount\\BankAccountAionSettingsDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-28T12:38:33.78Z"}, {"$type": "Document", "DocumentIndex": 69, "Title": "PaymentSubscriptionHeaderFilter.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Swagger\\PaymentSubscriptionHeaderFilter.cs", "RelativeDocumentMoniker": "BlueTape.Aion.API\\Swagger\\PaymentSubscriptionHeaderFilter.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Swagger\\PaymentSubscriptionHeaderFilter.cs", "RelativeToolTip": "BlueTape.Aion.API\\Swagger\\PaymentSubscriptionHeaderFilter.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAxwBcAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-28T12:33:40.176Z"}, {"$type": "Document", "DocumentIndex": 70, "Title": "PaymentSubscriptionType.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\48e6f0d706c743cf820a22820b7abba41800\\c0\\e7f5f2eb\\PaymentSubscriptionType.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\48e6f0d706c743cf820a22820b7abba41800\\c0\\e7f5f2eb\\PaymentSubscriptionType.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\48e6f0d706c743cf820a22820b7abba41800\\c0\\e7f5f2eb\\PaymentSubscriptionType.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\48e6f0d706c743cf820a22820b7abba41800\\c0\\e7f5f2eb\\PaymentSubscriptionType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-28T12:32:39.798Z"}, {"$type": "Document", "DocumentIndex": 71, "Title": "BaseInternalAccountDetails.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Models\\InternalTransfer\\BaseInternalAccountDetails.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Models\\InternalTransfer\\BaseInternalAccountDetails.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Models\\InternalTransfer\\BaseInternalAccountDetails.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Models\\InternalTransfer\\BaseInternalAccountDetails.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAYAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-12T12:12:52.627Z"}, {"$type": "Document", "DocumentIndex": 72, "Title": "BaseGetACHTransfers.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\AchTransfer\\BaseGetACHTransfers.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Models\\AchTransfer\\BaseGetACHTransfers.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\AchTransfer\\BaseGetACHTransfers.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Models\\AchTransfer\\BaseGetACHTransfers.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-29T14:11:24.245Z"}, {"$type": "Document", "DocumentIndex": 74, "Title": "BaseAionPageAbleResponseModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\BaseAionPageAbleResponseModel.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Models\\BaseAionPageAbleResponseModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\BaseAionPageAbleResponseModel.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Models\\BaseAionPageAbleResponseModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-08-21T12:53:52.224Z"}, {"$type": "Document", "DocumentIndex": 73, "Title": "TransactionListObj.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\Transactions\\TransactionListObj.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Models\\Transactions\\TransactionListObj.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\Transactions\\TransactionListObj.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Models\\Transactions\\TransactionListObj.cs", "ViewState": "AgIAAC4AAAAAAAAAAAAkwDkAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-12T12:12:38.542Z"}, {"$type": "Document", "DocumentIndex": 75, "Title": "BaseAionResponseModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\BaseAionResponseModel.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Models\\BaseAionResponseModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\BaseAionResponseModel.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Models\\BaseAionResponseModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-08-21T12:53:53.249Z"}, {"$type": "Document", "DocumentIndex": 76, "Title": "GetTransactionsResponseModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\Transactions\\GetTransactionsResponseModel.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Models\\Transactions\\GetTransactionsResponseModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\Transactions\\GetTransactionsResponseModel.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Models\\Transactions\\GetTransactionsResponseModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-07T11:46:31.519Z"}, {"$type": "Document", "DocumentIndex": 77, "Title": "GetTransactionsRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\Transactions\\GetTransactionsRequest.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Models\\Transactions\\GetTransactionsRequest.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\Transactions\\GetTransactionsRequest.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Models\\Transactions\\GetTransactionsRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-07T11:46:29.992Z"}, {"$type": "Document", "DocumentIndex": 78, "Title": "CreateAchTransferRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\CreateAchTransfer\\CreateAchTransferRequest.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Models\\CreateAchTransfer\\CreateAchTransferRequest.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\CreateAchTransfer\\CreateAchTransferRequest.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Models\\CreateAchTransfer\\CreateAchTransferRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-07T11:46:24.989Z"}, {"$type": "Document", "DocumentIndex": 79, "Title": "AchObjectRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\CreateAchTransfer\\AchObjectRequest.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Models\\CreateAchTransfer\\AchObjectRequest.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\CreateAchTransfer\\AchObjectRequest.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Models\\CreateAchTransfer\\AchObjectRequest.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAIwB4AAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-07-11T11:35:07.612Z"}, {"$type": "Document", "DocumentIndex": 81, "Title": "CounterpartyResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\CreateCounterparty\\CounterpartyResponse.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Models\\CreateCounterparty\\CounterpartyResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\CreateCounterparty\\CounterpartyResponse.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Models\\CreateCounterparty\\CounterpartyResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAkAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-29T14:14:15.298Z"}, {"$type": "Document", "DocumentIndex": 80, "Title": "BlueTape.Aion.DataAccess.External.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2024-05-23T09:25:05.638Z"}, {"$type": "Document", "DocumentIndex": 82, "Title": "CounterpartyObjectResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\CreateCounterparty\\CounterpartyObjectResponse.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Models\\CreateCounterparty\\CounterpartyObjectResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\CreateCounterparty\\CounterpartyObjectResponse.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Models\\CreateCounterparty\\CounterpartyObjectResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-07T10:43:18.394Z"}, {"$type": "Document", "DocumentIndex": 83, "Title": "Originator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Models\\Ach\\Pull\\Originator.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Models\\Ach\\Pull\\Originator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Models\\Ach\\Pull\\Originator.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Models\\Ach\\Pull\\Originator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-07T10:43:09.543Z"}, {"$type": "Document", "DocumentIndex": 85, "Title": "TransactionServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\Services\\TransactionServiceTests.cs", "RelativeDocumentMoniker": "BlueTape.Application.Tests\\Services\\TransactionServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\Services\\TransactionServiceTests.cs", "RelativeToolTip": "BlueTape.Application.Tests\\Services\\TransactionServiceTests.cs", "ViewState": "AgIAAKIAAAAAAAAAAAAMwLcAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-29T11:14:38.997Z"}, {"$type": "Document", "DocumentIndex": 84, "Title": "IAionHttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Abstractions\\IAionHttpClient.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Abstractions\\IAionHttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Abstractions\\IAionHttpClient.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Abstractions\\IAionHttpClient.cs", "ViewState": "AgIAAAsAAAAAAAAAAAASwBgAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-06T12:06:54.708Z"}, {"$type": "Document", "DocumentIndex": 86, "Title": "InternalTransferServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\Services\\InternalTransferServiceTests.cs", "RelativeDocumentMoniker": "BlueTape.Application.Tests\\Services\\InternalTransferServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\Services\\InternalTransferServiceTests.cs", "RelativeToolTip": "BlueTape.Application.Tests\\Services\\InternalTransferServiceTests.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAwKQAAABfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-07-26T14:02:40.093Z"}, {"$type": "Document", "DocumentIndex": 87, "Title": "AionLoggingRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Repositories\\AionLoggingRepository.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.MongoDB\\Repositories\\AionLoggingRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Repositories\\AionLoggingRepository.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.MongoDB\\Repositories\\AionLoggingRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAgAAABLAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-20T08:37:17.718Z"}, {"$type": "Document", "DocumentIndex": 134, "Title": "IBankAccountRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Abstractions\\IBankAccountRepository.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.MongoDB\\Abstractions\\IBankAccountRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Abstractions\\IBankAccountRepository.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.MongoDB\\Abstractions\\IBankAccountRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-29T18:10:04.517Z"}, {"$type": "Document", "DocumentIndex": 88, "Title": "UserRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Repositories\\UserRepository.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.MongoDB\\Repositories\\UserRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Repositories\\UserRepository.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.MongoDB\\Repositories\\UserRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-20T08:37:27.389Z"}, {"$type": "Document", "DocumentIndex": 89, "Title": "appsettings.prod.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.prod.json", "RelativeDocumentMoniker": "BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.prod.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.prod.json", "RelativeToolTip": "BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.prod.json", "ViewState": "AgIAAAsAAAAAAAAAAADwvxsAAABCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-07-24T15:29:12.457Z"}, {"$type": "Document", "DocumentIndex": 132, "Title": "IHelperService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Abstractions\\IHelperService.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Abstractions\\IHelperService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Abstractions\\IHelperService.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Abstractions\\IHelperService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-29T14:22:45.746Z"}, {"$type": "Document", "DocumentIndex": 133, "Title": "ICompanyRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Abstractions\\ICompanyRepository.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.MongoDB\\Abstractions\\ICompanyRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Abstractions\\ICompanyRepository.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.MongoDB\\Abstractions\\ICompanyRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-29T14:33:04.274Z"}, {"$type": "Document", "DocumentIndex": 90, "Title": "PathConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Constants\\PathConstants.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Constants\\PathConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Constants\\PathConstants.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Constants\\PathConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAABTAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-29T14:16:11.042Z"}, {"$type": "Document", "DocumentIndex": 91, "Title": "GetACHTransferResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\AchTransfer\\Response\\GetACHTransferResponse.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Models\\AchTransfer\\Response\\GetACHTransferResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\AchTransfer\\Response\\GetACHTransferResponse.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Models\\AchTransfer\\Response\\GetACHTransferResponse.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAUwAsAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-29T14:12:52.006Z"}, {"$type": "Document", "DocumentIndex": 92, "Title": "GetACHTransfersRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\AchTransfer\\GetACHTransfersRequest.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Models\\AchTransfer\\GetACHTransfersRequest.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\AchTransfer\\GetACHTransfersRequest.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Models\\AchTransfer\\GetACHTransfersRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-29T14:11:21.492Z"}, {"$type": "Document", "DocumentIndex": 93, "Title": "GetAccountsResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\Accounts\\GetAccountsResponse.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Models\\Accounts\\GetAccountsResponse.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\Accounts\\GetAccountsResponse.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Models\\Accounts\\GetAccountsResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-29T14:11:16.392Z"}, {"$type": "Document", "DocumentIndex": 94, "Title": "GetAccountsRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\Accounts\\GetAccountsRequest.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Models\\Accounts\\GetAccountsRequest.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\Accounts\\GetAccountsRequest.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Models\\Accounts\\GetAccountsRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-29T14:11:15.186Z"}, {"$type": "Document", "DocumentIndex": 95, "Title": "CounterpartyRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\CreateCounterparty\\CounterpartyRequest.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Models\\CreateCounterparty\\CounterpartyRequest.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\CreateCounterparty\\CounterpartyRequest.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Models\\CreateCounterparty\\CounterpartyRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-29T14:11:09.505Z"}, {"$type": "Document", "DocumentIndex": 97, "Title": "ServiceCollectionExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Extensions\\ServiceCollectionExtensions.cs", "RelativeDocumentMoniker": "BlueTape.Aion.API\\Extensions\\ServiceCollectionExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Extensions\\ServiceCollectionExtensions.cs", "RelativeToolTip": "BlueTape.Aion.API\\Extensions\\ServiceCollectionExtensions.cs", "ViewState": "AgIAAAQAAAAAAAAAAAAgwA8AAABSAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-17T12:59:09.044Z"}, {"$type": "Document", "DocumentIndex": 96, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Program.cs", "RelativeDocumentMoniker": "BlueTape.Aion.API\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Program.cs", "RelativeToolTip": "BlueTape.Aion.API\\Program.cs", "ViewState": "AgIAABEAAAAAAAAAAAAiwCgAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-07-26T11:55:25.518Z"}, {"$type": "Document", "DocumentIndex": 98, "Title": "UserRoleRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Repositories\\UserRoleRepository.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.MongoDB\\Repositories\\UserRoleRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\Repositories\\UserRoleRepository.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.MongoDB\\Repositories\\UserRoleRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-20T08:37:29.465Z"}, {"$type": "Document", "DocumentIndex": 99, "Title": "appsettings.qa.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.qa.json", "RelativeDocumentMoniker": "BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.qa.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.qa.json", "RelativeToolTip": "BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.qa.json", "ViewState": "AgIAAAsAAAAAAAAAAADwvygAAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-07-24T15:29:13.023Z"}, {"$type": "Document", "DocumentIndex": 100, "Title": "CounterPartyType.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Enums\\CounterPartyType.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Enums\\CounterPartyType.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Enums\\CounterPartyType.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Enums\\CounterPartyType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-23T13:17:23.037Z"}, {"$type": "Document", "DocumentIndex": 101, "Title": "IAchService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Abstractions\\IAchService.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Abstractions\\IAchService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Abstractions\\IAchService.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Abstractions\\IAchService.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAwwBoAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-07-11T11:51:33.136Z"}, {"$type": "Document", "DocumentIndex": 102, "Title": "BankAccountServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\Services\\BankAccountServiceTests.cs", "RelativeDocumentMoniker": "BlueTape.Application.Tests\\Services\\BankAccountServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\Services\\BankAccountServiceTests.cs", "RelativeToolTip": "BlueTape.Application.Tests\\Services\\BankAccountServiceTests.cs", "ViewState": "AgIAAE0AAAAAAAAAAAAawF4AAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-07-26T11:56:53.345Z"}, {"$type": "Document", "DocumentIndex": 103, "Title": "OriginatorResponseModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Models\\Ach\\Pull\\Response\\OriginatorResponseModel.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Models\\Ach\\Pull\\Response\\OriginatorResponseModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Models\\Ach\\Pull\\Response\\OriginatorResponseModel.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Models\\Ach\\Pull\\Response\\OriginatorResponseModel.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAewBIAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-19T13:31:04.805Z"}, {"$type": "Document", "DocumentIndex": 104, "Title": "BlueTape.Application.Tests.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj", "RelativeDocumentMoniker": "BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj", "RelativeToolTip": "BlueTape.Application.Tests\\BlueTape.Application.Tests.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-02-17T13:19:55.213Z"}, {"$type": "Document", "DocumentIndex": 106, "Title": "appsettings.dev.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.dev.json", "RelativeDocumentMoniker": "BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.dev.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.dev.json", "RelativeToolTip": "BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.dev.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-06-17T09:57:57.355Z"}, {"$type": "Document", "DocumentIndex": 107, "Title": "appsettings.beta.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.beta.json", "RelativeDocumentMoniker": "BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.beta.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.beta.json", "RelativeToolTip": "BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.beta.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-06-17T11:02:16.776Z"}, {"$type": "Document", "DocumentIndex": 105, "Title": "EnvironmentStringExtension.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\Extensions\\EnvironmentStringExtension.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Domain\\Extensions\\EnvironmentStringExtension.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\Extensions\\EnvironmentStringExtension.cs", "RelativeToolTip": "BlueTape.Aion.Domain\\Extensions\\EnvironmentStringExtension.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-04T15:14:09.393Z"}, {"$type": "Document", "DocumentIndex": 108, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.json", "RelativeDocumentMoniker": "BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.json", "RelativeToolTip": "BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-06-17T09:57:54.453Z"}, {"$type": "Document", "DocumentIndex": 109, "Title": "local.settings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\local.settings.json", "RelativeDocumentMoniker": "BlueTape.Functions.Aion.TransactionStatusReport\\local.settings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\local.settings.json", "RelativeToolTip": "BlueTape.Functions.Aion.TransactionStatusReport\\local.settings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-06-17T10:50:12.517Z"}, {"$type": "Document", "DocumentIndex": 110, "Title": "IAionMemoryCache.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Abstractions\\IAionMemoryCache.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Abstractions\\IAionMemoryCache.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Abstractions\\IAionMemoryCache.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Abstractions\\IAionMemoryCache.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAQAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-17T08:42:13.182Z"}, {"$type": "Document", "DocumentIndex": 111, "Title": "CreateAchPullModelValidator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Validators\\Ach\\CreateAchPullModelValidator.cs", "RelativeDocumentMoniker": "BlueTape.Aion.API\\Validators\\Ach\\CreateAchPullModelValidator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.API\\Validators\\Ach\\CreateAchPullModelValidator.cs", "RelativeToolTip": "BlueTape.Aion.API\\Validators\\Ach\\CreateAchPullModelValidator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-07T15:37:34.734Z"}, {"$type": "Document", "DocumentIndex": 112, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.Development.json", "RelativeDocumentMoniker": "BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.Development.json", "RelativeToolTip": "BlueTape.Functions.Aion.TransactionStatusReport\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-06-17T09:58:08.106Z"}, {"$type": "Document", "DocumentIndex": 113, "Title": "CreateAchResponseModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Models\\Ach\\Pull\\Response\\CreateAchResponseModel.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Models\\Ach\\Pull\\Response\\CreateAchResponseModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Models\\Ach\\Pull\\Response\\CreateAchResponseModel.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Models\\Ach\\Pull\\Response\\CreateAchResponseModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-12T12:12:59.388Z"}, {"$type": "Document", "DocumentIndex": 114, "Title": "OriginalResponseModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Models\\Ach\\Pull\\Response\\OriginalResponseModel.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Models\\Ach\\Pull\\Response\\OriginalResponseModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Models\\Ach\\Pull\\Response\\OriginalResponseModel.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Models\\Ach\\Pull\\Response\\OriginalResponseModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-12T12:13:00.453Z"}, {"$type": "Document", "DocumentIndex": 115, "Title": "CreateInternal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Models\\InternalTransfer\\CreateInternal.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Models\\InternalTransfer\\CreateInternal.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Models\\InternalTransfer\\CreateInternal.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Models\\InternalTransfer\\CreateInternal.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-12T12:12:51.802Z"}, {"$type": "Document", "DocumentIndex": 116, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "BlueTape.Functions.Aion.TransactionStatusReport\\Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\Properties\\launchSettings.json", "RelativeToolTip": "BlueTape.Functions.Aion.TransactionStatusReport\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAABLAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-06-17T09:38:27.942Z"}, {"$type": "Document", "DocumentIndex": 117, "Title": "AionReportOptions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\Options\\AionReportOptions.cs", "RelativeDocumentMoniker": "BueTape.Aion.Infrastructure\\Options\\AionReportOptions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\Options\\AionReportOptions.cs", "RelativeToolTip": "BueTape.Aion.Infrastructure\\Options\\AionReportOptions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-29T11:08:12.736Z"}, {"$type": "Document", "DocumentIndex": 118, "Title": "BlueTape.Aion.Application.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj", "RelativeToolTip": "BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2024-05-22T12:26:38.426Z"}, {"$type": "Document", "DocumentIndex": 119, "Title": "ITransactionService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Abstractions\\ITransactionService.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Abstractions\\ITransactionService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Abstractions\\ITransactionService.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Abstractions\\ITransactionService.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAuwBUAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-16T10:46:10.989Z"}, {"$type": "Document", "DocumentIndex": 120, "Title": "ReceiverResponseModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Models\\Ach\\Pull\\Response\\ReceiverResponseModel.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Models\\Ach\\Pull\\Response\\ReceiverResponseModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Models\\Ach\\Pull\\Response\\ReceiverResponseModel.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Models\\Ach\\Pull\\Response\\ReceiverResponseModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwA8AAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-07T13:15:36.613Z"}, {"$type": "Document", "DocumentIndex": 121, "Title": "IReportService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Abstractions\\IReportService.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Abstractions\\IReportService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Abstractions\\IReportService.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Abstractions\\IReportService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-07-10T12:11:38.576Z"}, {"$type": "Document", "DocumentIndex": 122, "Title": "AionErrorCodes.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Constants\\AionErrorCodes.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Constants\\AionErrorCodes.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Constants\\AionErrorCodes.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Constants\\AionErrorCodes.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-18T10:19:38.198Z"}, {"$type": "Document", "DocumentIndex": 123, "Title": "vctmp52464_351481.TransactionService.00000000.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\TFSTemp\\vctmp52464_351481.TransactionService.00000000.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\TFSTemp\\vctmp52464_351481.TransactionService.00000000.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\TFSTemp\\vctmp52464_351481.TransactionService.00000000.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\TFSTemp\\vctmp52464_351481.TransactionService.00000000.cs", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-18T10:01:33.101Z"}, {"$type": "Document", "DocumentIndex": 124, "Title": "CreateCounterpartyRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\CreateCounterparty\\CreateCounterpartyRequest.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.External\\Models\\CreateCounterparty\\CreateCounterpartyRequest.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\Models\\CreateCounterparty\\CreateCounterpartyRequest.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.External\\Models\\CreateCounterparty\\CreateCounterpartyRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-08-27T08:41:11.907Z"}, {"$type": "Document", "DocumentIndex": 125, "Title": "IBankAccountService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Abstractions\\IBankAccountService.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Abstractions\\IBankAccountService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Abstractions\\IBankAccountService.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Abstractions\\IBankAccountService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-07-11T11:51:59.374Z"}, {"$type": "Document", "DocumentIndex": 131, "Title": "IInternalTransferService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Abstractions\\IInternalTransferService.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Abstractions\\IInternalTransferService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Abstractions\\IInternalTransferService.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Abstractions\\IInternalTransferService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-07-26T13:55:06.944Z"}, {"$type": "Document", "DocumentIndex": 126, "Title": "ICompanyService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Abstractions\\ICompanyService.cs", "RelativeDocumentMoniker": "BlueTape.Aion.Application\\Abstractions\\ICompanyService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\Abstractions\\ICompanyService.cs", "RelativeToolTip": "BlueTape.Aion.Application\\Abstractions\\ICompanyService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-07-11T11:51:59.997Z"}, {"$type": "Document", "DocumentIndex": 127, "Title": ".giti<PERSON>re", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\.gitignore", "RelativeDocumentMoniker": "BlueTape.Functions.Aion.TransactionStatusReport\\.gitignore", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Functions.Aion.TransactionStatusReport\\.gitignore", "RelativeToolTip": "BlueTape.Functions.Aion.TransactionStatusReport\\.gitignore", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2024-06-17T11:02:44.173Z"}, {"$type": "Document", "DocumentIndex": 128, "Title": "MockHelper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\Helper\\MockHelper.cs", "RelativeDocumentMoniker": "BlueTape.Application.Tests\\Helper\\MockHelper.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Application.Tests\\Helper\\MockHelper.cs", "RelativeToolTip": "BlueTape.Application.Tests\\Helper\\MockHelper.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-23T13:15:42.995Z"}, {"$type": "Document", "DocumentIndex": 129, "Title": "BlueTape.Aion.Domain.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj", "RelativeDocumentMoniker": "BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj", "RelativeToolTip": "BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2024-05-23T09:25:02.637Z"}, {"$type": "Document", "DocumentIndex": 130, "Title": "DependencyRegistrar.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\DI\\DependencyRegistrar.cs", "RelativeDocumentMoniker": "BlueTape.Aion.DataAccess.MongoDB\\DI\\DependencyRegistrar.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\DI\\DependencyRegistrar.cs", "RelativeToolTip": "BlueTape.Aion.DataAccess.MongoDB\\DI\\DependencyRegistrar.cs", "ViewState": "AgIAAAAAAAAAAAAAAIBZwAYAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-23T09:24:51.738Z"}]}]}]}