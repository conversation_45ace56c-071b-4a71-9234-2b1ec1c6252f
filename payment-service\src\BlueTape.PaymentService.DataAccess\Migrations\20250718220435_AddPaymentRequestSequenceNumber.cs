﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BlueTape.PaymentService.DataAccess.Migrations
{
    /// <inheritdoc />
    public partial class AddPaymentRequestSequenceNumber : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "SequenceNumber",
                table: "PaymentRequests",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "SequenceN<PERSON><PERSON>",
                table: "PaymentRequests");
        }
    }
}
