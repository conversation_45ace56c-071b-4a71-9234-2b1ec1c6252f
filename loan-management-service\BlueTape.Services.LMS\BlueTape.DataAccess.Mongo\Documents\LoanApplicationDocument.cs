﻿using BlueTape.DataAccess.Mongo.Serializers;
using BlueTape.MongoDB.Attributes;
using BlueTape.MongoDB.DTO;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.DataAccess.Mongo.Documents;

[BsonIgnoreExtraElements]
[MongoCollection("loanapplications")]
public class LoanApplicationDocument : Document
{
    [BsonElement("company_id")]
    public string? CompanyId { get; set; }

    [BsonElement("invoiceDetails")]
    public InvoiceDetails? InvoiceDetails { get; set; }

    [BsonElement("status")]
    public string? Status { get; set; }

    [BsonElement("type")]
    public string? Type { get; set; }

    [BsonSerializer(typeof(FlexibleDoubleSerializer))]
    [BsonElement("amountDue")]
    public double? AmountDue { get; set; }

    [BsonElement("draft")]
    public Dictionary<string, object>? Draft { get; set; }

    [BsonElement("progress")]
    public BsonDocument? Progress { get; set; }

    [BsonElement("executionArn")]
    public string? ExecutionArn { get; set; }

    [BsonElement("creditApplicationId")]
    public string? CreditApplicationId { get; set; }

    [BsonElement("drawApprovalId")]
    public string? DrawApprovalId { get; set; }

    [BsonElement("submitDate")]
    public DateTime? SubmitDate { get; set; }

    [BsonElement("decisionDate")]
    public DateTime? DecisionDate { get; set; }

    [BsonElement("approvedBy")]
    public string? ApprovedBy { get; set; }

    [BsonSerializer(typeof(FlexibleDoubleSerializer))]
    [BsonElement("approvedAmount")]
    public double? ApprovedAmount { get; set; }

    [BsonSerializer(typeof(FlexibleDoubleSerializer))]
    [BsonElement("usedAmount")]
    public double? UsedAmount { get; set; }

    [BsonElement("isSentBack")]
    public bool? IsSentBack { get; set; }

    [BsonElement("lms_id")]
    public string? LmsId { get; set; }

    [BsonElement("lastPaymentDate")]
    public string? LastPaymentDate { get; set; }

    [BsonElement("nextPaymentDate")]
    public string? NextPaymentDate { get; set; }

    [BsonSerializer(typeof(FlexibleDoubleSerializer))]
    [BsonElement("nextPaymentAmount")]
    public double? NextPaymentAmount { get; set; }

    [BsonSerializer(typeof(FlexibleDoubleSerializer))]
    [BsonElement("remainingAmount")]
    public double? RemainingAmount { get; set; }

    [BsonSerializer(typeof(FlexibleDoubleSerializer))]
    [BsonElement("processingAmount")]
    public double? ProcessingAmount { get; set; }

    [BsonSerializer(typeof(FlexibleDoubleSerializer))]
    [BsonElement("pastDueAmount")]
    public double? PastDueAmount { get; set; }

    [BsonElement("notes")]
    public IEnumerable<LoanApplicationNotesDocument>? Notes { get; set; }

    [BsonElement("updatedAtByCompatibility")]
    public DateTime? UpdatedAtByCompatibility { get; set; }

    [BsonElement("metadata")]
    public LoanApplicationMetadataDocument? Metadata { get; set; }
}
