using AutoMapper;
using BlueTape.Common.ExceptionHandling.Models;
using BlueTape.PaymentService.API.ViewModels;
using BlueTape.PaymentService.Application.Abstractions.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.PaymentService.API.Controllers;

[ExcludeFromCodeCoverage(Justification = "Controllers are not required to be tested at the moment")]
[Authorize]
[ApiController]
[Route("admin")]
public class AdminController(IPaymentQueueService paymentQueueService, IMapper mapper) : ControllerBase
{
    private readonly IMapper _mapper = mapper;

    /// <summary>
    /// Updates the sequence order of payment requests in disbursement queues
    /// </summary>
    /// <param name="request">Request containing the ordered list of payment request IDs</param>
    /// <param name="userId ">User ID performing the update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of the sequence update operation</returns>
    [HttpPatch("paymentRequests/disbursementQueues/sequence")]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ErrorModel), StatusCodes.Status500InternalServerError)]
    public async Task UpdateSequenceOrder(
        [FromBody] UpdateSequenceOrderViewModel request,
        [FromHeader, Required] string userId,
        CancellationToken cancellationToken)
    {
        await paymentQueueService.UpdateSequenceOrder(request.PaymentRequestIds, userId, cancellationToken);
    }
}