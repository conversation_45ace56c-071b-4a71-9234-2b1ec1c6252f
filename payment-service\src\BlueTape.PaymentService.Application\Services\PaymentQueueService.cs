﻿using AutoMapper;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.Application.Abstractions.Processors;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Models;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Services;

public class PaymentQueueService(
    IPaymentJobProcessor paymentJobProcessor,
    IMapper mapper,
    ISequenceNumberService sequenceNumberService,
    ILogger<PaymentQueueService> logger)
    : IPaymentQueueService
{
    public async Task<IEnumerable<PaymentRequestModel>> GetDisbursementQueuesPaymentRequests(string provider, PaymentSubscriptionType subscriptionCode, CancellationToken ct)
    {
        var result = await paymentJobProcessor.GetCommandsToExecute(ct);

        var paymentRequests = result
            .Select(c => c.PaymentRequest)
            .Where(x => x != null && x.PaymentSubscription == subscriptionCode)
            .Distinct()
            .ToList();

        return mapper.Map<IEnumerable<PaymentRequestModel>>(paymentRequests);
    }

    public async Task UpdateSequenceOrder(List<Guid> paymentRequestIds, string updatedBy, CancellationToken ct)
    {
        try
        {
            if (!paymentRequestIds.Any())
            {
                throw new ArgumentException("Payment request IDs list cannot be empty", nameof(paymentRequestIds));
            }

            var updatedCount = await sequenceNumberService.UpdateSequenceNumbers(paymentRequestIds, updatedBy, ct);

            logger.LogInformation("Updated sequence order for {UpdatedCount} payment requests by {UpdatedBy}, processed payment request ids: {paymentReqeustIds}",
                updatedCount, updatedBy, string.Concat(paymentRequestIds, ", "));

        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating sequence order for payment requests by {UpdatedBy}", updatedBy);
            throw;
        }
    }
}
