﻿using System.Diagnostics.CodeAnalysis;
using Azure.Data.Tables;
using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Constants;
using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.InternalTransfer;
using BlueTape.Aion.DataAccess.External.Models.Transactions;
using BlueTape.Common.ExceptionHandling.Exceptions;
using BlueTape.Integrations.Aion.AzureTableStorage.Abstractions;
using BlueTape.Integrations.Aion.AzureTableStorage.Entities;
using BlueTape.Integrations.Aion.Infrastructure.Constants;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.Utilities.Extensions;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using System.Globalization;
using BlueTape.Aion.DataAccess.External.Models.InstantTransfer.Response;

namespace BlueTape.Aion.Application.Service;

[ExcludeFromCodeCoverage]
public class TransactionService(
    IAzureStorageTransactionRepository azureStorageTransactionRepository,
    IDateProvider dateProvider,
    ILogger<TransactionService> logger,
    IErrorNotificationService notificationService)
    : ITransactionService
{
    public async Task<List<TransactionEntity>> GetAllExistingSentOrReceivedTransactions(CancellationToken ctx)
    {
        var result = await azureStorageTransactionRepository
            .GetByAionTransactionStatusAsync([AionStatuses.Sent, AionStatuses.Received], ctx);

        return result.ToList();
    }

    private async Task SaveAionTransactionAfterCreate(TransactionEntity entity, CancellationToken ctx)
    {
        var existingTransaction = (await azureStorageTransactionRepository
                .GetByBlueTapeTransactionNumbersAsync([entity.BlueTapeTransactionNumber], ctx))
            ?.FirstOrDefault();

        if (existingTransaction == null)
        {
            entity.CreatedAt = dateProvider.CurrentDateTime;
            entity.UpdatedAt = dateProvider.CurrentDateTime;
            entity.IsProcessed = true;

            await azureStorageTransactionRepository.InsertOrReplaceEntityAsync(entity, TableUpdateMode.Merge, ctx);
        }
        
        else if (existingTransaction.AionTransactionType == AionTransactionType.DEFAULT)
        {
            entity.CreatedAt = existingTransaction.CreatedAt;
            entity.UpdatedAt = dateProvider.CurrentDateTime;
            entity.BlueTapeTransactionId = existingTransaction.BlueTapeTransactionId;
            entity.IsProcessed = false;

            await azureStorageTransactionRepository.InsertOrReplaceEntityAsync(entity, TableUpdateMode.Merge, ctx);
        }
        else if (entity.AionTransactionStatus != existingTransaction.AionTransactionStatus)
        {
            existingTransaction.AionTransactionStatus = entity.AionTransactionStatus;
            existingTransaction.IsProcessed = false;
            existingTransaction.UpdatedAt = dateProvider.CurrentDateTime;

            await azureStorageTransactionRepository.InsertOrReplaceEntityAsync(existingTransaction, TableUpdateMode.Merge, ctx);
        }
    }

    public async Task SaveAionCreatedAchTransactionAsync(AchResponseObj achTransaction, CancellationToken ctx)
    {
        var entity = MapToTransactionEntity(achTransaction);
        await SaveAionTransactionAfterCreate(entity, ctx);
    }
    
    public async Task SaveAionCreatedWireTransactionAsync(WireTransferObjItem wireTransferObjItem, CancellationToken ctx)
    {
        var entity = MapToTransactionEntity(wireTransferObjItem);
        await SaveAionTransactionAfterCreate(entity, ctx);
    }

    public async Task SaveAionCreatedInstantTransactionAsync(InstantTransferObjectItemResponse instantTransferObjectItemResponse,
        CancellationToken ctx)
    {
        var entity = MapToTransactionEntity(instantTransferObjectItemResponse);
        await SaveAionTransactionAfterCreate(entity, ctx);
    }

    public async Task SaveAionCreatedInternalTransactionAsync(BookTransferObj achTransaction, CancellationToken ctx)
    {
        var entity = MapToTransactionEntity(achTransaction);
        await SaveAionTransactionAfterCreate(entity, ctx);
    }

    private TransactionEntity MapToTransactionEntity(InstantTransferObjectItemResponse responseObj)
    {
        return new TransactionEntity
        {
            AionTransactionNumber = responseObj.RtpPaymentInfo.ReferenceId,
            AionTransactionStatus = responseObj.Status,
            IsProcessed = false,
            ErrorCode = "", //Check possible existanse of error code field
            StatusMessage = responseObj.StatusMessage,
            BlueTapeTransactionNumber = !string.IsNullOrEmpty(responseObj.ContextIdentifier)
                ? responseObj.ContextIdentifier.Replace("#", string.Empty)
                : null, // temporary solution with replacing invalid char for azure tables
            AionTransactionType = AionTransactionType.EXTERNAL,
            InstantId = responseObj.RtpPaymentInfo.RtpPaymentId,
            PaymentMethodType = AionPaymentMethodType.INSTANT,
            EffectiveDate = dateProvider.CurrentDateTime,
            TransferType = AionTransferType.PUSH,
            AionFee = responseObj.FeeAmount,
            
        };
    }
    
    private TransactionEntity MapToTransactionEntity(WireTransferObjItem responseObj)
    {
        return new TransactionEntity
        {
            AionTransactionNumber = responseObj.ReferenceId,
            AionTransactionStatus = responseObj.WasReversed ? AionStatuses.Error : responseObj.Status,
            IsProcessed = false,
            ErrorCode = "", //Check possible existanse of error code field
            StatusMessage = responseObj.StatusMessage,
            BlueTapeTransactionNumber = !string.IsNullOrEmpty(responseObj.ContextIdentifier)
                ? responseObj.ContextIdentifier.Replace("#", string.Empty)
                : null, // temporary solution with replacing invalid char for azure tables
            AionTransactionType = AionTransactionType.EXTERNAL,
            WireId = responseObj.Id,
            PaymentMethodType = AionPaymentMethodType.WIRE,
            EffectiveDate = !string.IsNullOrEmpty(responseObj.EffectiveDate)
                ? DateTimeOffset.Parse(responseObj.EffectiveDate, CultureInfo.InvariantCulture).UtcDateTime
                : null,
            TransferType = AionTransferType.PUSH,
            AionFee = responseObj.FeeAmount
        };
    }
    
    private TransactionEntity MapToTransactionEntity(AchResponseObj responseObj)
    {
        var parsedResult = Enum.TryParse(responseObj.TransactionType.ToUpper(), out AionTransferType aionTransferType);
        if (!parsedResult) throw new VariableNullException(nameof(aionTransferType));

        return new TransactionEntity
        {
            AionTransactionNumber = responseObj.ReferenceId,
            AionTransactionStatus = responseObj.Status,
            IsProcessed = false,
            ErrorCode = responseObj.ReasonCode,
            StatusMessage = responseObj.StatusMessage,
            BlueTapeTransactionNumber = !string.IsNullOrEmpty(responseObj.ContextIdentifier)
                ? responseObj.ContextIdentifier.Replace("#", string.Empty)
                : null, // temporary solution with replacing invalid char for azure tables
            AionTransactionType = AionTransactionType.EXTERNAL,
            AionFee = responseObj.FeeAmount,
            AchId = responseObj.Id,
            PaymentMethodType = AionPaymentMethodType.ACH,
            EffectiveDate = !string.IsNullOrEmpty(responseObj.EffectiveDate)
                ? DateTime.SpecifyKind(DateTime.ParseExact(responseObj.EffectiveDate, "yyMMdd", CultureInfo.InvariantCulture), DateTimeKind.Utc)
                : null,
            TransferType = aionTransferType
        };
    }

    private TransactionEntity MapToTransactionEntity(BookTransferObj responseObj)
    {
        return new TransactionEntity
        {
            AionTransactionNumber = responseObj.TraceNumber,
            AionTransactionStatus = responseObj.Status,
            IsProcessed = false,
            ErrorCode = string.Empty,
            StatusMessage = responseObj.StatusMessage,
            BlueTapeTransactionNumber = responseObj.ContextIdentifier,
            AionTransactionType = AionTransactionType.INTERNAL,
            BookId = responseObj.Id,
            PaymentMethodType = AionPaymentMethodType.Internal,
            EffectiveDate = null,
            TransferType = null,
        };
    }

    public Task SaveAionInternalTransactionsAsync(List<BookTransferObj> achList, CancellationToken ctx)
    {
        var transactions = achList
            .GroupBy(x => x.ContextIdentifier)
            .Select(group => group.OrderByDescending(t => t.CreatedAt).First())
            .Select(MapToTransactionEntity)
            .Where(x => !string.IsNullOrEmpty(x.BlueTapeTransactionNumber))
            .ToList();

        return SaveAsync(transactions, ctx);
    }

    public Task SaveAionAchTransactionsAsync(List<AchResponseObj> achList, CancellationToken ctx)
    {
        var transactions = achList
            .GroupBy(x => x.ContextIdentifier)
            .Select(group => group.OrderByDescending(t => t.CreatedAt).First())
        .Select(MapToTransactionEntity)
        .Where(x => !string.IsNullOrEmpty(x.BlueTapeTransactionNumber))
        .ToList();

        return SaveAsync(transactions, ctx);
    }

    public Task SaveAionWireTransactionsAsync(List<WireTransferObjItem> listOfWire, CancellationToken ctx)
    {
        var transactions = listOfWire
            .GroupBy(x => x.ContextIdentifier)
            .Select(group => group.Where(x => x.CreatedAt != null).OrderByDescending(t => t.CreatedAt).First())
            .Select(MapToTransactionEntity)
            .Where(x => !string.IsNullOrEmpty(x.BlueTapeTransactionNumber))
            .ToList();

        return SaveAsync(transactions, ctx);
    }

    public Task SaveAionInstantTransactionsAsync(List<InstantTransferObjectItemResponse> listOfInstant, CancellationToken ctx)
    {
        var transactions = listOfInstant
            .GroupBy(x => x.ContextIdentifier)
            .Select(group => group.Where(x => x.CreatedAt != null)
                .OrderByDescending(t => t.CreatedAt).First())
            .Select(MapToTransactionEntity)
            .Where(x => !string.IsNullOrEmpty(x.BlueTapeTransactionNumber))
            .ToList();

        return SaveAsync(transactions, ctx);
    }

    public Task SaveAionAchTransactionReturnsAsync(List<AchResponseObj> achReturnsList, CancellationToken ctx)
    {
        var transactions = achReturnsList
            .OrderBy(t => t.CreatedAt)
            .ToList();

        return SaveReturnsAsync(transactions, ctx);
    }

    public async Task ProcessClearedTransactionsAsync(
        List<TransactionListObj> transactionList,
        List<TransactionEntity> transactionEntities,
        CancellationToken ctx)
    {
        var transactionsToUpdate = transactionEntities
            .Where(x =>
                x.AchId != null &&
                transactionList.Any(y =>
                    !string.IsNullOrEmpty(y.AchId) &&
                    y.AchId.Equals(x.AchId)))
            .ToList();

        if (transactionsToUpdate.Count != 0)
        {
            foreach (var item in transactionsToUpdate)
            {
                item.UpdatedAt = dateProvider.CurrentDateTime;
                item.IsProcessed = false;
                item.AionTransactionStatus = AionStatuses.Cleared;
            }

            foreach (var entity in transactionsToUpdate)
            {
                await azureStorageTransactionRepository.InsertOrReplaceEntityAsync(entity, TableUpdateMode.Merge, ctx);
            }
        }
    }

    public Task<IEnumerable<TransactionEntity>> GetByRowKeyPrefixAsync(string prefix, DateTime? startDate, DateTime? endDate, CancellationToken ct) =>
        azureStorageTransactionRepository.GetByRowKeyPrefixAsync(prefix, startDate, endDate, ct);

    public async Task EnsureTransactionDoesNotExist(string transactionId, string transactionNumber, CancellationToken ctx)
    {
        var existingTransfer = await azureStorageTransactionRepository.GetByBlueTapeTransactionIdOrNumberAsync(transactionId, transactionNumber, ctx);

        if (existingTransfer.Count() != 0)
            throw new ArgumentOutOfRangeException($"Transaction with BlueTape ID {transactionId} and number {transactionNumber} already exists inside the table storage. Possible attempt of a duplicate payment");

        // Type and other properties should be updated after the transfer is created
        var entity = new TransactionEntity
        {
            BlueTapeTransactionNumber = transactionNumber,
            IsProcessed = true,
            BlueTapeTransactionId = transactionId,
            AionTransactionType = AionTransactionType.DEFAULT,
            CreatedAt = dateProvider.CurrentDateTime,
            UpdatedAt = dateProvider.CurrentDateTime,
        };

        await azureStorageTransactionRepository.InsertOrReplaceEntityAsync(entity, TableUpdateMode.Merge, ctx);
    }

    private async Task SaveAsync(List<TransactionEntity> transactions, CancellationToken ctx)
    {
        var existingTransactions = (await azureStorageTransactionRepository.GetByBlueTapeTransactionNumbersAsync
            (transactions.Select(t => t.BlueTapeTransactionNumber).ToArray(), ctx))
            .ToList();

        var transactionsToInsertOrUpdate = new List<TransactionEntity>();

        foreach (var transaction in transactions)
        {
            var existingTransaction = existingTransactions.FirstOrDefault(t => t.BlueTapeTransactionNumber == transaction.BlueTapeTransactionNumber);

            logger.LogInformation("Start processing new transaction achId: {AchId}, bookId: {bookId} status: {AionTransactionStatus}",
                transaction.AchId, transaction.BookId, transaction.AionTransactionStatus);

            if (existingTransaction == null)
            {
                logger.LogInformation("Transaction does not exist in database");

                ProcessNewTransaction(transaction, transactionsToInsertOrUpdate);
            }
            else
            {
                logger.LogInformation("Transaction  exist in database. ExistingData: achId: {AchId}, bookId: {bookId} status: {AionTransactionStatus}",
                        existingTransaction.AchId, existingTransaction.BookId, existingTransaction.AionTransactionStatus);

                switch (transaction.AionTransactionType)
                {
                    case AionTransactionType.INTERNAL:
                        ProcessExistingInternalTransaction(transaction, existingTransaction, transactionsToInsertOrUpdate);
                        break;
                    case AionTransactionType.EXTERNAL:
                        ProcessExistingExternalTransaction(transaction, existingTransaction, transactionsToInsertOrUpdate, ctx);
                        break;
                    case AionTransactionType.DEFAULT:
                    default:
                        throw new ArgumentOutOfRangeException();
                }
            }
        }

        if (transactionsToInsertOrUpdate.Count != 0)
        {
            foreach (var entity in transactionsToInsertOrUpdate)
            {
                await azureStorageTransactionRepository.InsertOrReplaceEntityAsync(entity, TableUpdateMode.Merge, ctx);
            }
        }
    }

    private async Task SaveReturnsAsync(List<AchResponseObj> returnedTransactions, CancellationToken ctx)
    {
        var originalTransactionsAchIds = returnedTransactions.Select(t => t.Original.PaymentId).ToArray();

        var existingTransactions = (await azureStorageTransactionRepository.GetByAchIdsAsync
            (originalTransactionsAchIds, ctx))
            .OrderByDescending(t => t.CreatedAt)
            .ToList();

        var transactionsToInsertOrUpdate = new List<TransactionEntity>();

        foreach (var transaction in returnedTransactions)
        {
            var originalAchId = transaction.Original.PaymentId;
            var existingTransaction = existingTransactions.Find(t => t.AchId == originalAchId);

            logger.LogInformation("Start processing new returned transaction achId: {AchId}",
                originalAchId);

            if (existingTransaction == null)
            {
                logger.LogInformation("Original transaction does not exist in the database");
                // Push record to database at least to avoid multiple notifications
                existingTransaction = new()
                {
                    AchId = originalAchId,
                    CreatedAt = dateProvider.CurrentDateTime,
                    BlueTapeTransactionNumber = $"UnmatchedReturn_${transaction.Amount}_{transaction.ReferenceId}"
                };

                await notificationService.NotifyUnmatchedReturn(transaction, ctx);
            }
            else
            {
                logger.LogInformation("Original transaction exist in database. ExistingData: achId: {AchId}, bookId: {BookId} status: {AionTransactionStatus}",
                        existingTransaction.AchId, existingTransaction.BookId, existingTransaction.AionTransactionStatus);
            }

            if (!string.IsNullOrEmpty(existingTransaction.ErrorCode) &&
                    (existingTransaction.AionTransactionStatus == AionStatuses.Returned || !IsTransactionReturn(transaction))) continue;

            await notificationService.NotifyReturnedTransaction(transaction, ctx);

            if (IsTransactionReturn(transaction))
                existingTransaction.AionTransactionStatus = AionStatuses.Returned;

            existingTransaction.IsProcessed = false;
            existingTransaction.ErrorCode = transaction.ReasonCode;
            existingTransaction.StatusMessage = transaction.Error;
            existingTransaction.UpdatedAt = dateProvider.CurrentDateTime;

            transactionsToInsertOrUpdate.Add(existingTransaction);
        }

        if (transactionsToInsertOrUpdate.Count != 0)
        {
            foreach (var entity in transactionsToInsertOrUpdate)
            {
                await azureStorageTransactionRepository.InsertOrReplaceEntityAsync(entity, TableUpdateMode.Merge, ctx);
            }
        }
    }

    private void ProcessNewTransaction(TransactionEntity transaction, List<TransactionEntity> transactionsToInsertOrUpdate)
    {
        if (transaction is { AionTransactionType: AionTransactionType.INTERNAL, AionTransactionStatus: AionStatuses.Sent })
            transaction.AionTransactionStatus = AionStatuses.Cleared;

        transaction.CreatedAt = dateProvider.CurrentDateTime;
        transaction.UpdatedAt = dateProvider.CurrentDateTime;
        transactionsToInsertOrUpdate.Add(transaction);
    }

    private void ProcessExistingInternalTransaction(
        TransactionEntity transaction,
        TransactionEntity existingTransaction,
        List<TransactionEntity> transactionsToInsertOrUpdate)
    {
        var needUpdate = false;

        if (transaction.AionTransactionStatus == AionStatuses.Sent &&
            existingTransaction.AionTransactionStatus == AionStatuses.Sent)
        {
            transaction.AionTransactionStatus = AionStatuses.Cleared;
            needUpdate = true;
        }
        else if (TransactionStatusOrNumberChanged(transaction, existingTransaction) &&
                 !(TransactionSentOrReceived(transaction) && existingTransaction.AionTransactionStatus.Equals(AionStatuses.Cleared)))
        {
            // We agree when InternalTransfer is in status Sent for us it means that money transfer is done
            // And it is not required to search internal transfer in /api/bb/getTransactions
            if (TransactionSentOrReceived(transaction))
                transaction.AionTransactionStatus = AionStatuses.Cleared;

            needUpdate = true;
        }

        if (needUpdate)
        {
            existingTransaction.AionTransactionStatus = transaction.AionTransactionStatus;
            existingTransaction.IsProcessed = false;
            existingTransaction.UpdatedAt = dateProvider.CurrentDateTime;
            transactionsToInsertOrUpdate.Add(existingTransaction);
        }
    }

    private static bool TransactionSentOrReceived(TransactionEntity transaction)
    {
        return transaction.AionTransactionStatus.Equals(AionStatuses.Sent) ||
               transaction.AionTransactionStatus.Equals(AionStatuses.Received);
    }

    private static bool TransactionStatusOrNumberChanged(TransactionEntity newTransaction, TransactionEntity existingTransaction)
    {
        return newTransaction.AionTransactionStatus != existingTransaction.AionTransactionStatus
               || (existingTransaction.AionTransactionNumber.IsNullOrEmpty() && newTransaction.AionTransactionNumber != existingTransaction.AionTransactionNumber);
    }

    private static bool IsTransactionReturn(AchResponseObj transaction)
    {
        return string.Equals(transaction.PaymentType, AionConstants.AionReturnPaymentType, StringComparison.CurrentCultureIgnoreCase);
    }

    private async void ProcessExistingExternalTransaction(
        TransactionEntity transaction,
        TransactionEntity existingTransaction,
        List<TransactionEntity> transactionsToInsertOrUpdate,
        CancellationToken ct)
    {
        var needUpdate = TransactionStatusOrNumberChanged(transaction, existingTransaction) &&
            !(TransactionSentOrReceived(transaction) && existingTransaction.AionTransactionStatus.Equals(AionStatuses.Cleared)) &&
            // Instead of this need to add isReturned flag to transaction entity
            !existingTransaction.AionTransactionStatus.Equals(AionStatuses.Returned);

        if (!needUpdate) return;

        existingTransaction.AionTransactionStatus = transaction.AionTransactionStatus;

        if (existingTransaction.AionTransactionNumber.IsNullOrEmpty())
        {
            existingTransaction.AionTransactionNumber = transaction.AionTransactionNumber;
        }
        else if (existingTransaction.AionTransactionNumber != transaction.AionTransactionNumber)
        {
            var message = $"Transaction reference number change blocked. Existing: {existingTransaction.AionTransactionNumber}, New: {transaction.AionTransactionNumber}";

            // not notify the issue if the transaction not generated by the payment service - payment service transaction number starts with BT
            if (existingTransaction.BlueTapeTransactionNumber.StartsWith("BT"))
                await notificationService.Notify(message, "AionTransactionNumberChanged", "API", ct);

            logger.LogError(message);
        }

        existingTransaction.IsProcessed = false;
        existingTransaction.UpdatedAt = dateProvider.CurrentDateTime;
        existingTransaction.EffectiveDate = transaction.EffectiveDate;
        existingTransaction.TransferType = transaction.TransferType;
        transactionsToInsertOrUpdate.Add(existingTransaction);
    }
}