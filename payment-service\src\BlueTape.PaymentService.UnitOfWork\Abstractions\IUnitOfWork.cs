﻿using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Entities.Base;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.UnitOfWork.Records;
using Microsoft.EntityFrameworkCore.Storage;
using System.Linq.Expressions;

namespace BlueTape.PaymentService.UnitOfWork.Abstractions;

public interface IUnitOfWork : IDisposable
{
    IUoWGenericRepository<PaymentRequestEntity> PaymentRequestRepository { get; }
    IUoWGenericRepository<PaymentRequestDetailsEntity> PaymentRequestDetailsRepository { get; }
    IUoWGenericRepository<PaymentRequestCommandEntity> PaymentRequestCommandRepository { get; }
    IUoWGenericRepository<PaymentRequestPayableEntity> PaymentRequestPayableRepository { get; }
    IUoWGenericRepository<PaymentTransactionHistoryEntity> PaymentTransactionHistoryRepository { get; }
    IUoWGenericRepository<PaymentTransactionEntity> PaymentTransactionRepository { get; }
    IUoWGenericRepository<PaymentRequestFeeEntity> PaymentRequestFeeRepository { get; }
    IUoWGenericRepository<ForbiddenCompanyEntity> ForbiddenCompanyRepository { get; }
    IUoWGenericRepository<EventLogEntity> EventLogRepository { get; }

    Task<IEnumerable<TEntity>> Get<TEntity>(
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>>? filter = null,
        Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>>? orderBy = null,
        string includeProperties = "") where TEntity : EntityWithId;

    Task<TEntity?> GetById<TEntity>(Guid id, CancellationToken cancellationToken, string includeProperties = "")
        where TEntity : EntityWithId;

    Task<PaymentRequestCommandEntity?> GetCommandByTransactionId(Guid transactionId, CancellationToken ct);

    Task<List<PaymentRequestCommandEntity>> GetCommandsByPaymentRequestId(Guid paymentRequestId, CancellationToken ct);

    Task SaveRangePaymentRequestsChanges(List<PaymentRequestEntity> paymentRequests, List<PaymentRequestCommandEntity> commands,
        List<PaymentTransactionPayload> transactionPayloads, CancellationToken ct);

    Task SaveRangeTransactionChangesWithoutHistories(List<PaymentRequestCommandEntity> commands,
        List<PaymentTransactionEntity> transactions, CancellationToken ct);

    Task SaveTransactionChanges(PaymentTransactionEntity transaction, PaymentRequestCommandEntity command,
        PaymentTransactionPayload transactionPayload, CancellationToken ct);

    Task SaveTransactionChangesWithoutCommand(PaymentTransactionEntity transaction, PaymentTransactionPayload transactionPayload,
        CancellationToken ct);

    Task UpdateTransactionAndCommandWithoutSaving(PaymentTransactionEntity transaction, PaymentRequestCommandEntity command,
        CancellationToken ct);

    Task<PaymentTransactionEntity> UpdateCommandAndInsertTransactionWithoutSaving(PaymentRequestCommandEntity command,
        PaymentTransactionEntity newTransaction, CancellationToken ct);

    Task UpdatePaymentRequestAndInsertCommand(PaymentRequestEntity paymentRequest, PaymentRequestCommandEntity newCommand,
        CancellationToken ct);

    Task HandleRollbackTransactions(PaymentRequestCommandEntity recalledCommand, List<PaymentTransactionEntity> rollbackTransactions,
        List<PaymentRequestCommandEntity> rollbackCommands, CancellationToken ct);

    Task HandlePaymentRequestStatusChange(List<PaymentTransactionEntity> transactions,
        List<PaymentTransactionPayload> transactionPayloads, PaymentRequestEntity paymentRequest,
        List<PaymentRequestCommandEntity> paymentRequestCommandItems, CommandStatus status, string updatedBy, CancellationToken ct);

    Task InsertCommandAndTransaction(PaymentRequestCommandEntity command, PaymentTransactionEntity transaction,
        CancellationToken ct);

    Task InsertCommandsAndTransactionsRange(List<PaymentRequestCommandEntity> commands, List<PaymentTransactionEntity> transactions,
        CancellationToken ct);

    Task UpdateCommand(PaymentRequestCommandEntity command, CancellationToken ct);

    Task UpdateCommandsRange(List<PaymentRequestCommandEntity> commands, CancellationToken ct);

    Task UpdatePaymentRequest(PaymentRequestEntity paymentRequest, CancellationToken ct);

    Task UpdatePaymentRequestRange(IEnumerable<PaymentRequestEntity> paymentRequests, CancellationToken ct);

    Task UpdateTransaction(PaymentTransactionEntity transaction, CancellationToken ct);

    Task InsertPaymentRequestWithoutSaving(PaymentRequestEntity paymentRequest, CancellationToken ct);

    Task InsertPaymentRequestDetails(PaymentRequestDetailsEntity details, CancellationToken ct);
    Task<int> SaveAsync(CancellationToken cancellationToken);

    Task UpdatePaymentRequestDetails(PaymentRequestDetailsEntity details, CancellationToken ct);

    Task<List<PaymentTransactionHistoryEntity?>> GetMostRecentNotEnoughBalanceHistoryItemsByTransactionIds(IEnumerable<Guid> transactionIds, CancellationToken ct);

    Task<bool> AreCompaniesPaymentsForbidden(IEnumerable<string> companyIds, CancellationToken ct);

    Task<List<PaymentTransactionEntity>> GetAchPaymentTransactionsByDateRange(DateTime startDate, DateTime endDate);

    Task<List<PaymentRequestEntity>> GetPaymentsByPayableIds(IEnumerable<string> payableIds, CancellationToken ct);

    Task<List<PaymentRequestEntity>> GetPaymentsByIds(IEnumerable<Guid> ids, CancellationToken ct);

    Task<List<PaymentRequestEntity>> GetPaymentsByDrawId(Guid drawId, IEnumerable<PaymentRequestType>? types,
        CancellationToken ct);

    Task<IDbContextTransaction> BeginTransactionAsync(CancellationToken cancellationToken);
}
