{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj", "projectName": "BlueTape.BackOffice.DecisionEngine.DataAccess.External", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BlueTape.AzureKeyVault": {"target": "Package", "version": "[1.0.3, )"}, "BlueTape.Common.ExceptionHandling": {"target": "Package", "version": "[1.0.8, )"}, "BlueTape.Integrations.Giact": {"target": "Package", "version": "[1.0.3, )"}, "BlueTape.Integrations.Plaid": {"target": "Package", "version": "[1.0.7, )"}, "BlueTape.InvoiceClient": {"target": "Package", "version": "[1.0.24, )"}, "BlueTape.InvoiceService": {"target": "Package", "version": "[1.0.43, )"}, "BlueTape.LS": {"target": "Package", "version": "[1.1.76, )"}, "BlueTape.LS.Domain": {"target": "Package", "version": "[1.1.36, )"}, "BlueTape.OBS": {"target": "Package", "version": "[1.6.72, )"}, "BlueTape.PaymentService": {"target": "Package", "version": "[1.0.14, )"}, "BlueTape.Utilities": {"target": "Package", "version": "[1.4.6, )"}, "Microsoft.CodeAnalysis.NetAnalyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http.Polly": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}, "bluetape.companyservice": {"target": "Package", "version": "[1.3.4, )"}, "bluetape.companyservice.common": {"target": "Package", "version": "[1.1.21, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj", "projectName": "BlueTape.BackOffice.DecisionEngine.Domain", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\BlueTape.BackOffice.DecisionEngine.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BlueTape.Integrations.Aion": {"target": "Package", "version": "[1.0.17, )"}, "BlueTape.LS.Domain": {"target": "Package", "version": "[1.1.36, )"}, "BlueTape.OBS": {"target": "Package", "version": "[1.6.72, )"}, "BlueTape.PaymentService": {"target": "Package", "version": "[1.0.14, )"}, "Microsoft.CodeAnalysis.NetAnalyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests.csproj", "projectName": "BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\tests\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.DataAccess.External\\BlueTape.BackOffice.DecisionEngine.DataAccess.External.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoFixture.Xunit2": {"target": "Package", "version": "[4.18.1, )"}, "Microsoft.CodeAnalysis.NetAnalyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.6.0, )"}, "NSubstitute": {"target": "Package", "version": "[5.1.0, )"}, "Shouldly": {"target": "Package", "version": "[4.2.1, )"}, "bluetape.ls": {"target": "Package", "version": "[1.1.76, )"}, "bluetape.ls.domain": {"target": "Package", "version": "[1.1.36, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.0, )"}, "xunit": {"target": "Package", "version": "[2.4.2, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.4.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}