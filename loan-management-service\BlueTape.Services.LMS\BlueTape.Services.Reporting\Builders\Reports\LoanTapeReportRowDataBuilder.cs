using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyService.Companies;
using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.DataAccess.Mongo.Documents;
using BlueTape.DataAccess.Mongo.Documents.CustomerAccounts;
using BlueTape.InvoiceClient.Abstractions;
using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.OBS.Client.Abstractions;
using BlueTape.OBS.DTOs.DrawApprovalNotes;
using BlueTape.OBS.Enums;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.LMS.Domain.Extensions;
using BlueTape.Services.Reporting.Abstractions.Builders;
using BlueTape.Services.Reporting.Domain.Constants;
using BlueTape.Services.Reporting.Models.FactoringReports;
using BlueTape.Services.Reporting.Models.TradeCreditReports;
using BlueTape.Utilities.Extensions;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using TinyHelpers.Extensions;

namespace BlueTape.Services.Reporting.Builders.Reports;

public class LoanTapeReportRowDataBuilder(
    ICashFlowDataBuilder cashFlowDataBuilder,
    IAccountAuthorizationDataBuilder accountAuthorizationDataBuilder,
    IDraftDataBuilder draftDataBuilder,
    IInvoiceHttpClient invoiceHttpClient,
    ICreditRepository creditRepository,
    ILoanPricingPackageRepository loanPricingPackageRepository,
    IOnBoardingIntegrationExternalService onBoardingHttpClient,
    ICompanyHttpClient companyHttpClient,
    ICustomerAccountRepository customerAccountRepository,
    IOperationRepository operationRepository,
    ILoanRepository loanRepository,
    ILoanApplicationRepository loanApplicationRepository,
    ILogger<LoanTapeReportRowDataBuilder> logger
)
    : ILoanTapeReportRowDataBuilder
{
    public async Task<List<FactoringReportRowModel>> BuildFactoringReportRowsData(IReadOnlyCollection<LoanEntity> loans,
        CancellationToken ctx)
    {
        logger.LogInformation("Factoring loan tape report: started building data for report rows");
        var rows = new List<FactoringReportRowModel>();
        var invoiceIds = loans.SelectMany(x => x.LoanPayables.Select(payable => payable.PayableId))
            .Where(id => !string.IsNullOrEmpty(id));
        logger.LogInformation("Factoring loan tape report: getting invoices: {InvoiceIds}", invoiceIds);

        var invoices = await invoiceHttpClient.GetInvoicesByIdsAsync(invoiceIds.ToArray(), ctx);

        await loans.ForEachAsync(async loan =>
        {
            logger.LogInformation("Factoring loan tape report: started building row data for loan {LoanId}", loan.Id);

            if (string.IsNullOrEmpty(loan.MerchantId))
            {
                logger.LogWarning(
                    "Factoring loan tape report: Loan {LoanId} will be excluded from report generation as it does not have merchant id",
                    loan.Id);
                return;
            }

            var loanPayable = loan.LoanPayables.FirstOrDefault();
            var invoice = invoices?.Find(invoice => invoice.Id == loanPayable?.PayableId);
            if (invoice is null)
            {
                logger.LogWarning(
                    "Factoring loan tape report: Loan {LoanId} will be excluded from report generation as it does not has have invoice",
                    loan.Id);
                return;
            }

            var pricingPackageId = invoice.PaymentDetails?.PricingPackageId;
            var loanPricingPackage = await GetLoanPricingPackage(pricingPackageId, default);
            var returningCustomer = await CalculateFactoringReturningCustomer(loan.MerchantId, ctx);

            var drawApprovalNotesTask = ExternalGetDrawApprovalNotes(loan.DrawApprovalId, ctx);
            var accountAuthorizationDataTask = accountAuthorizationDataBuilder.Build(loan.MerchantId, ctx);
            var addressDataTask = draftDataBuilder.BuildAddressData(loan.MerchantId, ctx);
            var cashFlowDataTask = cashFlowDataBuilder.Build(loan.MerchantId, true, ctx);

            await Task.WhenAll(addressDataTask, cashFlowDataTask, accountAuthorizationDataTask, drawApprovalNotesTask);
            var arAdvanceCredit = await GetArAdvanceCredit(loan, ctx);

            var drawApprovalNotes = drawApprovalNotesTask.Result.Select(x => x.Note);
            var paymentDate = loan.LoanParameters.Find(x => x.IsActive)?.FirstPaymentDate;
            var creditOutstandingBalance = CalculateCreditOutstandingBalance(arAdvanceCredit, loan.ProjectId);

            var row = new FactoringReportRowModel()
            {
                SellerId = loan.MerchantId,
                PurchaserId = loan.CompanyId ?? string.Empty,
                PurchasedReceivablesValue = loan.Amount,
                Term = loan.ActiveLoanTemplate?.PaymentDelayCode,
                DueDate = paymentDate,
                SellerName = GetCompanyNameWithoutDba(loan),
                ReturningCustomer = returningCustomer,
                PurchaseDate = invoice.InvoiceDate,
                AccountId = arAdvanceCredit?.Id.ToString() ?? string.Empty,

                PurchasePrice = loanPricingPackage?.Metadata?.AdvanceRate,
                PurchaseAmount = loan.Amount * loanPricingPackage?.Metadata?.AdvanceRate.ToPercentage(),
                BlueTapeSellerOutstandingAmount = creditOutstandingBalance,
                BackOfficeNotes = drawApprovalNotes,

                DebtInvestor = string.Empty, // Unclear from business
                SellerAccountAuthorizationDetails = accountAuthorizationDataTask.Result,
                SellerCashFlowData = cashFlowDataTask.Result,
                BusinessOwnerAddress = addressDataTask.Result,
            };

            rows.Add(row);

            logger.LogInformation("Factoring loan tape report: finished building row data for loan {LoanId}", loan.Id);
        }, ctx);

        logger.LogInformation("Factoring loan tape report: finished building data for report rows");

        return rows;
    }

    public async Task<List<TradeCreditReportRowModel>> BuildTradeCreditReportRowsData(
        IReadOnlyCollection<LoanEntity> loans, CancellationToken ctx)
    {
        logger.LogInformation("Aion TC loan tape report: started building data for report rows");
        var rows = new List<TradeCreditReportRowModel>();
        var invoiceIds = loans.SelectMany(x => x.LoanPayables.Select(payable => payable.PayableId))
            .Where(id => !string.IsNullOrEmpty(id));
        logger.LogInformation("Aion TC loan tape report: getting invoices: {InvoiceIds}", invoiceIds);

        var invoices = await invoiceHttpClient.GetInvoicesByIdsAsync(invoiceIds.ToArray(), ctx);
        var merchantIds = loans.Select(company => company.MerchantId).Where(id => !string.IsNullOrEmpty(id))
           .Select(id=>id!).Distinct().ToArray();
        var merchants = await companyHttpClient.GetCompaniesByIdsAsync(merchantIds, ctx);
        var customerAccountsIds = invoices?.Select(invoice => invoice.CustomerAccountId)
            .Distinct()
            .Where(id => !string.IsNullOrEmpty(id) && ObjectId.TryParse(id, out _))
            .ToArray() ?? [];
        var customerAccounts = await customerAccountRepository.GetByIds(customerAccountsIds, ctx);
        var loanAppsDictionary = await GetLoanApplications(loans, ctx);

        await loans.ForEachAsync(async loan =>
        {
            var row = await BuildTradeCreditReportRow(loan, invoices, merchants, customerAccounts, loanAppsDictionary, ctx);
            if(row is null)
                return;

            rows.Add(row);
            logger.LogInformation("Aion TC loan tape report: finished building row data for loan {LoanId}", loan.Id);
        }, ctx);

        logger.LogInformation("Aion TC loan tape report: finished building data for report rows");

        return rows;
    }

    private async Task<TradeCreditReportRowModel?> BuildTradeCreditReportRow(LoanEntity loan, List<InvoiceModel>? invoices, List<CompanyModel> merchants,
        IEnumerable<CustomerAccountDocument> customerAccounts, IDictionary<Guid, LoanApplicationDocument> loanAppsDictionary, CancellationToken ctx)
    {
        logger.LogInformation("Aion TC loan tape report: started building row data for loan {LoanId}", loan.Id);

        if (string.IsNullOrEmpty(loan.CompanyId))
        {
            logger.LogWarning(
                "Aion TC loan tape report: Loan {LoanId} will be excluded from report generation as it does not have customer id",
                loan.Id);
            return null;
        }

        var loanPayable = loan.LoanPayables.FirstOrDefault();
        var invoice = invoices?.Find(invoice => invoice.Id == loanPayable?.PayableId);
        if (invoice is null)
        {
            logger.LogWarning(
                "Aion TC loan tape report: Loan {LoanId} will be excluded from report generation as it does not has have invoice",
                loan.Id);
            return null;
        }

        var returningCustomer = await CalculateTradeCreditReturningCustomer(loan.CompanyId, ctx);

        var successfulDisbursementOperations = await operationRepository.GetAll(x =>
            x.Type == "invoice_payment" && x.Metadata.PaymentMethod == "loan" && x.OwnerId == invoice.Id &&
            x.Status != "CANCELED" && x.Status != "FAIL" , ctx);
        var successfulOperation = successfulDisbursementOperations.MaxBy(x => x.CreatedAt);
        var lineOfCredit = await GetLineOfCredit(loan, ctx);

        var drawApprovalNotesTask = ExternalGetDrawApprovalNotes(loan.DrawApprovalId, ctx);
        var accountAuthorizationDataTask = accountAuthorizationDataBuilder.Build(loan.CompanyId, ctx);
        var addressDataTask = draftDataBuilder.BuildAddressData(loan.CompanyId, ctx);
        var cashFlowDataTask = cashFlowDataBuilder.Build(loan.CompanyId, true, ctx);
        var businessNameTask = draftDataBuilder.BuildBusinessNameData(loan.CompanyId, CancellationToken.None);

        await Task.WhenAll(drawApprovalNotesTask, accountAuthorizationDataTask, addressDataTask, cashFlowDataTask,
            businessNameTask);

        var loanParameters = loan.LoanParameters.Find(x => x.IsActive);

        var drawApprovalNotes = drawApprovalNotesTask.Result.Select(x => x.Note);
        var firstPaymentDate = loanParameters?.FirstPaymentDate;
        var paymentFrequency = loan.ActiveLoanTemplate?.PaymentIntervalInDays;
        var lastPaymentDate = loan.LoanReceivables
            .Where(r => IsReceivableActive(r) && r.Type is ReceivableType.Installment or ReceivableType.LoanFee)
            .MaxBy(x => x.ExpectedDate)?.ExpectedDate;
        var creditOutstandingBalance = CalculateCreditOutstandingBalance(lineOfCredit, loan.ProjectId);
        var supplierCompany = merchants.FirstOrDefault(x => x.Id == loan.MerchantId);
        var customerAccount = customerAccounts.FirstOrDefault(x => x.Id == invoice.CustomerAccountId);

        var isResource = (supplierCompany?.Settings.SendFinalPaymentWhenLoanIsPaid ?? false) &&
                         (customerAccount?.Settings?.SendFinalPaymentWhenLoanIsPaid ?? false);

        var loanPricingPackageMetadata = await GetLoanPricingPackageDetails(invoice, loan, loanAppsDictionary);

        var row = new TradeCreditReportRowModel
        {
            CustomerId = loan.CompanyId ?? string.Empty,
            DrawId = loan.Id.ToString(),
            LineOfCreditId = lineOfCredit?.Id.ToString() ?? string.Empty,
            DrawAmountIncludingOriginationFee = loan.Amount + loan.Fee,
            TotalReceivablesFinanced = loan.Amount,
            AdvancePercentage = loanPricingPackageMetadata?.AdvanceRate.ToPercentage(),
            ResourceToSupplierPercentage =
                isResource ? loanPricingPackageMetadata?.FinalPayment.ToPercentage() : null,
            FirstExpectedPayment = firstPaymentDate,
            Term = loan.ActiveLoanTemplate?.PaymentDelayCode,
            PayOffDate = lastPaymentDate,
            ReturningCustomer = returningCustomer,
            PaymentFrequency = paymentFrequency,
            InternalBlueTapeLoansOutstanding = creditOutstandingBalance,
            CreditPolicy = LoanTapeReportConstants.AionCreditPolicy,
            DrawType = LoanTapeReportConstants.DrawType,
            OriginationDate = successfulOperation?.Date,
            AdvanceAmount =  successfulOperation?.Amount, // merchant fee deducted!
            DebtInvestor = string.Empty, // Unclear from business

            BackOfficeNotes = drawApprovalNotes,
            CustomerAccountAuthorizationDetails = accountAuthorizationDataTask.Result,
            CustomerCashFlowData = cashFlowDataTask.Result,
            BusinessOwnerAddress = addressDataTask.Result,
            BusinessInfo = businessNameTask.Result,
        };
        return row;
    }

    private async Task<LoanPricingPackageMetadataDocument?> GetLoanPricingPackageDetails(
        InvoiceModel invoice,
        LoanEntity loan,
        IDictionary<Guid, LoanApplicationDocument> loanAppsDictionary)
    {
        LoanPricingPackageMetadataDocument? loanPricingPackageMetadata = null;
        var pricingPackageId = invoice.PaymentDetails?.PricingPackageId;
        if (loan.LoanOrigin != LoanOrigin.Factoring || loan.Credit?.Product != ProductType.InHouseCredit ||
            pricingPackageId is null)
        {
            if (loanAppsDictionary.TryGetValue(loan.Id, out var loanApp))
            {
                loanPricingPackageMetadata = loanApp.Metadata?.LoanPackage;
            }
        }
        else
        {
            var loanPricingPackage = await GetLoanPricingPackage(pricingPackageId, CancellationToken.None);
            loanPricingPackageMetadata = loanPricingPackage?.Metadata;
        }

        return loanPricingPackageMetadata;
    }

    private async Task<IDictionary<Guid, LoanApplicationDocument>> GetLoanApplications(
        IReadOnlyCollection<LoanEntity> loans, CancellationToken ctx)
    {
        var loadIds = loans.Select(l => l.Id.ToString()).ToList();
        var loanApp = await loanApplicationRepository.GetByLmsIds(loadIds, ctx);

        return loanApp
            .Where(a => a.LmsId != null && Guid.TryParse(a.LmsId, out _))
            .ToDictionary(a => Guid.Parse(a.LmsId!));
    }

    private async Task<CreditEntity?> GetArAdvanceCredit(LoanEntity loan, CancellationToken ctx)
    {
        var credits = await creditRepository.Get(x => x.Product == ProductType.ARAdvance
                                                      && x.CompanyId == loan.MerchantId
                                                      && (x.Status == LMS.Domain.Enums.CreditStatus.Active
                                                          || x.Status == LMS.Domain.Enums.CreditStatus.PastDue), ctx);

        return credits.FirstOrDefault();
    }

    private async Task<CreditEntity?> GetLineOfCredit(LoanEntity loan, CancellationToken ctx)
    {
        var credits = await creditRepository.Get(x => x.Product == ProductType.LineOfCredit
                                                      && x.CompanyId == loan.CompanyId
                                                      && (x.Status == LMS.Domain.Enums.CreditStatus.Active
                                                          || x.Status == LMS.Domain.Enums.CreditStatus.PastDue), ctx);

        return credits.FirstOrDefault();
    }

    private async Task<LoanPricingPackageDocument?> GetLoanPricingPackage(string? id, CancellationToken ct)
    {
        return string.IsNullOrEmpty(id) ? null : await loanPricingPackageRepository.GetById(id, ct);
    }

    private async Task<IEnumerable<DrawApprovalNoteDto>> ExternalGetDrawApprovalNotes(string? drawApprovalId,
        CancellationToken ctx)
    {
        if (string.IsNullOrEmpty(drawApprovalId)) return Enumerable.Empty<DrawApprovalNoteDto>();

        return await onBoardingHttpClient.GetDrawApprovalNotes(drawApprovalId, ctx);
    }

    private async Task<bool> CalculateFactoringReturningCustomer(string? merchantId, CancellationToken ctx)
    {
        var supplierInHouseCreditLoans = await loanRepository.Get(loan =>
                loan.MerchantId == merchantId &&
                loan.Credit != null &&
                loan.Credit.Product == ProductType.InHouseCredit,
            ctx);

        return supplierInHouseCreditLoans.Count() >= LoanTapeReportConstants.MinimalDrawsCountForReturningCustomer;
    }

    private async Task<bool> CalculateTradeCreditReturningCustomer(string? companyId, CancellationToken ctx)
    {
        var customerDraws = await
            loanRepository.Get(loan =>
                    loan.CompanyId == companyId &&
                    loan.Credit != null &&
                    loan.Credit.Product == ProductType.LineOfCredit,
                ctx);

        return customerDraws.Count() >= LoanTapeReportConstants.MinimalDrawsCountForReturningCustomer;
    }

    private static string GetCompanyNameWithoutDba(LoanEntity loan)
    {
        if (string.IsNullOrEmpty(loan.MerchantName)) return string.Empty;
        return loan.MerchantName.Split("/").FirstOrDefault()?.Trim() ?? string.Empty;
    }

    private static decimal? CalculateCreditOutstandingBalance(CreditEntity? credit, string? projectId)
    {
        if (credit == null) return null;

        var unpaidLoans = string.IsNullOrEmpty(projectId)
            ? credit.Loans.Where(x =>
                    x.Status is LoanStatus.Started || x is { Status: LoanStatus.Created, LoanOrigin: LoanOrigin.Quote })
                .ToList()
            : credit.Loans.Where(x =>
                (x.Status is LoanStatus.Started || x is { Status: LoanStatus.Created, LoanOrigin: LoanOrigin.Quote }) &&
                x.ProjectId == projectId).ToList();
        var unpaidLoansPrincipalBalance = IsInvalidCredit(credit, unpaidLoans)
            ? 0
            : unpaidLoans.Select(GetLoanPrincipalBalance).Sum();
        var processingAmount = IsInvalidCredit(credit, unpaidLoans)
            ? 0
            : unpaidLoans.Select(GetLoanTotalProcessingPaymentsAmount).Sum();

        var result = unpaidLoansPrincipalBalance - processingAmount;

        return result.Round();
    }

    private static bool IsInvalidCredit(CreditEntity credit, List<LoanEntity> unpaidLoans)
    {
        return unpaidLoans.Count == 0 || credit.CreditLimit <= 0.0m;
    }

    private static decimal GetLoanPrincipalBalance(LoanEntity loan)
    {
        var principalBalance = loan.LoanReceivables
            .Where(r => IsReceivableActive(r) &&
                        r.Type is ReceivableType.Payable or ReceivableType.Installment or ReceivableType.LoanFee)
            .Sum(r => r.GetOutstandingAmount());

        return principalBalance;
    }

    private static decimal GetLoanTotalProcessingPaymentsAmount(LoanEntity loan)
    {
        var processingAmount =
            loan.Payments.Where(x => x.Status == PaymentStatus.Processing).Select(x => x.Amount).Sum();

        return processingAmount;
    }

    private static bool IsReceivableActive(LoanReceivableEntity loanReceivable)
    {
        return loanReceivable.Status is LoanReceivableStatus.Pending or LoanReceivableStatus.Late
                   or LoanReceivableStatus.Paid &&
               loanReceivable.ScheduleStatus is ScheduleStatus.Current;
    }
}