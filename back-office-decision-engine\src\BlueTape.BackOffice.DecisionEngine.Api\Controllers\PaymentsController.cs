using BlueTape.BackOffice.DecisionEngine.Api.Constants;
using BlueTape.BackOffice.DecisionEngine.Api.Models.Error;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Payments;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.PaymentApi.Proxy;
using BlueTape.BackOffice.DecisionEngine.Domain.Models;
using BlueTape.OBS.DTOs;
using BlueTape.PaymentService.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net;

namespace BlueTape.BackOffice.DecisionEngine.Api.Controllers;

[ApiController]
[Authorize(Roles = Roles.Admin)]
[Route(Routes.Payment)]
public class PaymentsController(
    IPaymentApiProxy paymentApiProxy,
    IBackofficePaymentService backofficePaymentService)
    : ControllerBase
{
    [ProducesResponseType(typeof(PaymentRequestModel), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
    [HttpGet("{id:guid}")]
    public Task<PaymentRequestModel?> GetById([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        return paymentApiProxy.GetById(id, cancellationToken);
    }

    [ProducesResponseType(typeof(List<AionAccountShort?>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
    [HttpGet("accounts")]
    public Task<List<AionAccountShort?>?> GetAccounts([FromQuery] string? paymentProvider, CancellationToken cancellationToken)
    {
        return paymentApiProxy.GetAccounts(paymentProvider, cancellationToken);
    }

    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
    [ProducesResponseType(typeof(GetQueryWithPaginationResultDto<PaymentResponse>), StatusCodes.Status200OK)]
    [HttpGet("disbursements")]
    public async Task<GetQueryWithPaginationResultDto<PaymentResponse>> GetDisbursements([FromQuery] PaymentRequestFilterQuery query, CancellationToken cancellationToken)
    {
        return await backofficePaymentService.GetDisbursements(query, cancellationToken);
    }

    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
    [ProducesResponseType(typeof(GetQueryWithPaginationResultDto<PaymentResponse>), StatusCodes.Status200OK)]
    [HttpGet("disbursements/availablePaymentMethods")]
    public async Task<string?> GetAvailablePaymentMethods(CancellationToken cancellationToken)
    {
        return await backofficePaymentService.GetAvailablePaymentMethods(cancellationToken);
    }

    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(GetQueryWithPaginationResultDto<PaymentsSubscriptionResponse>), StatusCodes.Status200OK)]
    [HttpGet("subscriptions")]
    public Task<GetQueryWithPaginationResultDto<PaymentsSubscriptionResponse>> GetSubscriptions(
        [FromQuery] SubscriptionPaymentsQuery query,
        CancellationToken cancellationToken)
    {
        return backofficePaymentService.GetSubscriptions(query, cancellationToken);
    }

    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(FileStreamResult), StatusCodes.Status200OK)]
    [HttpGet("subscriptions/export")]
    public Task<FileStreamResult> ExportSubscriptions(
        [FromQuery] DateOnly? from,
        [FromQuery] DateOnly? to,
        CancellationToken cancellationToken)
    {
        var query = new SubscriptionPaymentsQuery
        {
            From = from,
            To = to,
            // Set default values for other properties
            PageNumber = 1,
            PageSize = int.MaxValue,
            SortBy = "createdAt",
            SortOrder = "desc"
        };

        return backofficePaymentService.ExportSubscriptions(query, cancellationToken);
    }

    [ProducesResponseType(typeof(IEnumerable<PaymentRequestModel>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType(typeof(ErrorDto), (int)HttpStatusCode.NotFound)]
    [HttpGet("disbursementQueues")]
    public Task<IEnumerable<PaymentRequestModel>> GetDisbursementQueues(
        [FromQuery] string provider, 
        [FromQuery] string subscriptionCode, 
        CancellationToken cancellationToken)
    {
        return backofficePaymentService.GetDisbursementQueuesPaymentRequests(provider, subscriptionCode, cancellationToken);
    }
}
