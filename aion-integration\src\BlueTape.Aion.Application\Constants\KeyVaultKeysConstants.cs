﻿namespace BlueTape.Aion.Application.Constants;

public class KeyVaultKeysConstants
{
    public const string FundingAccountId = "AION-SPV-DISBURSEMENTS-ACCOUNT-ID";
    public const string CollectionAccountId = "AION-SPV-COLLECTIONS-ACCOUNT-ID";
    public const string AionTransactionReportHighFrequencyMode = "AION-TRANSACTION-REPORT-HIGH-FREQUENCY-MODE";

    public static readonly List<string> SecretNames =
    [
        "NET-AION-APIKEY",
        "APP-INSIGHTS-CONNECTION",
        "AION-REVENUE-ACCOUNT-NUMBER",
        "AION-DISBURSEMENTS-ACCOUNT-NUMBER",
        "AION-COLLECTIONS-ACCOUNT-NUMBER",
        "AION-COLLECTIONS-REPAYMENT-ACCOUNT-NUMBER",
        "AION-COLLECTIONS-REPAYMENT-ACCOUNT-ID",
        "AION-REVENUE-ACCOUNT-ID",
        "AION-DISBURSEMENTS-ACCOUNT-ID",
        "AION-COLLECTIONS-ACCOUNT-ID",
        "AION-USER-ID",
        "AION-PASSWORD",
        "AION-EXTERNAL-API-KEY",
        "AION-PASSWORD2",
        "AION-USER-ID2",
        "AION-EXTERNAL-API-KEY2",
        "AION-PASSWORD3",
        "AION-USER-ID3",
        "AION-EXTERNAL-API-KEY3",
        "MONGO-DB-CONNECTION",
        "AION-AZURE-STORAGE-CONNECTION-STRING",
        "AION-SPV-DISBURSEMENTS-ACCOUNT-ID",
        "AION-SPV-COLLECTIONS-ACCOUNT-ID",
        "AION-TRANSACTION-REPORT-HIGH-FREQUENCY-MODE"
   ];
}
