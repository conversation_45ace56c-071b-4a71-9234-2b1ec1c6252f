﻿using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.Domain.Entities.Base;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.Utilities.Extensions;

namespace BlueTape.PaymentService.Domain.Entities;

public class PaymentRequestEntity : EntityWithId
{
    private decimal _amount;
    public decimal Amount
    {
        get => _amount;
        set => _amount = value.Round();
    }

    private decimal _feeAmount;
    public decimal FeeAmount
    {
        get => _feeAmount;
        set => _feeAmount = value.Round();
    }

    public string Currency { get; set; } = string.Empty;
    public string FlowTemplateCode { get; set; } = null!;
    public DateOnly Date { get; set; }
    public int MerchantAchDelayInBusinessDays { get; set; }
    public DateTime? ExecuteAfter { get; set; }

    public PaymentRequestStatus Status { get; set; }
    public SubjectType SubjectType { get; set; }
    public PaymentRequestType RequestType { get; set; }
    public PaymentMethod PaymentMethod { get; set; }
    public ManualPaymentMethod? ManualPaymentMethod { get; set; }
    public PaymentSubscriptionType PaymentSubscription { get; set; }

    public string? PayerId { get; set; }
    public string? PayeeId { get; set; }
    public string? SellerId { get; set; }

    public Guid? CreditId { get; set; }
    public int SequenceNumber { get; set; } = 0;

    public ICollection<PaymentRequestPayableEntity> PaymentRequestPayables { get; set; } = new List<PaymentRequestPayableEntity>();
    public ICollection<PaymentTransactionEntity> Transactions { get; set; } = new List<PaymentTransactionEntity>();
    public ICollection<PaymentRequestCommandEntity> PaymentRequestCommands { get; set; } = new List<PaymentRequestCommandEntity>();
    public ICollection<PaymentRequestFeeEntity> PaymentRequestFees { get; set; } = new List<PaymentRequestFeeEntity>();
    public ICollection<EventLogEntity> PaymentRequestNotifications { get; set; } = new List<EventLogEntity>();

    public PaymentRequestDetailsEntity? PaymentRequestDetails { get; set; }

    public ConfirmationType ConfirmationType { get; set; }
    public DateTime? ConfirmedAt { get; set; }
    public string? ConfirmedBy { get; set; }

    public string CreatedBy { get; set; } = string.Empty;
    public string UpdatedBy { get; set; } = string.Empty;
}
