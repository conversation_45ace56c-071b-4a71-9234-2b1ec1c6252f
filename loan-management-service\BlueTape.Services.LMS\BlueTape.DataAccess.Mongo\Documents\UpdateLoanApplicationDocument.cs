﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.DataAccess.Mongo.Documents;
public class UpdateLoanApplicationDocument
{
    [BsonElement("lastPaymentDate")]
    public string? LastPaymentDate { get; set; }

    [BsonElement("nextPaymentDate")]
    public string? NextPaymentDate { get; set; }

    [BsonElement("nextPaymentAmount")]
    public double? NextPaymentAmount { get; set; }

    [BsonElement("remainingAmount")]
    public double? RemainingAmount { get; set; }

    [BsonElement("processingAmount")]
    public double? ProcessingAmount { get; set; }

    [BsonElement("pastDueAmount")]
    public double? PastDueAmount { get; set; }
}
