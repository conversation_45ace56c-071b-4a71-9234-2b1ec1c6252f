using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.Domain.Enums;

namespace BlueTape.PaymentService.Domain.Models;

public class PaymentWindow
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public int DurationMinutes { get; set; }
    public TimeSpan StartTime { get; set; }
    public bool IsEnabled { get; set; }

    public List<DayOfWeek> ActiveDays { get; set; } = new();

    public List<PaymentMethod> AllowedPaymentMethods { get; set; } = new();

    public List<PaymentRequestType> AllowedPaymentTypes { get; set; } = new();

    public int Priority { get; set; }
}

public class DefaultPaymentConfig
{
    public List<PaymentMethod> AllowedPaymentMethods { get; set; } = new();

    public Dictionary<PaymentMethod, int> PaymentMethodPriorities { get; set; } = new();
}

public class PaymentWindowConfig
{
    public bool IsPaymentWindowEnabled { get; set; }

    public List<PaymentSubscriptionType> AllowedPaymentSubscriptions { get; set; } = new();

    public List<PaymentRequestType> AffectedPaymentTypes { get; set; } = new();

    public DefaultPaymentConfig DefaultConfig { get; set; } = new();

    public List<PaymentWindow> PaymentWindows { get; set; } = new();

    public DateTime? UpdatedAt { get; set; }
    public string? UpdatedBy { get; set; }
}
