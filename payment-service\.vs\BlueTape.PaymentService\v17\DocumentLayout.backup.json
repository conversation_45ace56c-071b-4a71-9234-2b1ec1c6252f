{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{8F05D09B-E8DB-4C5E-B160-56EAACB84EF4}|tests\\BlueTape.PaymentService.Application.Tests\\BlueTape.PaymentService.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.application.tests\\services\\sequencenumberservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8F05D09B-E8DB-4C5E-B160-56EAACB84EF4}|tests\\BlueTape.PaymentService.Application.Tests\\BlueTape.PaymentService.Application.Tests.csproj|solutionrelative:tests\\bluetape.paymentservice.application.tests\\services\\sequencenumberservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8F05D09B-E8DB-4C5E-B160-56EAACB84EF4}|tests\\BlueTape.PaymentService.Application.Tests\\BlueTape.PaymentService.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.application.tests\\processors\\paymentjobprocessororderingtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8F05D09B-E8DB-4C5E-B160-56EAACB84EF4}|tests\\BlueTape.PaymentService.Application.Tests\\BlueTape.PaymentService.Application.Tests.csproj|solutionrelative:tests\\bluetape.paymentservice.application.tests\\processors\\paymentjobprocessororderingtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\controllers\\admincontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\controllers\\admincontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitTests\\Services\\CustomOrderingConfigServiceTests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\BlueTape.PaymentService.UnitTests\\Services\\CustomOrderingConfigServiceTests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\abstractions\\services\\isequencenumberservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\abstractions\\services\\isequencenumberservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitTests\\Services\\SequenceNumberServiceTests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\BlueTape.PaymentService.UnitTests\\Services\\SequenceNumberServiceTests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitTests\\Processors\\PaymentJobProcessorOrderingTests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\BlueTape.PaymentService.UnitTests\\Processors\\PaymentJobProcessorOrderingTests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\viewmodels\\updatesequenceorderviewmodel.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\viewmodels\\updatesequenceorderviewmodel.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.dataaccess\\repositories\\base\\genericrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|solutionrelative:src\\bluetape.paymentservice.dataaccess\\repositories\\base\\genericrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\middlewares\\exceptionmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\middlewares\\exceptionmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\controllers\\queuescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\controllers\\queuescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.dataaccess\\contexts\\databasecontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|solutionrelative:src\\bluetape.paymentservice.dataaccess\\contexts\\databasecontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\abstractions\\services\\ipaymentqueueservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\abstractions\\services\\ipaymentqueueservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\paymentqueueservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\paymentqueueservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\processors\\paymentjobprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\processors\\paymentjobprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8F05D09B-E8DB-4C5E-B160-56EAACB84EF4}|tests\\BlueTape.PaymentService.Application.Tests\\BlueTape.PaymentService.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.application.tests\\processors\\paymentjobprocessortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8F05D09B-E8DB-4C5E-B160-56EAACB84EF4}|tests\\BlueTape.PaymentService.Application.Tests\\BlueTape.PaymentService.Application.Tests.csproj|solutionrelative:tests\\bluetape.paymentservice.application.tests\\processors\\paymentjobprocessortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.dataaccess\\repositories\\paymentrequestrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|solutionrelative:src\\bluetape.paymentservice.dataaccess\\repositories\\paymentrequestrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{82D143EC-D27D-46A3-A927-BCA12D1FE658}|src\\BlueTape.PaymentService.UnitOfWork\\BlueTape.PaymentService.UnitOfWork.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.unitofwork\\abstractions\\iunitofwork.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{82D143EC-D27D-46A3-A927-BCA12D1FE658}|src\\BlueTape.PaymentService.UnitOfWork\\BlueTape.PaymentService.UnitOfWork.csproj|solutionrelative:src\\bluetape.paymentservice.unitofwork\\abstractions\\iunitofwork.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\paymentwindowservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\paymentwindowservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\paymentconfigservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\paymentconfigservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\controllers\\paymentwindowcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\controllers\\paymentwindowcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\publictransactionnumberservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\publictransactionnumberservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\processors\\rollbackcommandsprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\processors\\rollbackcommandsprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\paymentrequestservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\paymentrequestservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\controllers\\queueeventscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\controllers\\queueeventscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\docs\\PaymentMethodEnumMigration.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:docs\\PaymentMethodEnumMigration.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\mixedcasestests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\mixedcasestests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8F05D09B-E8DB-4C5E-B160-56EAACB84EF4}|tests\\BlueTape.PaymentService.Application.Tests\\BlueTape.PaymentService.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.application.tests\\models\\paymentrequestgroupings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8F05D09B-E8DB-4C5E-B160-56EAACB84EF4}|tests\\BlueTape.PaymentService.Application.Tests\\BlueTape.PaymentService.Application.Tests.csproj|solutionrelative:tests\\bluetape.paymentservice.application.tests\\models\\paymentrequestgroupings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\docs\\MultiplePaymentWindows.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:docs\\MultiplePaymentWindows.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\abstractions\\services\\external\\iaionservicev2.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\abstractions\\services\\external\\iaionservicev2.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\viewmodels\\paymentrequestviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\viewmodels\\paymentrequestviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\controllers\\testingcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\controllers\\testingcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\controllers\\paymentrequestscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\controllers\\paymentrequestscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\controllers\\aiondailylimitscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\controllers\\aiondailylimitscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\CopilotBaseline\\~PaymentRequestsController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\CopilotBaseline\\~DependencyRegistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\controllers\\emailnotificationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\controllers\\emailnotificationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\controllers\\companiescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\controllers\\companiescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\controllers\\helpercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\controllers\\helpercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\abstractions\\processors\\ipaymentjobprocessor.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\abstractions\\processors\\ipaymentjobprocessor.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{FD4867FB-3CFB-4127-B238-2781431F0E32}|src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\BlueTape.PaymentService.PaymentFlowTemplatesEngine.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.paymentflowtemplatesengine\\paymentflowtemplatesengine.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD4867FB-3CFB-4127-B238-2781431F0E32}|src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\BlueTape.PaymentService.PaymentFlowTemplatesEngine.csproj|solutionrelative:src\\bluetape.paymentservice.paymentflowtemplatesengine\\paymentflowtemplatesengine.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\models\\paymentwindowconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\models\\paymentwindowconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\paymentjobprocessorpaymentwindowtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\paymentjobprocessorpaymentwindowtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\728b2af7030607f29913fa27b17da547131b49c824e9b9b611ae5df747609526\\ShouldlyCoreExtensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{82D143EC-D27D-46A3-A927-BCA12D1FE658}|src\\BlueTape.PaymentService.UnitOfWork\\BlueTape.PaymentService.UnitOfWork.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.unitofwork\\repositories\\uowgenericrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{82D143EC-D27D-46A3-A927-BCA12D1FE658}|src\\BlueTape.PaymentService.UnitOfWork\\BlueTape.PaymentService.UnitOfWork.csproj|solutionrelative:src\\bluetape.paymentservice.unitofwork\\repositories\\uowgenericrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.dataaccess\\repositories\\paymentrequestcommandrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|solutionrelative:src\\bluetape.paymentservice.dataaccess\\repositories\\paymentrequestcommandrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\fakes\\genericfakemessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\fakes\\genericfakemessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\transactionstatusupdateservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\transactionstatusupdateservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\paymenttransactionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\paymenttransactionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8F05D09B-E8DB-4C5E-B160-56EAACB84EF4}|tests\\BlueTape.PaymentService.Application.Tests\\BlueTape.PaymentService.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.application.tests\\handlers\\collectfromborrowertransactionhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8F05D09B-E8DB-4C5E-B160-56EAACB84EF4}|tests\\BlueTape.PaymentService.Application.Tests\\BlueTape.PaymentService.Application.Tests.csproj|solutionrelative:tests\\bluetape.paymentservice.application.tests\\handlers\\collectfromborrowertransactionhandlertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\handlers\\commands\\paymentcommands\\aionbasehandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\handlers\\commands\\paymentcommands\\aionbasehandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\mockedservices.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\mockedservices.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\handlers\\commands\\paymentcommands\\pushtomerchantadvanceamounttransactionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\handlers\\commands\\paymentcommands\\pushtomerchantadvanceamounttransactionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\handlers\\commands\\paymentcommands\\pushtomerchantfinalamounttransactionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\handlers\\commands\\paymentcommands\\pushtomerchantfinalamounttransactionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\external\\aionservicev2.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\external\\aionservicev2.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\handlers\\commands\\paymentcommands\\pushtomerchantfullamounttransactionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\handlers\\commands\\paymentcommands\\pushtomerchantfullamounttransactionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\commandmanagementservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\commandmanagementservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\constants\\configuration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\constants\\configuration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\paymentrequestcommandservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\paymentrequestcommandservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD4867FB-3CFB-4127-B238-2781431F0E32}|src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\BlueTape.PaymentService.PaymentFlowTemplatesEngine.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.paymentflowtemplatesengine\\constants\\paymentflowtemplatesengineconstants.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD4867FB-3CFB-4127-B238-2781431F0E32}|src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\BlueTape.PaymentService.PaymentFlowTemplatesEngine.csproj|solutionrelative:src\\bluetape.paymentservice.paymentflowtemplatesengine\\constants\\paymentflowtemplatesengineconstants.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\rollbacksintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\rollbacksintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\handlers\\events\\transaction\\transactionrecalledeventhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\handlers\\events\\transaction\\transactionrecalledeventhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\processors\\transactionstatusupdateprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\processors\\transactionstatusupdateprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\transactionstatusupdatehistorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\transactionstatusupdatehistorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\happypathintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\happypathintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\mappers\\statusservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\mappers\\statusservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\failedcasestests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\failedcasestests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\availablebalancetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\availablebalancetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\appsettings.integration-test.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\appsettings.integration-test.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\appsettings.dev.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\appsettings.dev.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\docs\\PaymentWindowFeature.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:docs\\PaymentWindowFeature.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\controllertests\\paymentrequestcontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\controllertests\\paymentrequestcontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{07E88325-2218-4CCC-B61A-C746BBE8C0D1}|src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\functions\\bluetape.functions.paymentjob\\transactionstatusupdateconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{07E88325-2218-4CCC-B61A-C746BBE8C0D1}|src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj|solutionrelative:src\\functions\\bluetape.functions.paymentjob\\transactionstatusupdateconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.UnitTests\\Services\\PaymentWindowServiceTests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:tests\\BlueTape.PaymentService.UnitTests\\Services\\PaymentWindowServiceTests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\testdata\\paymentrequestdata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\testdata\\paymentrequestdata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\processors\\notificationprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\processors\\notificationprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\fakes\\fakepaymentflowservicemessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\fakes\\fakepaymentflowservicemessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\fakes\\testazurestoragedbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\fakes\\testazurestoragedbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\fakes\\faketransactionstatusmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\fakes\\faketransactionstatusmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\fakes\\fakenotificationmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\fakes\\fakenotificationmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\fakes\\fakeazurerepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\fakes\\fakeazurerepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\pausedpaymentrequeststests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\pausedpaymentrequeststests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\postgresdatabasetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\postgresdatabasetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\eligiblebalancefiltrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\paymentflowtests\\eligiblebalancefiltrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\models\\aiondailylimitconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\models\\aiondailylimitconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\models\\retrypolicysettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\models\\retrypolicysettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\basepaymentrequestcreationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\basepaymentrequestcreationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD4867FB-3CFB-4127-B238-2781431F0E32}|src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\BlueTape.PaymentService.PaymentFlowTemplatesEngine.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.paymentflowtemplatesengine\\models\\flowtemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FD4867FB-3CFB-4127-B238-2781431F0E32}|src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\BlueTape.PaymentService.PaymentFlowTemplatesEngine.csproj|solutionrelative:src\\bluetape.paymentservice.paymentflowtemplatesengine\\models\\flowtemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\strategies\\lineofcreditdisbursementstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\strategies\\lineofcreditdisbursementstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\strategies\\factoringdisbursementstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\strategies\\factoringdisbursementstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\strategies\\ihcrepaymentstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\strategies\\ihcrepaymentstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.dataaccess\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|solutionrelative:src\\bluetape.paymentservice.dataaccess\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\notification\\transactionnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\notification\\transactionnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\notification\\requesttypes\\invoicepaymentv2notificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\notification\\requesttypes\\invoicepaymentv2notificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\notification\\emailnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\notification\\emailnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\abstractions\\services\\notification\\iemailnotificationservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\abstractions\\services\\notification\\iemailnotificationservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\notification\\requesttypes\\invoicepaymentnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\notification\\requesttypes\\invoicepaymentnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\notification\\notificationreceiversservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\notification\\notificationreceiversservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\helpers\\transactionhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\helpers\\transactionhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\notification\\requesttypes\\invoicedisbursementv2notificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\notification\\requesttypes\\invoicedisbursementv2notificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\notification\\slacknotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\notification\\slacknotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\abstractions\\services\\notification\\itransactionnotificationservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\abstractions\\services\\notification\\itransactionnotificationservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\notification\\requesttypes\\finalpaymentnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\notification\\requesttypes\\finalpaymentnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\handlers\\events\\transaction\\transactionfailedorerroreventhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\handlers\\events\\transaction\\transactionfailedorerroreventhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\extensions\\helpers.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\extensions\\helpers.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\invoicepayment\\invoicepaymentoperationssyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\invoicepayment\\invoicepaymentoperationssyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\extensions\\enummapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\extensions\\enummapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\drawrepaymentmanual\\drawrepaymentmanualoperationsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\drawrepaymentmanual\\drawrepaymentmanualoperationsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\invoicepaymentv2\\invoicepaymentv2operationssyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\invoicepaymentv2\\invoicepaymentv2operationssyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\factoringfinalpayment\\factoringfinalpaymentoperationsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\factoringfinalpayment\\factoringfinalpaymentoperationsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\finalpayment\\finalpaymentoperationsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\finalpayment\\finalpaymentoperationsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\base\\baseoperationsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\base\\baseoperationsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\senders\\invoicesyncmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\senders\\invoicesyncmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\drawdisbursement\\drawdisbursementoperationsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\drawdisbursement\\drawdisbursementoperationsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\abstractions\\senders\\iinvoicesyncmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\abstractions\\senders\\iinvoicesyncmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{07E88325-2218-4CCC-B61A-C746BBE8C0D1}|src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\functions\\bluetape.functions.paymentjob\\paymentrequestconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{07E88325-2218-4CCC-B61A-C746BBE8C0D1}|src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj|solutionrelative:src\\functions\\bluetape.functions.paymentjob\\paymentrequestconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\drawrepayments\\drawrepaymentsoperationsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\drawrepayments\\drawrepaymentsoperationsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\drawrepaymentmanual\\drawrepaymentmanualtransactionsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\drawrepaymentmanual\\drawrepaymentmanualtransactionsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\abstractions\\service\\drawrepaymentmanual\\idrawrepaymentmanualtransactionsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\abstractions\\service\\drawrepaymentmanual\\idrawrepaymentmanualtransactionsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\abstractions\\service\\drawrepaymentmanual\\idrawrepaymentmanualoperationsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\abstractions\\service\\drawrepaymentmanual\\idrawrepaymentmanualoperationsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\drawrepayments\\drawrepaymentscompatibilitymapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\drawrepayments\\drawrepaymentscompatibilitymapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\invoicepayment\\invoicepaymentcompatibilitymapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\invoicepayment\\invoicepaymentcompatibilitymapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\invoicepaymentrequestcreationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\invoicepaymentrequestcreationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\strategies\\subscriptionfeestrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\strategies\\subscriptionfeestrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\strategies\\invoicepaymentstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\strategies\\invoicepaymentstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\invoicedisbursementv2requestcreationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\invoicedisbursementv2requestcreationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\viewmodels\\paymentrequestdetailsviewmodel.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\viewmodels\\paymentrequestdetailsviewmodel.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\extensions\\decimalextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\extensions\\decimalextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.dataaccess\\migrations\\20250605073536_addpaymentconfigtable.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|solutionrelative:src\\bluetape.paymentservice.dataaccess\\migrations\\20250605073536_addpaymentconfigtable.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.dataaccess\\migrations\\databasecontextmodelsnapshot.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|solutionrelative:src\\bluetape.paymentservice.dataaccess\\migrations\\databasecontextmodelsnapshot.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.dataaccess\\repositories\\paymentrequestpayablerepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|solutionrelative:src\\bluetape.paymentservice.dataaccess\\repositories\\paymentrequestpayablerepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\mappers\\apiprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\mappers\\apiprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\controllers\\operationscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\controllers\\operationscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\controllers\\loanpaymentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\controllers\\loanpaymentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8F05D09B-E8DB-4C5E-B160-56EAACB84EF4}|tests\\BlueTape.PaymentService.Application.Tests\\BlueTape.PaymentService.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.application.tests\\services\\loanmanagementservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8F05D09B-E8DB-4C5E-B160-56EAACB84EF4}|tests\\BlueTape.PaymentService.Application.Tests\\BlueTape.PaymentService.Application.Tests.csproj|solutionrelative:tests\\bluetape.paymentservice.application.tests\\services\\loanmanagementservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\loanmanagementservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\loanmanagementservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\abstractions\\services\\iloanmanagementservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\abstractions\\services\\iloanmanagementservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\messages\\paymentrequestmessage\\drawrepayment\\drawrepaymentrequestmessage.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\messages\\paymentrequestmessage\\drawrepayment\\drawrepaymentrequestmessage.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\messages\\paymentrequestmessage\\drawrepaymentcard\\drawrepaymentcardrequestmessage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\messages\\paymentrequestmessage\\drawrepaymentcard\\drawrepaymentcardrequestmessage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D8333A12-E7A6-4525-B56E-FCFB6EE805FD}|src\\BlueTape.PaymentService.DataAccess.Mongo\\BlueTape.PaymentService.DataAccess.Mongo.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.dataaccess.mongo\\repositories\\transactionsrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D8333A12-E7A6-4525-B56E-FCFB6EE805FD}|src\\BlueTape.PaymentService.DataAccess.Mongo\\BlueTape.PaymentService.DataAccess.Mongo.csproj|solutionrelative:src\\bluetape.paymentservice.dataaccess.mongo\\repositories\\transactionsrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\drawrepayments\\drawrepaymentstransactionsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\drawrepayments\\drawrepaymentstransactionsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\invoicepaymentv2\\invoicepaymentv2transactionssyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\invoicepaymentv2\\invoicepaymentv2transactionssyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{07E88325-2218-4CCC-B61A-C746BBE8C0D1}|src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\functions\\bluetape.functions.paymentjob\\transactionstatusupdatejobfunction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{07E88325-2218-4CCC-B61A-C746BBE8C0D1}|src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj|solutionrelative:src\\functions\\bluetape.functions.paymentjob\\transactionstatusupdatejobfunction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\enums\\paymentrequesttype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\enums\\paymentrequesttype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\enums\\paymentrequeststatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\enums\\paymentrequeststatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\enums\\eventtype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\enums\\eventtype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\enums\\emailtemplatename.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\enums\\emailtemplatename.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\enums\\bluetapepaymenterrorcodes.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\enums\\bluetapepaymenterrorcodes.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\enums\\confirmationtype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\enums\\confirmationtype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\enums\\finalpaymentstatusfornotification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\enums\\finalpaymentstatusfornotification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\enums\\paymentprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\enums\\paymentprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\enums\\paymentpausereason.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\enums\\paymentpausereason.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\enums\\payabletype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\enums\\payabletype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\notification\\requesttypes\\drawdisbursementnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\notification\\requesttypes\\drawdisbursementnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\notification\\requesttypes\\drawrepaymentnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\notification\\requesttypes\\drawrepaymentnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\handlers\\events\\paymentrequest\\paymentrequestsettledhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\handlers\\events\\paymentrequest\\paymentrequestsettledhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\invoicepaymentv2requestcreationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\invoicepaymentv2requestcreationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\invoicepaymentcardrequestcreationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\invoicepaymentcardrequestcreationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\external\\ledger\\ledgerservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\external\\ledger\\ledgerservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8F05D09B-E8DB-4C5E-B160-56EAACB84EF4}|tests\\BlueTape.PaymentService.Application.Tests\\BlueTape.PaymentService.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.application.tests\\services\\invoicepaymentrequestcreationservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8F05D09B-E8DB-4C5E-B160-56EAACB84EF4}|tests\\BlueTape.PaymentService.Application.Tests\\BlueTape.PaymentService.Application.Tests.csproj|solutionrelative:tests\\bluetape.paymentservice.application.tests\\services\\invoicepaymentrequestcreationservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\abstractions\\services\\external\\ledger\\iledgerservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\abstractions\\services\\external\\ledger\\iledgerservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{07E88325-2218-4CCC-B61A-C746BBE8C0D1}|src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\functions\\bluetape.functions.paymentjob\\operationsyncconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{07E88325-2218-4CCC-B61A-C746BBE8C0D1}|src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj|solutionrelative:src\\functions\\bluetape.functions.paymentjob\\operationsyncconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{07E88325-2218-4CCC-B61A-C746BBE8C0D1}|src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\functions\\bluetape.functions.paymentjob\\paymentjobfunction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{07E88325-2218-4CCC-B61A-C746BBE8C0D1}|src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj|solutionrelative:src\\functions\\bluetape.functions.paymentjob\\paymentjobfunction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\handlers\\events\\paymentrequest\\paymentrequestfailedhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\handlers\\events\\paymentrequest\\paymentrequestfailedhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\f2a8da854073706fa2071257656390f3499c628218273ea440667f952a1e4baa\\AsyncMethodBuilderCore.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\f2256876427a059b9953c63da8b5ed989c4c449b3c6605ccdb8777c9e3a4ab0f\\ValueTask.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\436f8735f9701c121e542e705b8002c28cf053641f7e971e41e47cab32739fae\\DiagnosticsHandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\df41063b75a98ed4d94a347657623436de4c396d99df97d1012994f374b271d0\\PoolingAsyncValueTaskMethodBuilder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\4dcf567db33b8407e8e7e85491df71d46c06985d33f5ad8d904e52c3b325c6c5\\ExecutionContext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\e8dbc30d26a5a1ebfab4248acc30b41298aa139e054d44f23a4aefc8a3eead43\\HttpConnectionPool.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\controllers\\transactionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\controllers\\transactionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\handlers\\events\\transaction\\transactionprocessingeventhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\handlers\\events\\transaction\\transactionprocessingeventhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\drawdisbursement\\drawdisbursementcompatibilitymapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\drawdisbursement\\drawdisbursementcompatibilitymapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\drawdisbursement\\drawdisbursementtransactionsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\drawdisbursement\\drawdisbursementtransactionsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D8333A12-E7A6-4525-B56E-FCFB6EE805FD}|src\\BlueTape.PaymentService.DataAccess.Mongo\\BlueTape.PaymentService.DataAccess.Mongo.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.dataaccess.mongo\\repositories\\userrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D8333A12-E7A6-4525-B56E-FCFB6EE805FD}|src\\BlueTape.PaymentService.DataAccess.Mongo\\BlueTape.PaymentService.DataAccess.Mongo.csproj|solutionrelative:src\\bluetape.paymentservice.dataaccess.mongo\\repositories\\userrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\strategies\\invoicepaymentv2compatibilitystrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\strategies\\invoicepaymentv2compatibilitystrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\messages\\paymentrequestmessage\\drawrepayment\\drawrepaymentrequestdetails.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\messages\\paymentrequestmessage\\drawrepayment\\drawrepaymentrequestdetails.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{07E88325-2218-4CCC-B61A-C746BBE8C0D1}|src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\functions\\bluetape.functions.paymentjob\\settlementreportjob.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{07E88325-2218-4CCC-B61A-C746BBE8C0D1}|src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj|solutionrelative:src\\functions\\bluetape.functions.paymentjob\\settlementreportjob.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\validators\\createpaymentrequestviewmodelvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\validators\\createpaymentrequestviewmodelvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\validators\\createpaymentrequesttransactionviewmodelvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\validators\\createpaymentrequesttransactionviewmodelvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\validators\\createpaymentrequestpayableviewmodelvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\validators\\createpaymentrequestpayableviewmodelvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\validators\\createpaymentrequestfeeviewmodelvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\validators\\createpaymentrequestfeeviewmodelvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\models\\createlegacytransactionmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\models\\createlegacytransactionmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\viewmodels\\paymenttransactionviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\viewmodels\\paymenttransactionviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\drawrepaymentcard\\drawrepaymentcardcompatibilitymapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\drawrepaymentcard\\drawrepaymentcardcompatibilitymapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\constants\\keyvaultkeysconstants.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\constants\\keyvaultkeysconstants.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\paymentcompatibilityservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\paymentcompatibilityservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\di\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\di\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\notification\\requesttypes\\basenotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\notification\\requesttypes\\basenotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\appsettings.beta.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\appsettings.beta.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\notification\\requesttypes\\factoringdisbursementnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\notification\\requesttypes\\factoringdisbursementnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\notification\\requesttypes\\factoringfinalpaymentnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\notification\\requesttypes\\factoringfinalpaymentnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\handlers\\events\\paymentrequest\\basepaymentrequesthandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\handlers\\events\\paymentrequest\\basepaymentrequesthandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\finalpayment\\finalpaymenttransactionsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\finalpayment\\finalpaymenttransactionsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\services\\finalpayment\\finalpaymentcompatibilitymapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\services\\finalpayment\\finalpaymentcompatibilitymapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\6e0a018c11d0808fa6a9b0941636abc259f34e9b11938460a4bb00d5a256d364\\TaskCompletionSourceWithCancellation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\943866804c986b3f1c185b0aaf5a6b2eb778d67c9141c5459bf5f86981d49a11\\AsyncTaskMethodBuilder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\45558abf292363b6260c2ac82d509821cf8b7db701ef41c521afd31f6e2fa9a1\\FileSystem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{07E88325-2218-4CCC-B61A-C746BBE8C0D1}|src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\functions\\bluetape.functions.paymentjob\\bluetape.functions.paymentjob.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{07E88325-2218-4CCC-B61A-C746BBE8C0D1}|src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj|solutionrelative:src\\functions\\bluetape.functions.paymentjob\\bluetape.functions.paymentjob.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\controllertests\\helpercontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\controllertests\\helpercontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\globalusings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\globalusings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\bluetape.paymentservice.application.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\bluetape.paymentservice.application.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.integrationtests\\bluetape.paymentservice.integrationtests.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{6AE3A519-5076-4F99-BF61-4734885B7029}|tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj|solutionrelative:tests\\bluetape.paymentservice.integrationtests\\bluetape.paymentservice.integrationtests.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\3d191bc8662e70a19deca42370d7982b8547e79362a27948a42aa9df305a69f5\\MethodBaseInvoker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{07E88325-2218-4CCC-B61A-C746BBE8C0D1}|src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\functions\\bluetape.functions.paymentjob\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{07E88325-2218-4CCC-B61A-C746BBE8C0D1}|src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj|solutionrelative:src\\functions\\bluetape.functions.paymentjob\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{F687C990-3C5D-489E-97E7-DB0C80355D62}|tests\\BlueTape.PaymentService.CompatibilityService.Tests\\BlueTape.PaymentService.CompatibilityService.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.compatibilityservice.tests\\services\\drawdisbursement\\drawdisbursementcompatibilitymappertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F687C990-3C5D-489E-97E7-DB0C80355D62}|tests\\BlueTape.PaymentService.CompatibilityService.Tests\\BlueTape.PaymentService.CompatibilityService.Tests.csproj|solutionrelative:tests\\bluetape.paymentservice.compatibilityservice.tests\\services\\drawdisbursement\\drawdisbursementcompatibilitymappertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\c59c6e0a029705cef818236b76b5baf2af9cc664d8358ac2c57af0271a1c7d11\\ObjectGraphTestExtensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F687C990-3C5D-489E-97E7-DB0C80355D62}|tests\\BlueTape.PaymentService.CompatibilityService.Tests\\BlueTape.PaymentService.CompatibilityService.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.compatibilityservice.tests\\services\\drawdisbursement\\drawdisbursementtransactionsyncservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F687C990-3C5D-489E-97E7-DB0C80355D62}|tests\\BlueTape.PaymentService.CompatibilityService.Tests\\BlueTape.PaymentService.CompatibilityService.Tests.csproj|solutionrelative:tests\\bluetape.paymentservice.compatibilityservice.tests\\services\\drawdisbursement\\drawdisbursementtransactionsyncservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.dataaccess\\repositories\\eventlogrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|solutionrelative:src\\bluetape.paymentservice.dataaccess\\repositories\\eventlogrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.dataaccess\\repositories\\paymentrequestdetailsrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|solutionrelative:src\\bluetape.paymentservice.dataaccess\\repositories\\paymentrequestdetailsrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.dataaccess\\repositories\\paymenttransactionrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|solutionrelative:src\\bluetape.paymentservice.dataaccess\\repositories\\paymenttransactionrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.dataaccess\\repositories\\paymenttransactionhistoryrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|solutionrelative:src\\bluetape.paymentservice.dataaccess\\repositories\\paymenttransactionhistoryrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.dataaccess\\repositories\\sequencesrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|solutionrelative:src\\bluetape.paymentservice.dataaccess\\repositories\\sequencesrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\companyservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\companyservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{64113D58-460C-482A-9BE5-346438B0D71A}|src\\BlueTape.Reporting.Application\\BlueTape.Reporting.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.reporting.application\\services\\reportingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{64113D58-460C-482A-9BE5-346438B0D71A}|src\\BlueTape.Reporting.Application\\BlueTape.Reporting.Application.csproj|solutionrelative:src\\bluetape.reporting.application\\services\\reportingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\handlers\\events\\transaction\\basetransactioneventhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\handlers\\events\\transaction\\basetransactioneventhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\handlers\\events\\transaction\\transactionclearedeventhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\handlers\\events\\transaction\\transactionclearedeventhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\mappers\\messageprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\mappers\\messageprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711E1C50-F3C8-41EC-AE50-56060A1A02E1}|src\\BlueTape.PaymentService.TestUtilities\\BlueTape.PaymentService.TestUtilities.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.testutilities\\services\\testingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711E1C50-F3C8-41EC-AE50-56060A1A02E1}|src\\BlueTape.PaymentService.TestUtilities\\BlueTape.PaymentService.TestUtilities.csproj|solutionrelative:src\\bluetape.paymentservice.testutilities\\services\\testingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711E1C50-F3C8-41EC-AE50-56060A1A02E1}|src\\BlueTape.PaymentService.TestUtilities\\BlueTape.PaymentService.TestUtilities.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.testutilities\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711E1C50-F3C8-41EC-AE50-56060A1A02E1}|src\\BlueTape.PaymentService.TestUtilities\\BlueTape.PaymentService.TestUtilities.csproj|solutionrelative:src\\bluetape.paymentservice.testutilities\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711E1C50-F3C8-41EC-AE50-56060A1A02E1}|src\\BlueTape.PaymentService.TestUtilities\\BlueTape.PaymentService.TestUtilities.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.testutilities\\abstractions\\senders\\idrawrepaymentmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711E1C50-F3C8-41EC-AE50-56060A1A02E1}|src\\BlueTape.PaymentService.TestUtilities\\BlueTape.PaymentService.TestUtilities.csproj|solutionrelative:src\\bluetape.paymentservice.testutilities\\abstractions\\senders\\idrawrepaymentmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711E1C50-F3C8-41EC-AE50-56060A1A02E1}|src\\BlueTape.PaymentService.TestUtilities\\BlueTape.PaymentService.TestUtilities.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.testutilities\\abstractions\\senders\\iinvoicepaymentmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711E1C50-F3C8-41EC-AE50-56060A1A02E1}|src\\BlueTape.PaymentService.TestUtilities\\BlueTape.PaymentService.TestUtilities.csproj|solutionrelative:src\\bluetape.paymentservice.testutilities\\abstractions\\senders\\iinvoicepaymentmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{711E1C50-F3C8-41EC-AE50-56060A1A02E1}|src\\BlueTape.PaymentService.TestUtilities\\BlueTape.PaymentService.TestUtilities.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.testutilities\\senders\\drawrepaymentrequestmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{711E1C50-F3C8-41EC-AE50-56060A1A02E1}|src\\BlueTape.PaymentService.TestUtilities\\BlueTape.PaymentService.TestUtilities.csproj|solutionrelative:src\\bluetape.paymentservice.testutilities\\senders\\drawrepaymentrequestmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\messages\\paymentrequestmessage\\invoicepayment\\invoicepaymentrequestdetails.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\messages\\paymentrequestmessage\\invoicepayment\\invoicepaymentrequestdetails.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\messages\\paymentrequestmessage\\drawrepayment\\drawrepaymentdrawdetails.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\messages\\paymentrequestmessage\\drawrepayment\\drawrepaymentdrawdetails.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\messages\\paymentrequestmessage\\invoicepaymentv2\\invoicepaymentv2requestmessage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\messages\\paymentrequestmessage\\invoicepaymentv2\\invoicepaymentv2requestmessage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\validators\\paymentrequestvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\validators\\paymentrequestvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\drawdisbursementrequestcreatorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\paymentrequestcreation\\drawdisbursementrequestcreatorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\transactionnumberservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\transactionnumberservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8F05D09B-E8DB-4C5E-B160-56EAACB84EF4}|tests\\BlueTape.PaymentService.Application.Tests\\BlueTape.PaymentService.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.application.tests\\services\\paymentrequestcommandservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8F05D09B-E8DB-4C5E-B160-56EAACB84EF4}|tests\\BlueTape.PaymentService.Application.Tests\\BlueTape.PaymentService.Application.Tests.csproj|solutionrelative:tests\\bluetape.paymentservice.application.tests\\services\\paymentrequestcommandservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8F05D09B-E8DB-4C5E-B160-56EAACB84EF4}|tests\\BlueTape.PaymentService.Application.Tests\\BlueTape.PaymentService.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\tests\\bluetape.paymentservice.application.tests\\services\\finalpaymentrequestcreationservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8F05D09B-E8DB-4C5E-B160-56EAACB84EF4}|tests\\BlueTape.PaymentService.Application.Tests\\BlueTape.PaymentService.Application.Tests.csproj|solutionrelative:tests\\bluetape.paymentservice.application.tests\\services\\finalpaymentrequestcreationservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\46ce419f48f1441bbef0fd8674d636b34800\\3f\\11e66c54\\LoanServiceHttpClient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\services\\external\\aionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\services\\external\\aionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\handlers\\commands\\paymentcommands\\collectfromborrowertransactionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\handlers\\commands\\paymentcommands\\collectfromborrowertransactionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\handlers\\commands\\paymentcommands\\collectfrommerchanttransactionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\handlers\\commands\\paymentcommands\\collectfrommerchanttransactionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\handlers\\commands\\paymentcommands\\collecttofundingtransactionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\handlers\\commands\\paymentcommands\\collecttofundingtransactionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\handlers\\commands\\paymentcommands\\collectlockboxordacatransactionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\handlers\\commands\\paymentcommands\\collectlockboxordacatransactionhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.compatibilityservice\\strategies\\factoringfinalpaymentcompatibilitystrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D98F0424-9983-441D-9032-E3EDEB880194}|src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj|solutionrelative:src\\bluetape.paymentservice.compatibilityservice\\strategies\\factoringfinalpaymentcompatibilitystrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\viewmodels\\createinvoicerequest.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\viewmodels\\createinvoicerequest.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{711E1C50-F3C8-41EC-AE50-56060A1A02E1}|src\\BlueTape.PaymentService.TestUtilities\\BlueTape.PaymentService.TestUtilities.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.testutilities\\abstractions\\services\\itestingservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{711E1C50-F3C8-41EC-AE50-56060A1A02E1}|src\\BlueTape.PaymentService.TestUtilities\\BlueTape.PaymentService.TestUtilities.csproj|solutionrelative:src\\bluetape.paymentservice.testutilities\\abstractions\\services\\itestingservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.domain\\entities\\filters\\paymentrequestfilter.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{217FB2FD-7046-462F-8031-4EBCEDD8B05A}|src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj|solutionrelative:src\\bluetape.paymentservice.domain\\entities\\filters\\paymentrequestfilter.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\queries\\paymentrequestfilterquery.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\queries\\paymentrequestfilterquery.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.api\\viewmodels\\pausepaymentrequestviewmodel.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{159A3759-59A8-4836-B1E8-0B86EE59B1B3}|src\\BlueTape.PaymentService.API\\BlueTape.PaymentService.API.csproj|solutionrelative:src\\bluetape.paymentservice.api\\viewmodels\\pausepaymentrequestviewmodel.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.application\\abstractions\\services\\notification\\inotificationreceiversservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{2A4F5EE7-099C-4C41-9A82-951DE62EFE9D}|src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj|solutionrelative:src\\bluetape.paymentservice.application\\abstractions\\services\\notification\\inotificationreceiversservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\payment-service\\src\\bluetape.paymentservice.dataaccess\\abstractions\\repositories\\ipaymentrequestrepository.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{B148834A-FC45-42B6-AF60-BD2A0470B9E3}|src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj|solutionrelative:src\\bluetape.paymentservice.dataaccess\\abstractions\\repositories\\ipaymentrequestrepository.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 15, "Title": "PaymentJobProcessor.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Processors\\PaymentJobProcessor.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Processors\\PaymentJobProcessor.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Processors\\PaymentJobProcessor.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Processors\\PaymentJobProcessor.cs", "ViewState": "AgIAADgBAAAAAAAAAAAAAE8BAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T16:24:31.283Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "SequenceNumberServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.Application.Tests\\Services\\SequenceNumberServiceTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.Application.Tests\\Services\\SequenceNumberServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.Application.Tests\\Services\\SequenceNumberServiceTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.Application.Tests\\Services\\SequenceNumberServiceTests.cs", "ViewState": "AgIAAMMAAAAAAAAAAAAIwJcAAAAKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T08:24:40.815Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "CustomOrderingConfigServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitTests\\Services\\CustomOrderingConfigServiceTests.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.UnitTests\\Services\\CustomOrderingConfigServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitTests\\Services\\CustomOrderingConfigServiceTests.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.UnitTests\\Services\\CustomOrderingConfigServiceTests.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAACAAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T08:18:17.248Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\appsettings.json", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\appsettings.json", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\appsettings.json", "ViewState": "AgIAAI0CAAAAAAAAAAAAAN4AAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-24T09:49:28.929Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "PaymentJobProcessorOrderingTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.Application.Tests\\Processors\\PaymentJobProcessorOrderingTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.Application.Tests\\Processors\\PaymentJobProcessorOrderingTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.Application.Tests\\Processors\\PaymentJobProcessorOrderingTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.Application.Tests\\Processors\\PaymentJobProcessorOrderingTests.cs", "ViewState": "AgIAACgAAAAAAAAAAAAgwA8AAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T08:32:45.017Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ISequenceNumberService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\ISequenceNumberService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\ISequenceNumberService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\ISequenceNumberService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\ISequenceNumberService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T21:18:23.47Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "AdminController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\AdminController.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Controllers\\AdminController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\AdminController.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Controllers\\AdminController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-19T06:25:37.366Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "SequenceNumberServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitTests\\Services\\SequenceNumberServiceTests.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.UnitTests\\Services\\SequenceNumberServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitTests\\Services\\SequenceNumberServiceTests.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.UnitTests\\Services\\SequenceNumberServiceTests.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAACMAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T14:33:44Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "PaymentJobProcessorOrderingTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitTests\\Processors\\PaymentJobProcessorOrderingTests.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.UnitTests\\Processors\\PaymentJobProcessorOrderingTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitTests\\Processors\\PaymentJobProcessorOrderingTests.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.UnitTests\\Processors\\PaymentJobProcessorOrderingTests.cs", "ViewState": "AgIAABIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T14:33:46.819Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "UpdateSequenceOrderViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\ViewModels\\UpdateSequenceOrderViewModel.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\ViewModels\\UpdateSequenceOrderViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\ViewModels\\UpdateSequenceOrderViewModel.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\ViewModels\\UpdateSequenceOrderViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T07:57:49.644Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "GenericRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Repositories\\Base\\GenericRepository.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.DataAccess\\Repositories\\Base\\GenericRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Repositories\\Base\\GenericRepository.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.DataAccess\\Repositories\\Base\\GenericRepository.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAtwBYAAABPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T15:22:34.087Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "ExceptionMiddleware.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Middlewares\\ExceptionMiddleware.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Middlewares\\ExceptionMiddleware.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Middlewares\\ExceptionMiddleware.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Middlewares\\ExceptionMiddleware.cs", "ViewState": "AgIAABcAAAAAAAAAAAArwCkAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:32:03.918Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "DatabaseContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Contexts\\DatabaseContext.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.DataAccess\\Contexts\\DatabaseContext.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Contexts\\DatabaseContext.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.DataAccess\\Contexts\\DatabaseContext.cs", "ViewState": "AgIAAAcAAAAAAAAAAAArwBkAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T08:48:59.538Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "QueuesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\QueuesController.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Controllers\\QueuesController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\QueuesController.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Controllers\\QueuesController.cs", "ViewState": "AgIAACIAAAAAAAAAAAAgwDMAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T07:14:56.725Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "IPaymentQueueService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\IPaymentQueueService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\IPaymentQueueService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\IPaymentQueueService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\IPaymentQueueService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAkAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T07:18:55.203Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "PaymentJobProcessorTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.Application.Tests\\Processors\\PaymentJobProcessorTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.Application.Tests\\Processors\\PaymentJobProcessorTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.Application.Tests\\Processors\\PaymentJobProcessorTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.Application.Tests\\Processors\\PaymentJobProcessorTests.cs", "ViewState": "AgIAABkAAAAAAAAAAAAEwCsAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T16:33:19.423Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "PaymentQueueService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentQueueService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\PaymentQueueService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentQueueService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\PaymentQueueService.cs", "ViewState": "AgIAABMAAAAAAAAAAAAgwCkAAABRAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T12:39:53.877Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "PaymentRequestRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentRequestRepository.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentRequestRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentRequestRepository.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentRequestRepository.cs", "ViewState": "AgIAAJYAAAAAAAAAAAAUwBsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T09:40:51.706Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 248, "Title": "IPaymentRequestRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Abstractions\\Repositories\\IPaymentRequestRepository.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.DataAccess\\Abstractions\\Repositories\\IPaymentRequestRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Abstractions\\Repositories\\IPaymentRequestRepository.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.DataAccess\\Abstractions\\Repositories\\IPaymentRequestRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T21:38:29.838Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "IUnitOfWork.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitOfWork\\Abstractions\\IUnitOfWork.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.UnitOfWork\\Abstractions\\IUnitOfWork.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitOfWork\\Abstractions\\IUnitOfWork.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.UnitOfWork\\Abstractions\\IUnitOfWork.cs", "ViewState": "AgIAAFEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T21:21:13.529Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "PaymentWindowService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentWindowService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\PaymentWindowService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentWindowService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\PaymentWindowService.cs", "ViewState": "AgIAACIBAAAAAAAAAAAAAA0BAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T12:57:32.371Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "PaymentConfigService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentConfigService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\PaymentConfigService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentConfigService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\PaymentConfigService.cs", "ViewState": "AgIAAJwAAAAAAAAAAADgv3AAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T09:18:20.565Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "PublicTransactionNumberService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PublicTransactionNumberService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\PublicTransactionNumberService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PublicTransactionNumberService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\PublicTransactionNumberService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T12:10:30.137Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "PaymentWindowController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\PaymentWindowController.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Controllers\\PaymentWindowController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\PaymentWindowController.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Controllers\\PaymentWindowController.cs", "ViewState": "AgIAAEEAAAAAAAAAAAAIwFIAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T07:25:56.823Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "RollbackCommandsProcessor.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Processors\\RollbackCommandsProcessor.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Processors\\RollbackCommandsProcessor.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Processors\\RollbackCommandsProcessor.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Processors\\RollbackCommandsProcessor.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T11:17:50.71Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "PaymentRequestService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestService.cs", "ViewState": "AgIAAPkAAAAAAAAAAAAAANABAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T10:30:34.394Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "MixedCasesTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\MixedCasesTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\MixedCasesTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\MixedCasesTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\MixedCasesTests.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAkwBEAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T11:58:20.952Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 26, "Title": "PaymentMethodEnumMigration.md", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\docs\\PaymentMethodEnumMigration.md", "RelativeDocumentMoniker": "docs\\PaymentMethodEnumMigration.md", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\docs\\PaymentMethodEnumMigration.md", "RelativeToolTip": "docs\\PaymentMethodEnumMigration.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-07-16T11:37:07.622Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 25, "Title": "QueueEventsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\QueueEventsController.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Controllers\\QueueEventsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\QueueEventsController.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Controllers\\QueueEventsController.cs", "ViewState": "AgIAADsAAAAAAAAAAAAgwE8AAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T09:05:40.312Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 28, "Title": "PaymentRequestGroupings.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.Application.Tests\\Models\\PaymentRequestGroupings.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.Application.Tests\\Models\\PaymentRequestGroupings.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.Application.Tests\\Models\\PaymentRequestGroupings.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.Application.Tests\\Models\\PaymentRequestGroupings.cs", "ViewState": "AgIAACwAAAAAAAAAAAAQwEEAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T08:59:31.45Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "MultiplePaymentWindows.md", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\docs\\MultiplePaymentWindows.md", "RelativeDocumentMoniker": "docs\\MultiplePaymentWindows.md", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\docs\\MultiplePaymentWindows.md", "RelativeToolTip": "docs\\MultiplePaymentWindows.md", "ViewState": "AgIAALQAAAAAAAAAAAAAAB0AAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-07-16T10:09:35.431Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "PaymentRequestViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\ViewModels\\PaymentRequestViewModel.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\ViewModels\\PaymentRequestViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\ViewModels\\PaymentRequestViewModel.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\ViewModels\\PaymentRequestViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T07:18:27.079Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 30, "Title": "IAionServiceV2.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\External\\IAionServiceV2.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\External\\IAionServiceV2.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\External\\IAionServiceV2.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\External\\IAionServiceV2.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T07:18:29.132Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "AionDailyLimitsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\AionDailyLimitsController.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Controllers\\AionDailyLimitsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\AionDailyLimitsController.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Controllers\\AionDailyLimitsController.cs", "ViewState": "AgIAADcAAAAAAAAAAAAAAEkAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T10:56:28.891Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "~PaymentRequestsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\CopilotBaseline\\~PaymentRequestsController.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\AppData\\Local\\Temp\\CopilotBaseline\\~PaymentRequestsController.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\CopilotBaseline\\~PaymentRequestsController.cs", "RelativeToolTip": "..\\..\\..\\..\\AppData\\Local\\Temp\\CopilotBaseline\\~PaymentRequestsController.cs", "ViewState": "AgIAABAAAAAAAAAAAAAgwCQAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T07:18:12.471Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "PaymentRequestsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\PaymentRequestsController.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Controllers\\PaymentRequestsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\PaymentRequestsController.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Controllers\\PaymentRequestsController.cs", "ViewState": "AgIAABEAAAAAAAAAAAAQwCQAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T09:39:49.799Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 32, "Title": "TestingController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\TestingController.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Controllers\\TestingController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\TestingController.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Controllers\\TestingController.cs", "ViewState": "AgIAAIgAAAAAAAAAAAAqwJoAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T12:14:32.468Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "~DependencyRegistrar.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\CopilotBaseline\\~DependencyRegistrar.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\AppData\\Local\\Temp\\CopilotBaseline\\~DependencyRegistrar.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\CopilotBaseline\\~DependencyRegistrar.cs", "RelativeToolTip": "..\\..\\..\\..\\AppData\\Local\\Temp\\CopilotBaseline\\~DependencyRegistrar.cs", "ViewState": "AgIAAE4AAAAAAAAAAAAgwGIAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T07:18:10.971Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "CompaniesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\CompaniesController.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Controllers\\CompaniesController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\CompaniesController.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Controllers\\CompaniesController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T07:03:50.863Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "EmailNotificationController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\EmailNotificationController.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Controllers\\EmailNotificationController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\EmailNotificationController.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Controllers\\EmailNotificationController.cs", "ViewState": "AgIAAE4AAAAAAAAAAAAowE4AAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T09:29:37.082Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "HelperController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\HelperController.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Controllers\\HelperController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\HelperController.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Controllers\\HelperController.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAWwBMAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T07:48:56.711Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "IPaymentJobProcessor.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Processors\\IPaymentJobProcessor.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Abstractions\\Processors\\IPaymentJobProcessor.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Processors\\IPaymentJobProcessor.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Abstractions\\Processors\\IPaymentJobProcessor.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T16:49:04.729Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "PaymentFlowTemplatesEngine.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\PaymentFlowTemplatesEngine.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\PaymentFlowTemplatesEngine.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\PaymentFlowTemplatesEngine.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\PaymentFlowTemplatesEngine.cs", "ViewState": "AgIAAI8AAAAAAAAAAAAMwI4AAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T14:34:27.615Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "UoWGenericRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitOfWork\\Repositories\\UoWGenericRepository.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.UnitOfWork\\Repositories\\UoWGenericRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitOfWork\\Repositories\\UoWGenericRepository.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.UnitOfWork\\Repositories\\UoWGenericRepository.cs", "ViewState": "AgIAAEcAAAAAAAAAAAAjwFcAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T15:36:19.306Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 43, "Title": "PaymentJobProcessorPaymentWindowTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\PaymentJobProcessorPaymentWindowTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\PaymentJobProcessorPaymentWindowTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\PaymentJobProcessorPaymentWindowTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\PaymentJobProcessorPaymentWindowTests.cs", "ViewState": "AgIAAOYAAAAAAAAAAAAAAJUAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T12:17:09.14Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "PaymentWindowConfig.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Models\\PaymentWindowConfig.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Models\\PaymentWindowConfig.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Models\\PaymentWindowConfig.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Models\\PaymentWindowConfig.cs", "ViewState": "AgIAABYAAAAAAAAAAAAYwCoAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T12:18:06.016Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "ShouldlyCoreExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\728b2af7030607f29913fa27b17da547131b49c824e9b9b611ae5df747609526\\ShouldlyCoreExtensions.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\728b2af7030607f29913fa27b17da547131b49c824e9b9b611ae5df747609526\\ShouldlyCoreExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\728b2af7030607f29913fa27b17da547131b49c824e9b9b611ae5df747609526\\ShouldlyCoreExtensions.cs [Read Only]", "RelativeToolTip": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\728b2af7030607f29913fa27b17da547131b49c824e9b9b611ae5df747609526\\ShouldlyCoreExtensions.cs [Read Only]", "ViewState": "AgIAABsAAAAAAAAAAAArwC0AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T09:44:03.3Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 46, "Title": "PaymentRequestCommandRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentRequestCommandRepository.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentRequestCommandRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentRequestCommandRepository.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentRequestCommandRepository.cs", "ViewState": "AgIAABYAAAAAAAAAAAAgwBkAAABEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T13:50:26.208Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "GenericFakeMessageSender.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\GenericFakeMessageSender.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\GenericFakeMessageSender.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\GenericFakeMessageSender.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\GenericFakeMessageSender.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABoAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T16:26:14.277Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "MockedServices.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\MockedServices.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\MockedServices.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\MockedServices.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\MockedServices.cs", "ViewState": "AgIAAPcAAAAAAAAAAAArwA8BAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-27T10:58:48.862Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "TransactionStatusUpdateService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\TransactionStatusUpdateService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\TransactionStatusUpdateService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\TransactionStatusUpdateService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\TransactionStatusUpdateService.cs", "ViewState": "AgIAAIsAAAAAAAAAAAAjwL8AAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T12:38:55.605Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "PaymentTransactionService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentTransactionService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\PaymentTransactionService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentTransactionService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\PaymentTransactionService.cs", "ViewState": "AgIAAAIBAAAAAAAAAAAWwBEBAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T16:17:56.07Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 51, "Title": "AionBaseHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\AionBaseHandler.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\AionBaseHandler.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\AionBaseHandler.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\AionBaseHandler.cs", "ViewState": "AgIAAD4AAAAAAAAAAAArwFAAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T11:22:19.547Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 50, "Title": "CollectFromBorrowerTransactionHandlerTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.Application.Tests\\Handlers\\CollectFromBorrowerTransactionHandlerTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.Application.Tests\\Handlers\\CollectFromBorrowerTransactionHandlerTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.Application.Tests\\Handlers\\CollectFromBorrowerTransactionHandlerTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.Application.Tests\\Handlers\\CollectFromBorrowerTransactionHandlerTests.cs", "ViewState": "AgIAAFYAAAAAAAAAAAAjwGYAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T15:13:48.249Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 56, "Title": "PushToMerchantFullAmountTransactionHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\PushToMerchantFullAmountTransactionHandler.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\PushToMerchantFullAmountTransactionHandler.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\PushToMerchantFullAmountTransactionHandler.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\PushToMerchantFullAmountTransactionHandler.cs", "ViewState": "AgIAAEUAAAAAAAAAAAAowFAAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T14:56:12.076Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "Configuration.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\Constants\\Configuration.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\Constants\\Configuration.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\Constants\\Configuration.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\Constants\\Configuration.cs", "ViewState": "AgIAAAQAAAAAAAAAAAAjwBQAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T14:54:48.455Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "PushToMerchantFinalAmountTransactionHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\PushToMerchantFinalAmountTransactionHandler.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\PushToMerchantFinalAmountTransactionHandler.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\PushToMerchantFinalAmountTransactionHandler.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\PushToMerchantFinalAmountTransactionHandler.cs", "ViewState": "AgIAAD8AAAAAAAAAAAAowAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T14:55:47.873Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "PushToMerchantAdvanceAmountTransactionHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\PushToMerchantAdvanceAmountTransactionHandler.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\PushToMerchantAdvanceAmountTransactionHandler.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\PushToMerchantAdvanceAmountTransactionHandler.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\PushToMerchantAdvanceAmountTransactionHandler.cs", "ViewState": "AgIAAC8AAAAAAAAAAAAQwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T14:56:36.761Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "AionServiceV2.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\External\\AionServiceV2.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\External\\AionServiceV2.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\External\\AionServiceV2.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\External\\AionServiceV2.cs", "ViewState": "AgIAABkAAAAAAAAAAADgvxUAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T09:20:27.538Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "CommandManagementService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\CommandManagementService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\CommandManagementService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\CommandManagementService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\CommandManagementService.cs", "ViewState": "AgIAADQAAAAAAAAAAAAgwCcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T11:41:36.262Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "PaymentFlowTemplatesEngineConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\Constants\\PaymentFlowTemplatesEngineConstants.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\Constants\\PaymentFlowTemplatesEngineConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\Constants\\PaymentFlowTemplatesEngineConstants.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\Constants\\PaymentFlowTemplatesEngineConstants.cs", "ViewState": "AgIAACcAAAAAAAAAAAAkwDUAAABVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T14:34:33.38Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "PaymentRequestCommandService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCommandService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCommandService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCommandService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCommandService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T14:37:58.221Z"}, {"$type": "Document", "DocumentIndex": 64, "Title": "TransactionStatusUpdateHistoryTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\TransactionStatusUpdateHistoryTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\TransactionStatusUpdateHistoryTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\TransactionStatusUpdateHistoryTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\TransactionStatusUpdateHistoryTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:06:05.37Z"}, {"$type": "Document", "DocumentIndex": 63, "Title": "TransactionStatusUpdateProcessor.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Processors\\TransactionStatusUpdateProcessor.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Processors\\TransactionStatusUpdateProcessor.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Processors\\TransactionStatusUpdateProcessor.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Processors\\TransactionStatusUpdateProcessor.cs", "ViewState": "AgIAAFwAAAAAAAAAAAAjwG0AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:47:02.898Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "RollbacksIntegrationTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\RollbacksIntegrationTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\RollbacksIntegrationTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\RollbacksIntegrationTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\RollbacksIntegrationTests.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAkwBEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:05:54.997Z"}, {"$type": "Document", "DocumentIndex": 62, "Title": "TransactionRecalledEventHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\TransactionRecalledEventHandler.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\TransactionRecalledEventHandler.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\TransactionRecalledEventHandler.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\TransactionRecalledEventHandler.cs", "ViewState": "AgIAAA4AAAAAAAAAAIA6wCAAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T12:38:55.589Z"}, {"$type": "Document", "DocumentIndex": 66, "Title": "StatusService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Mappers\\StatusService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Mappers\\StatusService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Mappers\\StatusService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Mappers\\StatusService.cs", "ViewState": "AgIAABcAAAAAAAAAAAAlwC0AAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T09:50:44.55Z"}, {"$type": "Document", "DocumentIndex": 65, "Title": "HappyPathIntegrationTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\HappyPathIntegrationTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\HappyPathIntegrationTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\HappyPathIntegrationTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\HappyPathIntegrationTests.cs", "ViewState": "AgIAAIABAAAAAAAAAAAtwJoBAAB3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:05:56.25Z"}, {"$type": "Document", "DocumentIndex": 67, "Title": "FailedCasesTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\FailedCasesTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\FailedCasesTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\FailedCasesTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\FailedCasesTests.cs", "ViewState": "AgIAAEUAAAAAAAAAAAArwFcAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T16:11:20.121Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 69, "Title": "appsettings.integration-test.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\appsettings.integration-test.json", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\appsettings.integration-test.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\appsettings.integration-test.json", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\appsettings.integration-test.json", "ViewState": "AgIAAAgAAAAAAAAAAAAmwBsAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-17T12:55:26.881Z"}, {"$type": "Document", "DocumentIndex": 68, "Title": "AvailableBalanceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\AvailableBalanceTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\AvailableBalanceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\AvailableBalanceTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\AvailableBalanceTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABgAAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:05:59.297Z"}, {"$type": "Document", "DocumentIndex": 70, "Title": "appsettings.dev.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\appsettings.dev.json", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\appsettings.dev.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\appsettings.dev.json", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\appsettings.dev.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-24T09:49:36.776Z"}, {"$type": "Document", "DocumentIndex": 71, "Title": "DependencyRegistrar.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\DI\\DependencyRegistrar.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\DI\\DependencyRegistrar.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\DI\\DependencyRegistrar.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\DI\\DependencyRegistrar.cs", "ViewState": "AgIAAFwAAAAAAAAAAAAAAG0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:59:48.095Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 72, "Title": "PaymentWindowFeature.md", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\docs\\PaymentWindowFeature.md", "RelativeDocumentMoniker": "docs\\PaymentWindowFeature.md", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\docs\\PaymentWindowFeature.md", "RelativeToolTip": "docs\\PaymentWindowFeature.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-07-16T08:48:39.042Z"}, {"$type": "Document", "DocumentIndex": 74, "Title": "TransactionStatusUpdateConsumer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\functions\\BlueTape.Functions.PaymentJob\\TransactionStatusUpdateConsumer.cs", "RelativeDocumentMoniker": "src\\functions\\BlueTape.Functions.PaymentJob\\TransactionStatusUpdateConsumer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\functions\\BlueTape.Functions.PaymentJob\\TransactionStatusUpdateConsumer.cs", "RelativeToolTip": "src\\functions\\BlueTape.Functions.PaymentJob\\TransactionStatusUpdateConsumer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:06:09.583Z"}, {"$type": "Document", "DocumentIndex": 73, "Title": "PaymentRequestControllerTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\ControllerTests\\PaymentRequestControllerTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\ControllerTests\\PaymentRequestControllerTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\ControllerTests\\PaymentRequestControllerTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\ControllerTests\\PaymentRequestControllerTests.cs", "ViewState": "AgIAADEBAAAAAAAAAAAvwEUBAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T17:57:09.587Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 75, "Title": "PaymentWindowServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.UnitTests\\Services\\PaymentWindowServiceTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.UnitTests\\Services\\PaymentWindowServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.UnitTests\\Services\\PaymentWindowServiceTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.UnitTests\\Services\\PaymentWindowServiceTests.cs", "ViewState": "AgIAAM0AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T08:48:42.615Z"}, {"$type": "Document", "DocumentIndex": 76, "Title": "PaymentRequestData.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\TestData\\PaymentRequestData.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\TestData\\PaymentRequestData.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\TestData\\PaymentRequestData.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\TestData\\PaymentRequestData.cs", "ViewState": "AgIAAJUAAAAAAAAAAADwv4sAAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T16:30:14.563Z"}, {"$type": "Document", "DocumentIndex": 77, "Title": "NotificationProcessor.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Processors\\NotificationProcessor.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Processors\\NotificationProcessor.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Processors\\NotificationProcessor.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Processors\\NotificationProcessor.cs", "ViewState": "AgIAAEMAAAAAAAAAAAAqwEkAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T11:46:44.235Z"}, {"$type": "Document", "DocumentIndex": 78, "Title": "FakePaymentFlowServiceMessageSender.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\FakePaymentFlowServiceMessageSender.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\FakePaymentFlowServiceMessageSender.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\FakePaymentFlowServiceMessageSender.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\FakePaymentFlowServiceMessageSender.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAQwAQAAABgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T16:25:46.317Z"}, {"$type": "Document", "DocumentIndex": 79, "Title": "TestAzureStorageDbContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\TestAzureStorageDbContext.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\TestAzureStorageDbContext.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\TestAzureStorageDbContext.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\TestAzureStorageDbContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T16:27:18.871Z"}, {"$type": "Document", "DocumentIndex": 80, "Title": "FakeTransactionStatusMessageSender.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\FakeTransactionStatusMessageSender.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\FakeTransactionStatusMessageSender.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\FakeTransactionStatusMessageSender.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\FakeTransactionStatusMessageSender.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAABmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T16:25:49.635Z"}, {"$type": "Document", "DocumentIndex": 81, "Title": "FakeNotificationMessageSender.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\FakeNotificationMessageSender.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\FakeNotificationMessageSender.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\FakeNotificationMessageSender.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\FakeNotificationMessageSender.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAQAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T16:25:50.794Z"}, {"$type": "Document", "DocumentIndex": 82, "Title": "FakeAzureRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\FakeAzureRepository.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\FakeAzureRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\FakeAzureRepository.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\Fakes\\FakeAzureRepository.cs", "ViewState": "AgIAACEAAAAAAAAAAAAowAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T16:25:51.657Z"}, {"$type": "Document", "DocumentIndex": 83, "Title": "PausedPaymentRequestsTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\PausedPaymentRequestsTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\PausedPaymentRequestsTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\PausedPaymentRequestsTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\PausedPaymentRequestsTests.cs", "ViewState": "AgIAABYAAAAAAAAAAAAgwBoAAABJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:05:52.783Z"}, {"$type": "Document", "DocumentIndex": 84, "Title": "PostgresDatabaseTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\PostgresDatabaseTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\PostgresDatabaseTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\PostgresDatabaseTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\PostgresDatabaseTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:06:06.166Z"}, {"$type": "Document", "DocumentIndex": 85, "Title": "EligibleBalanceFiltrationTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\EligibleBalanceFiltrationTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\EligibleBalanceFiltrationTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\EligibleBalanceFiltrationTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\PaymentFlowTests\\EligibleBalanceFiltrationTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T11:58:20.876Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 87, "Title": "RetryPolicySettings.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Models\\RetryPolicySettings.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Models\\RetryPolicySettings.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Models\\RetryPolicySettings.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Models\\RetryPolicySettings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T15:01:52.544Z"}, {"$type": "Document", "DocumentIndex": 86, "Title": "AionDailyLimitConfig.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Models\\AionDailyLimitConfig.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Models\\AionDailyLimitConfig.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Models\\AionDailyLimitConfig.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Models\\AionDailyLimitConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T15:01:50.114Z"}, {"$type": "Document", "DocumentIndex": 88, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Program.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Program.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Program.cs", "ViewState": "AgIAAK4AAAAAAAAAAAAAALgAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T10:28:10.25Z"}, {"$type": "Document", "DocumentIndex": 89, "Title": "BasePaymentRequestCreationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\BasePaymentRequestCreationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\BasePaymentRequestCreationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\BasePaymentRequestCreationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\BasePaymentRequestCreationService.cs", "ViewState": "AgIAAEEAAAAAAAAAAADgv00AAABIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T12:10:36.202Z"}, {"$type": "Document", "DocumentIndex": 90, "Title": "FlowTemplate.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\Models\\FlowTemplate.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\Models\\FlowTemplate.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\Models\\FlowTemplate.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\Models\\FlowTemplate.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T11:27:21.659Z"}, {"$type": "Document", "DocumentIndex": 91, "Title": "LineOfCreditDisbursementStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\LineOfCreditDisbursementStrategy.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\LineOfCreditDisbursementStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\LineOfCreditDisbursementStrategy.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\LineOfCreditDisbursementStrategy.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T08:43:46.251Z"}, {"$type": "Document", "DocumentIndex": 92, "Title": "FactoringDisbursementStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\FactoringDisbursementStrategy.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\FactoringDisbursementStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\FactoringDisbursementStrategy.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\FactoringDisbursementStrategy.cs", "ViewState": "AgIAADoAAAAAAAAAAAAowE0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T08:43:17.056Z"}, {"$type": "Document", "DocumentIndex": 93, "Title": "IhcRepaymentStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\IhcRepaymentStrategy.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\IhcRepaymentStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\IhcRepaymentStrategy.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\IhcRepaymentStrategy.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T08:43:16.457Z"}, {"$type": "Document", "DocumentIndex": 94, "Title": "DependencyRegistrar.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\DI\\DependencyRegistrar.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.DataAccess\\DI\\DependencyRegistrar.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\DI\\DependencyRegistrar.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.DataAccess\\DI\\DependencyRegistrar.cs", "ViewState": "AgIAAB4AAAAAAAAAAAAqwB8AAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T12:37:45.765Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 96, "Title": "InvoicePaymentV2NotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\InvoicePaymentV2NotificationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\InvoicePaymentV2NotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\InvoicePaymentV2NotificationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\InvoicePaymentV2NotificationService.cs", "ViewState": "AgIAABsAAAAAAAAAAIAwwDgAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T07:22:29.489Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 101, "Title": "TransactionHelper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Helpers\\TransactionHelper.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Helpers\\TransactionHelper.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Helpers\\TransactionHelper.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Helpers\\TransactionHelper.cs", "ViewState": "AgIAAAIAAAAAAAAAAIAwwBgAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T14:29:22.698Z"}, {"$type": "Document", "DocumentIndex": 95, "Title": "TransactionNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\TransactionNotificationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\TransactionNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\TransactionNotificationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\TransactionNotificationService.cs", "ViewState": "AgIAACkAAAAAAAAAAAAMwFAAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T14:37:39.269Z"}, {"$type": "Document", "DocumentIndex": 97, "Title": "EmailNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\EmailNotificationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\EmailNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\EmailNotificationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\EmailNotificationService.cs", "ViewState": "AgIAAOcDAAAAAAAAAAAewJ0DAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T14:31:35.58Z"}, {"$type": "Document", "DocumentIndex": 98, "Title": "IEmailNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\Notification\\IEmailNotificationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\Notification\\IEmailNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\Notification\\IEmailNotificationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\Notification\\IEmailNotificationService.cs", "ViewState": "AgIAAFkAAAAAAAAAAAApwG8AAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T14:38:28.475Z"}, {"$type": "Document", "DocumentIndex": 100, "Title": "NotificationReceiversService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\NotificationReceiversService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\NotificationReceiversService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\NotificationReceiversService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\NotificationReceiversService.cs", "ViewState": "AgIAAC8AAAAAAAAAAAApwEMAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T10:42:32.04Z"}, {"$type": "Document", "DocumentIndex": 99, "Title": "InvoicePaymentNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\InvoicePaymentNotificationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\InvoicePaymentNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\InvoicePaymentNotificationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\InvoicePaymentNotificationService.cs", "ViewState": "AgIAAAgAAAAAAAAAAADwvzgAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-27T09:09:36.41Z"}, {"$type": "Document", "DocumentIndex": 102, "Title": "InvoiceDisbursementV2NotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\InvoiceDisbursementV2NotificationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\InvoiceDisbursementV2NotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\InvoiceDisbursementV2NotificationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\InvoiceDisbursementV2NotificationService.cs", "ViewState": "AgIAABsAAAAAAAAAAAAiwDgAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T07:22:50.826Z"}, {"$type": "Document", "DocumentIndex": 103, "Title": "SlackNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\SlackNotificationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\SlackNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\SlackNotificationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\SlackNotificationService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T11:35:29.911Z"}, {"$type": "Document", "DocumentIndex": 104, "Title": "ITransactionNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\Notification\\ITransactionNotificationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\Notification\\ITransactionNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\Notification\\ITransactionNotificationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\Notification\\ITransactionNotificationService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T09:52:17.616Z"}, {"$type": "Document", "DocumentIndex": 105, "Title": "FinalPaymentNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\FinalPaymentNotificationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\FinalPaymentNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\FinalPaymentNotificationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\FinalPaymentNotificationService.cs", "ViewState": "AgIAABYAAAAAAAAAAAAgwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T12:33:09.441Z"}, {"$type": "Document", "DocumentIndex": 106, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Properties\\launchSettings.json", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Properties\\launchSettings.json", "ViewState": "AgIAAEAAAAAAAAAAAAAAwFwAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-20T17:54:46.079Z"}, {"$type": "Document", "DocumentIndex": 107, "Title": "TransactionFailedOrErrorEventHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\TransactionFailedOrErrorEventHandler.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\TransactionFailedOrErrorEventHandler.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\TransactionFailedOrErrorEventHandler.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\TransactionFailedOrErrorEventHandler.cs", "ViewState": "AgIAAAUAAAAAAAAAAAArwBcAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T11:41:42.122Z"}, {"$type": "Document", "DocumentIndex": 109, "Title": "InvoicePaymentOperationsSyncService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\InvoicePayment\\InvoicePaymentOperationsSyncService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\InvoicePayment\\InvoicePaymentOperationsSyncService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\InvoicePayment\\InvoicePaymentOperationsSyncService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\InvoicePayment\\InvoicePaymentOperationsSyncService.cs", "ViewState": "AgIAAEsAAAAAAAAAAAAlwGIAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-27T08:15:36.318Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 110, "Title": "EnumMapper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Extensions\\EnumMapper.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Extensions\\EnumMapper.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Extensions\\EnumMapper.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Extensions\\EnumMapper.cs", "ViewState": "AgIAACwAAAAAAAAAAAAYwD4AAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-27T08:05:23.289Z"}, {"$type": "Document", "DocumentIndex": 108, "Title": "Helpers.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Extensions\\Helpers.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Extensions\\Helpers.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Extensions\\Helpers.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Extensions\\Helpers.cs", "ViewState": "AgIAAHMAAAAAAAAAAAAcwHUAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T08:43:31.453Z"}, {"$type": "Document", "DocumentIndex": 111, "Title": "DrawRepaymentManualOperationSyncService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepaymentManual\\DrawRepaymentManualOperationSyncService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepaymentManual\\DrawRepaymentManualOperationSyncService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepaymentManual\\DrawRepaymentManualOperationSyncService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepaymentManual\\DrawRepaymentManualOperationSyncService.cs", "ViewState": "AgIAAH0AAAAAAAAAAAAQwIwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T09:12:15.09Z"}, {"$type": "Document", "DocumentIndex": 112, "Title": "InvoicePaymentV2OperationsSyncService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\InvoicePaymentV2\\InvoicePaymentV2OperationsSyncService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\InvoicePaymentV2\\InvoicePaymentV2OperationsSyncService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\InvoicePaymentV2\\InvoicePaymentV2OperationsSyncService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\InvoicePaymentV2\\InvoicePaymentV2OperationsSyncService.cs", "ViewState": "AgIAAFQAAAAAAAAAAAAowGkAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T08:20:34.208Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 113, "Title": "FactoringFinalPaymentOperationSyncService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\FactoringFinalPayment\\FactoringFinalPaymentOperationSyncService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\FactoringFinalPayment\\FactoringFinalPaymentOperationSyncService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\FactoringFinalPayment\\FactoringFinalPaymentOperationSyncService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\FactoringFinalPayment\\FactoringFinalPaymentOperationSyncService.cs", "ViewState": "AgIAACgAAAAAAAAAAIA3wDkAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T07:14:37.909Z"}, {"$type": "Document", "DocumentIndex": 114, "Title": "FinalPaymentOperationSyncService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\FinalPayment\\FinalPaymentOperationSyncService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\FinalPayment\\FinalPaymentOperationSyncService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\FinalPayment\\FinalPaymentOperationSyncService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\FinalPayment\\FinalPaymentOperationSyncService.cs", "ViewState": "AgIAAEAAAAAAAAAAAAAlwFEAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T07:45:51.121Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 115, "Title": "BaseOperationSyncService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\Base\\BaseOperationSyncService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\Base\\BaseOperationSyncService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\Base\\BaseOperationSyncService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\Base\\BaseOperationSyncService.cs", "ViewState": "AgIAACQAAAAAAAAAAAAtwDEAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T09:41:42.864Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 116, "Title": "InvoiceSyncMessageSender.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Senders\\InvoiceSyncMessageSender.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Senders\\InvoiceSyncMessageSender.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Senders\\InvoiceSyncMessageSender.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Senders\\InvoiceSyncMessageSender.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T07:13:32.101Z"}, {"$type": "Document", "DocumentIndex": 117, "Title": "DrawDisbursementOperationSyncService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawDisbursement\\DrawDisbursementOperationSyncService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawDisbursement\\DrawDisbursementOperationSyncService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawDisbursement\\DrawDisbursementOperationSyncService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawDisbursement\\DrawDisbursementOperationSyncService.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAawBMAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T07:44:36.211Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 118, "Title": "IInvoiceSyncMessageSender.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Abstractions\\Senders\\IInvoiceSyncMessageSender.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Abstractions\\Senders\\IInvoiceSyncMessageSender.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Abstractions\\Senders\\IInvoiceSyncMessageSender.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Abstractions\\Senders\\IInvoiceSyncMessageSender.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T07:13:26.726Z"}, {"$type": "Document", "DocumentIndex": 119, "Title": "PaymentRequestConsumer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\functions\\BlueTape.Functions.PaymentJob\\PaymentRequestConsumer.cs", "RelativeDocumentMoniker": "src\\functions\\BlueTape.Functions.PaymentJob\\PaymentRequestConsumer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\functions\\BlueTape.Functions.PaymentJob\\PaymentRequestConsumer.cs", "RelativeToolTip": "src\\functions\\BlueTape.Functions.PaymentJob\\PaymentRequestConsumer.cs", "ViewState": "AgIAAPsAAAAAAAAAAAAQwPwAAABSAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:04:56.88Z"}, {"$type": "Document", "DocumentIndex": 120, "Title": "DrawRepaymentsOperationSyncService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepayments\\DrawRepaymentsOperationSyncService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepayments\\DrawRepaymentsOperationSyncService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepayments\\DrawRepaymentsOperationSyncService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepayments\\DrawRepaymentsOperationSyncService.cs", "ViewState": "AgIAAC8AAAAAAAAAAAAcwDkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T08:35:40.692Z"}, {"$type": "Document", "DocumentIndex": 121, "Title": "DrawRepaymentManualTransactionSyncService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepaymentManual\\DrawRepaymentManualTransactionSyncService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepaymentManual\\DrawRepaymentManualTransactionSyncService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepaymentManual\\DrawRepaymentManualTransactionSyncService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepaymentManual\\DrawRepaymentManualTransactionSyncService.cs", "ViewState": "AgIAAAQAAAAAAAAAAAAewBEAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T09:12:23.133Z"}, {"$type": "Document", "DocumentIndex": 122, "Title": "IDrawRepaymentManualTransactionSyncService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Abstractions\\Service\\DrawRepaymentManual\\IDrawRepaymentManualTransactionSyncService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Abstractions\\Service\\DrawRepaymentManual\\IDrawRepaymentManualTransactionSyncService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Abstractions\\Service\\DrawRepaymentManual\\IDrawRepaymentManualTransactionSyncService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Abstractions\\Service\\DrawRepaymentManual\\IDrawRepaymentManualTransactionSyncService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T09:26:08.084Z"}, {"$type": "Document", "DocumentIndex": 123, "Title": "IDrawRepaymentManualOperationSyncService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Abstractions\\Service\\DrawRepaymentManual\\IDrawRepaymentManualOperationSyncService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Abstractions\\Service\\DrawRepaymentManual\\IDrawRepaymentManualOperationSyncService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Abstractions\\Service\\DrawRepaymentManual\\IDrawRepaymentManualOperationSyncService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Abstractions\\Service\\DrawRepaymentManual\\IDrawRepaymentManualOperationSyncService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T09:26:07.184Z"}, {"$type": "Document", "DocumentIndex": 124, "Title": "DrawRepaymentsCompatibilityMapper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepayments\\DrawRepaymentsCompatibilityMapper.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepayments\\DrawRepaymentsCompatibilityMapper.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepayments\\DrawRepaymentsCompatibilityMapper.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepayments\\DrawRepaymentsCompatibilityMapper.cs", "ViewState": "AgIAAFEAAAAAAAAAAAAQwF0AAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T07:47:40.396Z"}, {"$type": "Document", "DocumentIndex": 125, "Title": "InvoicePaymentCompatibilityMapper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\InvoicePayment\\InvoicePaymentCompatibilityMapper.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\InvoicePayment\\InvoicePaymentCompatibilityMapper.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\InvoicePayment\\InvoicePaymentCompatibilityMapper.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\InvoicePayment\\InvoicePaymentCompatibilityMapper.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAowBoAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T07:45:08.137Z"}, {"$type": "Document", "DocumentIndex": 126, "Title": "InvoicePaymentRequestCreationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\InvoicePaymentRequestCreationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\InvoicePaymentRequestCreationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\InvoicePaymentRequestCreationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\InvoicePaymentRequestCreationService.cs", "ViewState": "AgIAAC0AAAAAAAAAAAAAADAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T12:27:31.412Z"}, {"$type": "Document", "DocumentIndex": 127, "Title": "SubscriptionFeeStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\SubscriptionFeeStrategy.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\SubscriptionFeeStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\SubscriptionFeeStrategy.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\SubscriptionFeeStrategy.cs", "ViewState": "AgIAAB8AAAAAAAAAAAAAwCoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T07:56:35.544Z"}, {"$type": "Document", "DocumentIndex": 128, "Title": "InvoicePaymentStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\InvoicePaymentStrategy.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\InvoicePaymentStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\InvoicePaymentStrategy.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\Strategies\\InvoicePaymentStrategy.cs", "ViewState": "AgIAADAAAAAAAAAAAAAjwDAAAABFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T12:27:38.071Z"}, {"$type": "Document", "DocumentIndex": 131, "Title": "DecimalExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Extensions\\DecimalExtensions.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Extensions\\DecimalExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Extensions\\DecimalExtensions.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Extensions\\DecimalExtensions.cs", "ViewState": "AgIAAAQAAAAAAAAAAAAAAAgAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T10:24:00.34Z"}, {"$type": "Document", "DocumentIndex": 130, "Title": "PaymentRequestDetailsViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\ViewModels\\PaymentRequestDetailsViewModel.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\ViewModels\\PaymentRequestDetailsViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\ViewModels\\PaymentRequestDetailsViewModel.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\ViewModels\\PaymentRequestDetailsViewModel.cs", "ViewState": "AgIAAAcAAAAAAAAAAAA1wB0AAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T16:35:59.669Z"}, {"$type": "Document", "DocumentIndex": 129, "Title": "InvoiceDisbursementV2RequestCreationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\InvoiceDisbursementV2RequestCreationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\InvoiceDisbursementV2RequestCreationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\InvoiceDisbursementV2RequestCreationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\InvoiceDisbursementV2RequestCreationService.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAYwCYAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T12:27:20.472Z"}, {"$type": "Document", "DocumentIndex": 132, "Title": "20250605073536_AddPaymentConfigTable.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Migrations\\20250605073536_AddPaymentConfigTable.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.DataAccess\\Migrations\\20250605073536_AddPaymentConfigTable.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Migrations\\20250605073536_AddPaymentConfigTable.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.DataAccess\\Migrations\\20250605073536_AddPaymentConfigTable.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T08:09:28.661Z"}, {"$type": "Document", "DocumentIndex": 133, "Title": "DatabaseContextModelSnapshot.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Migrations\\DatabaseContextModelSnapshot.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.DataAccess\\Migrations\\DatabaseContextModelSnapshot.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Migrations\\DatabaseContextModelSnapshot.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.DataAccess\\Migrations\\DatabaseContextModelSnapshot.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABkAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T08:09:33.251Z"}, {"$type": "Document", "DocumentIndex": 135, "Title": "APIProfile.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Mappers\\APIProfile.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Mappers\\APIProfile.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Mappers\\APIProfile.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Mappers\\APIProfile.cs", "ViewState": "AgIAABkAAAAAAAAAAADwvxsAAABDAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T12:25:38.949Z"}, {"$type": "Document", "DocumentIndex": 137, "Title": "LoanPaymentsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\LoanPaymentsController.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Controllers\\LoanPaymentsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\LoanPaymentsController.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Controllers\\LoanPaymentsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T09:31:24.907Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 136, "Title": "OperationsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\OperationsController.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Controllers\\OperationsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\OperationsController.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Controllers\\OperationsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T16:35:29.916Z"}, {"$type": "Document", "DocumentIndex": 134, "Title": "PaymentRequestPayableRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentRequestPayableRepository.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentRequestPayableRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentRequestPayableRepository.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentRequestPayableRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T08:03:49.649Z"}, {"$type": "Document", "DocumentIndex": 138, "Title": "LoanManagementServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.Application.Tests\\Services\\LoanManagementServiceTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.Application.Tests\\Services\\LoanManagementServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.Application.Tests\\Services\\LoanManagementServiceTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.Application.Tests\\Services\\LoanManagementServiceTests.cs", "ViewState": "AgIAADoAAAAAAAAAAAASwFQAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T11:59:07.904Z"}, {"$type": "Document", "DocumentIndex": 141, "Title": "DrawRepaymentRequestMessage.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\DrawRepayment\\DrawRepaymentRequestMessage.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\DrawRepayment\\DrawRepaymentRequestMessage.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\DrawRepayment\\DrawRepaymentRequestMessage.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\DrawRepayment\\DrawRepaymentRequestMessage.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T16:06:46.597Z"}, {"$type": "Document", "DocumentIndex": 140, "Title": "ILoanManagementService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\ILoanManagementService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\ILoanManagementService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\ILoanManagementService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\ILoanManagementService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAUAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T11:48:29.255Z"}, {"$type": "Document", "DocumentIndex": 139, "Title": "LoanManagementService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\LoanManagementService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\LoanManagementService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\LoanManagementService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\LoanManagementService.cs", "ViewState": "AgIAABQAAAAAAAAAAAAQwBoAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T12:00:45.004Z"}, {"$type": "Document", "DocumentIndex": 142, "Title": "DrawRepaymentCardRequestMessage.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\DrawRepaymentCard\\DrawRepaymentCardRequestMessage.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\DrawRepaymentCard\\DrawRepaymentCardRequestMessage.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\DrawRepaymentCard\\DrawRepaymentCardRequestMessage.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\DrawRepaymentCard\\DrawRepaymentCardRequestMessage.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAswAQAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T16:06:31.814Z"}, {"$type": "Document", "DocumentIndex": 143, "Title": "TransactionsRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess.Mongo\\Repositories\\TransactionsRepository.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.DataAccess.Mongo\\Repositories\\TransactionsRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess.Mongo\\Repositories\\TransactionsRepository.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.DataAccess.Mongo\\Repositories\\TransactionsRepository.cs", "ViewState": "AgIAAFkAAAAAAAAAAAAUwF4AAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-27T09:03:18.948Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 144, "Title": "DrawRepaymentsTransactionSyncService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepayments\\DrawRepaymentsTransactionSyncService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepayments\\DrawRepaymentsTransactionSyncService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepayments\\DrawRepaymentsTransactionSyncService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepayments\\DrawRepaymentsTransactionSyncService.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAABcAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T08:35:44.323Z"}, {"$type": "Document", "DocumentIndex": 145, "Title": "InvoicePaymentV2TransactionsSyncService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\InvoicePaymentV2\\InvoicePaymentV2TransactionsSyncService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\InvoicePaymentV2\\InvoicePaymentV2TransactionsSyncService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\InvoicePaymentV2\\InvoicePaymentV2TransactionsSyncService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\InvoicePaymentV2\\InvoicePaymentV2TransactionsSyncService.cs", "ViewState": "AgIAAFYAAAAAAAAAAAArwGwAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T08:20:35.61Z"}, {"$type": "Document", "DocumentIndex": 146, "Title": "TransactionStatusUpdateJobFunction.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\functions\\BlueTape.Functions.PaymentJob\\TransactionStatusUpdateJobFunction.cs", "RelativeDocumentMoniker": "src\\functions\\BlueTape.Functions.PaymentJob\\TransactionStatusUpdateJobFunction.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\functions\\BlueTape.Functions.PaymentJob\\TransactionStatusUpdateJobFunction.cs", "RelativeToolTip": "src\\functions\\BlueTape.Functions.PaymentJob\\TransactionStatusUpdateJobFunction.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAABkAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:46:59.04Z"}, {"$type": "Document", "DocumentIndex": 148, "Title": "PaymentRequestStatus.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\PaymentRequestStatus.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Enums\\PaymentRequestStatus.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\PaymentRequestStatus.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Enums\\PaymentRequestStatus.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:46:41.783Z"}, {"$type": "Document", "DocumentIndex": 149, "Title": "EventType.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\EventType.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Enums\\EventType.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\EventType.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Enums\\EventType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:46:37.021Z"}, {"$type": "Document", "DocumentIndex": 151, "Title": "BlueTapePaymentErrorCodes.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\BlueTapePaymentErrorCodes.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Enums\\BlueTapePaymentErrorCodes.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\BlueTapePaymentErrorCodes.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Enums\\BlueTapePaymentErrorCodes.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:46:35.661Z"}, {"$type": "Document", "DocumentIndex": 152, "Title": "ConfirmationType.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\ConfirmationType.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Enums\\ConfirmationType.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\ConfirmationType.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Enums\\ConfirmationType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:46:34.769Z"}, {"$type": "Document", "DocumentIndex": 153, "Title": "FinalPaymentStatusForNotification.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\FinalPaymentStatusForNotification.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Enums\\FinalPaymentStatusForNotification.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\FinalPaymentStatusForNotification.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Enums\\FinalPaymentStatusForNotification.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:46:34.036Z"}, {"$type": "Document", "DocumentIndex": 147, "Title": "PaymentRequestType.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\PaymentRequestType.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Enums\\PaymentRequestType.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\PaymentRequestType.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Enums\\PaymentRequestType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:46:32.376Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 154, "Title": "PaymentProvider.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\PaymentProvider.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Enums\\PaymentProvider.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\PaymentProvider.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Enums\\PaymentProvider.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:46:31.384Z"}, {"$type": "Document", "DocumentIndex": 155, "Title": "PaymentPauseReason.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\PaymentPauseReason.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Enums\\PaymentPauseReason.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\PaymentPauseReason.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Enums\\PaymentPauseReason.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:46:30.197Z"}, {"$type": "Document", "DocumentIndex": 150, "Title": "EmailTemplateName.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\EmailTemplateName.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Enums\\EmailTemplateName.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\EmailTemplateName.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Enums\\EmailTemplateName.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T09:48:57.726Z"}, {"$type": "Document", "DocumentIndex": 156, "Title": "PayableType.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\PayableType.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Enums\\PayableType.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Enums\\PayableType.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Enums\\PayableType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:46:25.003Z"}, {"$type": "Document", "DocumentIndex": 161, "Title": "InvoicePaymentCardRequestCreationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\InvoicePaymentCardRequestCreationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\InvoicePaymentCardRequestCreationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\InvoicePaymentCardRequestCreationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\InvoicePaymentCardRequestCreationService.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAYwCUAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T12:27:28.226Z"}, {"$type": "Document", "DocumentIndex": 160, "Title": "InvoicePaymentV2RequestCreationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\InvoicePaymentV2RequestCreationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\InvoicePaymentV2RequestCreationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\InvoicePaymentV2RequestCreationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\InvoicePaymentV2RequestCreationService.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAYwCUAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T12:27:34.45Z"}, {"$type": "Document", "DocumentIndex": 159, "Title": "PaymentRequestSettledHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Events\\PaymentRequest\\PaymentRequestSettledHandler.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Handlers\\Events\\PaymentRequest\\PaymentRequestSettledHandler.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Events\\PaymentRequest\\PaymentRequestSettledHandler.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Handlers\\Events\\PaymentRequest\\PaymentRequestSettledHandler.cs", "ViewState": "AgIAAJAAAAAAAAAAAAD4v7AAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T11:17:49.475Z"}, {"$type": "Document", "DocumentIndex": 158, "Title": "DrawRepaymentNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\DrawRepaymentNotificationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\DrawRepaymentNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\DrawRepaymentNotificationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\DrawRepaymentNotificationService.cs", "ViewState": "AgIAABEAAAAAAAAAAAAQwCQAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T12:33:03.198Z"}, {"$type": "Document", "DocumentIndex": 157, "Title": "DrawDisbursementNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\DrawDisbursementNotificationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\DrawDisbursementNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\DrawDisbursementNotificationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\DrawDisbursementNotificationService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T12:15:27.179Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 162, "Title": "LedgerService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\External\\Ledger\\LedgerService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\External\\Ledger\\LedgerService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\External\\Ledger\\LedgerService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\External\\Ledger\\LedgerService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T12:26:57.964Z"}, {"$type": "Document", "DocumentIndex": 163, "Title": "InvoicePaymentRequestCreationServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.Application.Tests\\Services\\InvoicePaymentRequestCreationServiceTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.Application.Tests\\Services\\InvoicePaymentRequestCreationServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.Application.Tests\\Services\\InvoicePaymentRequestCreationServiceTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.Application.Tests\\Services\\InvoicePaymentRequestCreationServiceTests.cs", "ViewState": "AgIAABEAAAAAAAAAAAAtwC4AAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T12:28:03.625Z"}, {"$type": "Document", "DocumentIndex": 164, "Title": "ILedgerService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\External\\Ledger\\ILedgerService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\External\\Ledger\\ILedgerService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\External\\Ledger\\ILedgerService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\External\\Ledger\\ILedgerService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T12:26:47.13Z"}, {"$type": "Document", "DocumentIndex": 165, "Title": "OperationSyncConsumer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\functions\\BlueTape.Functions.PaymentJob\\OperationSyncConsumer.cs", "RelativeDocumentMoniker": "src\\functions\\BlueTape.Functions.PaymentJob\\OperationSyncConsumer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\functions\\BlueTape.Functions.PaymentJob\\OperationSyncConsumer.cs", "RelativeToolTip": "src\\functions\\BlueTape.Functions.PaymentJob\\OperationSyncConsumer.cs", "ViewState": "AgIAADgAAAAAAAAAAAAswDoAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T08:42:26.808Z"}, {"$type": "Document", "DocumentIndex": 166, "Title": "PaymentJobFunction.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\functions\\BlueTape.Functions.PaymentJob\\PaymentJobFunction.cs", "RelativeDocumentMoniker": "src\\functions\\BlueTape.Functions.PaymentJob\\PaymentJobFunction.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\functions\\BlueTape.Functions.PaymentJob\\PaymentJobFunction.cs", "RelativeToolTip": "src\\functions\\BlueTape.Functions.PaymentJob\\PaymentJobFunction.cs", "ViewState": "AgIAABcAAAAAAAAAAAAQwCMAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T17:51:16.305Z"}, {"$type": "Document", "DocumentIndex": 167, "Title": "PaymentRequestFailedHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Events\\PaymentRequest\\PaymentRequestFailedHandler.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Handlers\\Events\\PaymentRequest\\PaymentRequestFailedHandler.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Events\\PaymentRequest\\PaymentRequestFailedHandler.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Handlers\\Events\\PaymentRequest\\PaymentRequestFailedHandler.cs", "ViewState": "AgIAAEsAAAAAAAAAAAAWwFwAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T11:36:52.85Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 169, "Title": "ValueTask.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\f2256876427a059b9953c63da8b5ed989c4c449b3c6605ccdb8777c9e3a4ab0f\\ValueTask.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\f2256876427a059b9953c63da8b5ed989c4c449b3c6605ccdb8777c9e3a4ab0f\\ValueTask.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\f2256876427a059b9953c63da8b5ed989c4c449b3c6605ccdb8777c9e3a4ab0f\\ValueTask.cs", "RelativeToolTip": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\f2256876427a059b9953c63da8b5ed989c4c449b3c6605ccdb8777c9e3a4ab0f\\ValueTask.cs", "ViewState": "AgIAAMkBAAAAAAAAAAAqwNkBAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T07:05:54.058Z"}, {"$type": "Document", "DocumentIndex": 168, "Title": "AsyncMethodBuilderCore.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\f2a8da854073706fa2071257656390f3499c628218273ea440667f952a1e4baa\\AsyncMethodBuilderCore.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\f2a8da854073706fa2071257656390f3499c628218273ea440667f952a1e4baa\\AsyncMethodBuilderCore.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\f2a8da854073706fa2071257656390f3499c628218273ea440667f952a1e4baa\\AsyncMethodBuilderCore.cs [Read Only]", "RelativeToolTip": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\f2a8da854073706fa2071257656390f3499c628218273ea440667f952a1e4baa\\AsyncMethodBuilderCore.cs [Read Only]", "ViewState": "AgIAACYAAAAAAAAAAAAqwDYAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T08:43:13.019Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 172, "Title": "ExecutionContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\4dcf567db33b8407e8e7e85491df71d46c06985d33f5ad8d904e52c3b325c6c5\\ExecutionContext.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\4dcf567db33b8407e8e7e85491df71d46c06985d33f5ad8d904e52c3b325c6c5\\ExecutionContext.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\4dcf567db33b8407e8e7e85491df71d46c06985d33f5ad8d904e52c3b325c6c5\\ExecutionContext.cs [Read Only]", "RelativeToolTip": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\4dcf567db33b8407e8e7e85491df71d46c06985d33f5ad8d904e52c3b325c6c5\\ExecutionContext.cs [Read Only]", "ViewState": "AgIAAHkAAAAAAAAAAAAqwIkAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T08:43:15.073Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 171, "Title": "PoolingAsyncValueTaskMethodBuilder.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\df41063b75a98ed4d94a347657623436de4c396d99df97d1012994f374b271d0\\PoolingAsyncValueTaskMethodBuilder.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\df41063b75a98ed4d94a347657623436de4c396d99df97d1012994f374b271d0\\PoolingAsyncValueTaskMethodBuilder.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\df41063b75a98ed4d94a347657623436de4c396d99df97d1012994f374b271d0\\PoolingAsyncValueTaskMethodBuilder.cs [Read Only]", "RelativeToolTip": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\df41063b75a98ed4d94a347657623436de4c396d99df97d1012994f374b271d0\\PoolingAsyncValueTaskMethodBuilder.cs [Read Only]", "ViewState": "AgIAAMsAAAAAAAAAAAAqwNsAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T08:43:18.385Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 170, "Title": "DiagnosticsHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\436f8735f9701c121e542e705b8002c28cf053641f7e971e41e47cab32739fae\\DiagnosticsHandler.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\436f8735f9701c121e542e705b8002c28cf053641f7e971e41e47cab32739fae\\DiagnosticsHandler.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\436f8735f9701c121e542e705b8002c28cf053641f7e971e41e47cab32739fae\\DiagnosticsHandler.cs [Read Only]", "RelativeToolTip": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\436f8735f9701c121e542e705b8002c28cf053641f7e971e41e47cab32739fae\\DiagnosticsHandler.cs [Read Only]", "ViewState": "AgIAAN8AAAAAAAAAAAAqwPYAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T08:44:08.962Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 174, "Title": "TransactionsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\TransactionsController.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Controllers\\TransactionsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Controllers\\TransactionsController.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Controllers\\TransactionsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T12:04:14.464Z"}, {"$type": "Document", "DocumentIndex": 173, "Title": "HttpConnectionPool.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\e8dbc30d26a5a1ebfab4248acc30b41298aa139e054d44f23a4aefc8a3eead43\\HttpConnectionPool.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\e8dbc30d26a5a1ebfab4248acc30b41298aa139e054d44f23a4aefc8a3eead43\\HttpConnectionPool.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\e8dbc30d26a5a1ebfab4248acc30b41298aa139e054d44f23a4aefc8a3eead43\\HttpConnectionPool.cs", "RelativeToolTip": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\e8dbc30d26a5a1ebfab4248acc30b41298aa139e054d44f23a4aefc8a3eead43\\HttpConnectionPool.cs", "ViewState": "AgIAAOoDAAAAAAAAAAAqwPoDAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T07:05:48.48Z"}, {"$type": "Document", "DocumentIndex": 176, "Title": "DrawDisbursementCompatibilityMapper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawDisbursement\\DrawDisbursementCompatibilityMapper.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawDisbursement\\DrawDisbursementCompatibilityMapper.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawDisbursement\\DrawDisbursementCompatibilityMapper.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawDisbursement\\DrawDisbursementCompatibilityMapper.cs", "ViewState": "AgIAAE4AAAAAAAAAAAAhwE8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-27T16:10:52.887Z"}, {"$type": "Document", "DocumentIndex": 247, "Title": "INotificationReceiversService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\Notification\\INotificationReceiversService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\Notification\\INotificationReceiversService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\Notification\\INotificationReceiversService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\Notification\\INotificationReceiversService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-27T10:55:14.987Z"}, {"$type": "Document", "DocumentIndex": 175, "Title": "TransactionProcessingEventHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\TransactionProcessingEventHandler.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\TransactionProcessingEventHandler.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\TransactionProcessingEventHandler.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\TransactionProcessingEventHandler.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAUwB4AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T12:38:55.788Z"}, {"$type": "Document", "DocumentIndex": 177, "Title": "DrawDisbursementTransactionSyncService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawDisbursement\\DrawDisbursementTransactionSyncService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawDisbursement\\DrawDisbursementTransactionSyncService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawDisbursement\\DrawDisbursementTransactionSyncService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawDisbursement\\DrawDisbursementTransactionSyncService.cs", "ViewState": "AgIAAAYAAAAAAAAAAAArwBIAAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T15:06:34.56Z"}, {"$type": "Document", "DocumentIndex": 178, "Title": "UserRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess.Mongo\\Repositories\\UserRepository.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.DataAccess.Mongo\\Repositories\\UserRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess.Mongo\\Repositories\\UserRepository.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.DataAccess.Mongo\\Repositories\\UserRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABkAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-27T10:51:47.965Z"}, {"$type": "Document", "DocumentIndex": 179, "Title": "InvoicePaymentV2CompatibilityStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Strategies\\InvoicePaymentV2CompatibilityStrategy.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Strategies\\InvoicePaymentV2CompatibilityStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Strategies\\InvoicePaymentV2CompatibilityStrategy.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Strategies\\InvoicePaymentV2CompatibilityStrategy.cs", "ViewState": "AgIAADQAAAAAAAAAAAASwEoAAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T11:38:54.601Z"}, {"$type": "Document", "DocumentIndex": 181, "Title": "SettlementReportJob.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\functions\\BlueTape.Functions.PaymentJob\\SettlementReportJob.cs", "RelativeDocumentMoniker": "src\\functions\\BlueTape.Functions.PaymentJob\\SettlementReportJob.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\functions\\BlueTape.Functions.PaymentJob\\SettlementReportJob.cs", "RelativeToolTip": "src\\functions\\BlueTape.Functions.PaymentJob\\SettlementReportJob.cs", "ViewState": "AgIAAAkAAAAAAAAAAABQwB8AAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T08:38:22.377Z"}, {"$type": "Document", "DocumentIndex": 180, "Title": "DrawRepaymentRequestDetails.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\DrawRepayment\\DrawRepaymentRequestDetails.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\DrawRepayment\\DrawRepaymentRequestDetails.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\DrawRepayment\\DrawRepaymentRequestDetails.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\DrawRepayment\\DrawRepaymentRequestDetails.cs", "ViewState": "AgIAABIAAAAAAAAAAAAgwBMAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T12:24:08.289Z"}, {"$type": "Document", "DocumentIndex": 185, "Title": "CreatePaymentRequestFeeViewModelValidator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Validators\\CreatePaymentRequestFeeViewModelValidator.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Validators\\CreatePaymentRequestFeeViewModelValidator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Validators\\CreatePaymentRequestFeeViewModelValidator.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Validators\\CreatePaymentRequestFeeViewModelValidator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:04:39.967Z"}, {"$type": "Document", "DocumentIndex": 182, "Title": "CreatePaymentRequestViewModelValidator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Validators\\CreatePaymentRequestViewModelValidator.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Validators\\CreatePaymentRequestViewModelValidator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Validators\\CreatePaymentRequestViewModelValidator.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Validators\\CreatePaymentRequestViewModelValidator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:04:38.116Z"}, {"$type": "Document", "DocumentIndex": 183, "Title": "CreatePaymentRequestTransactionViewModelValidator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Validators\\CreatePaymentRequestTransactionViewModelValidator.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Validators\\CreatePaymentRequestTransactionViewModelValidator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Validators\\CreatePaymentRequestTransactionViewModelValidator.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Validators\\CreatePaymentRequestTransactionViewModelValidator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:04:37.352Z"}, {"$type": "Document", "DocumentIndex": 184, "Title": "CreatePaymentRequestPayableViewModelValidator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Validators\\CreatePaymentRequestPayableViewModelValidator.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Validators\\CreatePaymentRequestPayableViewModelValidator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Validators\\CreatePaymentRequestPayableViewModelValidator.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Validators\\CreatePaymentRequestPayableViewModelValidator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T12:04:36.612Z"}, {"$type": "Document", "DocumentIndex": 188, "Title": "DrawRepaymentCardCompatibilityMapper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepaymentCard\\DrawRepaymentCardCompatibilityMapper.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepaymentCard\\DrawRepaymentCardCompatibilityMapper.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepaymentCard\\DrawRepaymentCardCompatibilityMapper.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\DrawRepaymentCard\\DrawRepaymentCardCompatibilityMapper.cs", "ViewState": "AgIAAHcAAAAAAAAAAAAmwIMAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T07:46:47.011Z"}, {"$type": "Document", "DocumentIndex": 186, "Title": "CreateLegacyTransactionModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Models\\CreateLegacyTransactionModel.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Models\\CreateLegacyTransactionModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Models\\CreateLegacyTransactionModel.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Models\\CreateLegacyTransactionModel.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAjwBEAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T07:49:07.762Z"}, {"$type": "Document", "DocumentIndex": 187, "Title": "PaymentTransactionViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\ViewModels\\PaymentTransactionViewModel.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\ViewModels\\PaymentTransactionViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\ViewModels\\PaymentTransactionViewModel.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\ViewModels\\PaymentTransactionViewModel.cs", "ViewState": "AgIAAAcAAAAAAAAAAIA0wA4AAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T07:46:26.615Z"}, {"$type": "Document", "DocumentIndex": 189, "Title": "KeyVaultKeysConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Constants\\KeyVaultKeysConstants.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Constants\\KeyVaultKeysConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Constants\\KeyVaultKeysConstants.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Constants\\KeyVaultKeysConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T07:45:04.317Z"}, {"$type": "Document", "DocumentIndex": 190, "Title": "PaymentCompatibilityService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\PaymentCompatibilityService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\PaymentCompatibilityService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\PaymentCompatibilityService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\PaymentCompatibilityService.cs", "ViewState": "AgIAADcAAAAAAAAAAAAIwEYAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T15:52:57.27Z"}, {"$type": "Document", "DocumentIndex": 191, "Title": "DependencyInjection.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\DI\\DependencyInjection.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\DI\\DependencyInjection.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\DI\\DependencyInjection.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\DI\\DependencyInjection.cs", "ViewState": "AgIAAHIAAAAAAAAAAAAkwH4AAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T15:52:39.585Z"}, {"$type": "Document", "DocumentIndex": 192, "Title": "BaseNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\BaseNotificationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\BaseNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\BaseNotificationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\BaseNotificationService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T14:05:15.067Z"}, {"$type": "Document", "DocumentIndex": 193, "Title": "appsettings.beta.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\appsettings.beta.json", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\appsettings.beta.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\appsettings.beta.json", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\appsettings.beta.json", "ViewState": "AgIAAAUAAAAAAAAAAABRwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-24T09:49:34.474Z"}, {"$type": "Document", "DocumentIndex": 194, "Title": "FactoringDisbursementNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\FactoringDisbursementNotificationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\FactoringDisbursementNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\FactoringDisbursementNotificationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\FactoringDisbursementNotificationService.cs", "ViewState": "AgIAAAYAAAAAAAAAAIBJwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T09:42:23.469Z"}, {"$type": "Document", "DocumentIndex": 195, "Title": "FactoringFinalPaymentNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\FactoringFinalPaymentNotificationService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\FactoringFinalPaymentNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\FactoringFinalPaymentNotificationService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\Notification\\RequestTypes\\FactoringFinalPaymentNotificationService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T09:42:22.092Z"}, {"$type": "Document", "DocumentIndex": 196, "Title": "BasePaymentRequestHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Events\\PaymentRequest\\BasePaymentRequestHandler.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Handlers\\Events\\PaymentRequest\\BasePaymentRequestHandler.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Events\\PaymentRequest\\BasePaymentRequestHandler.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Handlers\\Events\\PaymentRequest\\BasePaymentRequestHandler.cs", "ViewState": "AgIAABQAAAAAAAAAAAAwwAkAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T11:29:17.163Z"}, {"$type": "Document", "DocumentIndex": 197, "Title": "FinalPaymentTransactionSyncService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\FinalPayment\\FinalPaymentTransactionSyncService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\FinalPayment\\FinalPaymentTransactionSyncService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\FinalPayment\\FinalPaymentTransactionSyncService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\FinalPayment\\FinalPaymentTransactionSyncService.cs", "ViewState": "AgIAAAkAAAAAAAAAAIBJwBQAAABhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T07:45:51.759Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 198, "Title": "FinalPaymentCompatibilityMapper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\FinalPayment\\FinalPaymentCompatibilityMapper.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\FinalPayment\\FinalPaymentCompatibilityMapper.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Services\\FinalPayment\\FinalPaymentCompatibilityMapper.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Services\\FinalPayment\\FinalPaymentCompatibilityMapper.cs", "ViewState": "AgIAAEcAAAAAAAAAAADwv14AAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T07:45:50.525Z"}, {"$type": "Document", "DocumentIndex": 200, "Title": "AsyncTaskMethodBuilder.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\943866804c986b3f1c185b0aaf5a6b2eb778d67c9141c5459bf5f86981d49a11\\AsyncTaskMethodBuilder.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\943866804c986b3f1c185b0aaf5a6b2eb778d67c9141c5459bf5f86981d49a11\\AsyncTaskMethodBuilder.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\943866804c986b3f1c185b0aaf5a6b2eb778d67c9141c5459bf5f86981d49a11\\AsyncTaskMethodBuilder.cs", "RelativeToolTip": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\943866804c986b3f1c185b0aaf5a6b2eb778d67c9141c5459bf5f86981d49a11\\AsyncTaskMethodBuilder.cs", "ViewState": "AgIAAG0AAAAAAAAAAIBJwIIAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T07:05:46.193Z"}, {"$type": "Document", "DocumentIndex": 199, "Title": "TaskCompletionSourceWithCancellation.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\6e0a018c11d0808fa6a9b0941636abc259f34e9b11938460a4bb00d5a256d364\\TaskCompletionSourceWithCancellation.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\6e0a018c11d0808fa6a9b0941636abc259f34e9b11938460a4bb00d5a256d364\\TaskCompletionSourceWithCancellation.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\6e0a018c11d0808fa6a9b0941636abc259f34e9b11938460a4bb00d5a256d364\\TaskCompletionSourceWithCancellation.cs", "RelativeToolTip": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\6e0a018c11d0808fa6a9b0941636abc259f34e9b11938460a4bb00d5a256d364\\TaskCompletionSourceWithCancellation.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T07:05:52.826Z"}, {"$type": "Document", "DocumentIndex": 205, "Title": "BlueTape.PaymentService.Application.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\BlueTape.PaymentService.Application.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAADMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-23T14:35:56.176Z"}, {"$type": "Document", "DocumentIndex": 246, "Title": "PausePaymentRequestViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\ViewModels\\PausePaymentRequestViewModel.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\ViewModels\\PausePaymentRequestViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\ViewModels\\PausePaymentRequestViewModel.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\ViewModels\\PausePaymentRequestViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T14:32:52.651Z"}, {"$type": "Document", "DocumentIndex": 201, "Title": "FileSystem.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\45558abf292363b6260c2ac82d509821cf8b7db701ef41c521afd31f6e2fa9a1\\FileSystem.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\45558abf292363b6260c2ac82d509821cf8b7db701ef41c521afd31f6e2fa9a1\\FileSystem.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\45558abf292363b6260c2ac82d509821cf8b7db701ef41c521afd31f6e2fa9a1\\FileSystem.cs", "RelativeToolTip": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\45558abf292363b6260c2ac82d509821cf8b7db701ef41c521afd31f6e2fa9a1\\FileSystem.cs", "ViewState": "AgIAAKsAAAAAAAAAAIBJwMAAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T14:29:09.917Z"}, {"$type": "Document", "DocumentIndex": 202, "Title": "BlueTape.Functions.PaymentJob.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj", "RelativeDocumentMoniker": "src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj", "RelativeToolTip": "src\\functions\\BlueTape.Functions.PaymentJob\\BlueTape.Functions.PaymentJob.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-23T14:46:08.531Z"}, {"$type": "Document", "DocumentIndex": 208, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\functions\\BlueTape.Functions.PaymentJob\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "src\\functions\\BlueTape.Functions.PaymentJob\\Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\functions\\BlueTape.Functions.PaymentJob\\Properties\\launchSettings.json", "RelativeToolTip": "src\\functions\\BlueTape.Functions.PaymentJob\\Properties\\launchSettings.json", "ViewState": "AgIAAAsAAAAAAAAAAAAwwCUAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-20T17:51:26.559Z"}, {"$type": "Document", "DocumentIndex": 203, "Title": "HelperControllerTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\ControllerTests\\HelperControllerTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\ControllerTests\\HelperControllerTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\ControllerTests\\HelperControllerTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\ControllerTests\\HelperControllerTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T14:37:39.149Z"}, {"$type": "Document", "DocumentIndex": 204, "Title": "GlobalUsings.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\GlobalUsings.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\GlobalUsings.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\GlobalUsings.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\GlobalUsings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T11:59:28.766Z"}, {"$type": "Document", "DocumentIndex": 206, "Title": "BlueTape.PaymentService.IntegrationTests.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj", "RelativeToolTip": "tests\\BlueTape.PaymentService.IntegrationTests\\BlueTape.PaymentService.IntegrationTests.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAACcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-23T14:33:24.139Z"}, {"$type": "Document", "DocumentIndex": 207, "Title": "MethodBaseInvoker.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\3d191bc8662e70a19deca42370d7982b8547e79362a27948a42aa9df305a69f5\\MethodBaseInvoker.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\3d191bc8662e70a19deca42370d7982b8547e79362a27948a42aa9df305a69f5\\MethodBaseInvoker.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\3d191bc8662e70a19deca42370d7982b8547e79362a27948a42aa9df305a69f5\\MethodBaseInvoker.cs", "RelativeToolTip": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\3d191bc8662e70a19deca42370d7982b8547e79362a27948a42aa9df305a69f5\\MethodBaseInvoker.cs", "ViewState": "AgIAAJcAAAAAAAAAAAAYwKsAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T14:30:50.876Z"}, {"$type": "Document", "DocumentIndex": 212, "Title": "EventLogRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Repositories\\EventLogRepository.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.DataAccess\\Repositories\\EventLogRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Repositories\\EventLogRepository.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.DataAccess\\Repositories\\EventLogRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T08:03:51.504Z"}, {"$type": "Document", "DocumentIndex": 211, "Title": "DrawDisbursementTransactionSyncServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.CompatibilityService.Tests\\Services\\DrawDisbursement\\DrawDisbursementTransactionSyncServiceTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.CompatibilityService.Tests\\Services\\DrawDisbursement\\DrawDisbursementTransactionSyncServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.CompatibilityService.Tests\\Services\\DrawDisbursement\\DrawDisbursementTransactionSyncServiceTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.CompatibilityService.Tests\\Services\\DrawDisbursement\\DrawDisbursementTransactionSyncServiceTests.cs", "ViewState": "AgIAABEAAAAAAAAAAAAqwCgAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T08:13:39.405Z"}, {"$type": "Document", "DocumentIndex": 209, "Title": "DrawDisbursementCompatibilityMapperTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.CompatibilityService.Tests\\Services\\DrawDisbursement\\DrawDisbursementCompatibilityMapperTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.CompatibilityService.Tests\\Services\\DrawDisbursement\\DrawDisbursementCompatibilityMapperTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.CompatibilityService.Tests\\Services\\DrawDisbursement\\DrawDisbursementCompatibilityMapperTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.CompatibilityService.Tests\\Services\\DrawDisbursement\\DrawDisbursementCompatibilityMapperTests.cs", "ViewState": "AgIAAJ8AAAAAAAAAAAAowAYBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T08:15:04.555Z"}, {"$type": "Document", "DocumentIndex": 210, "Title": "ObjectGraphTestExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\c59c6e0a029705cef818236b76b5baf2af9cc664d8358ac2c57af0271a1c7d11\\ObjectGraphTestExtensions.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\c59c6e0a029705cef818236b76b5baf2af9cc664d8358ac2c57af0271a1c7d11\\ObjectGraphTestExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\c59c6e0a029705cef818236b76b5baf2af9cc664d8358ac2c57af0271a1c7d11\\ObjectGraphTestExtensions.cs", "RelativeToolTip": "..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\c59c6e0a029705cef818236b76b5baf2af9cc664d8358ac2c57af0271a1c7d11\\ObjectGraphTestExtensions.cs", "ViewState": "AgIAAKMAAAAAAAAAAAAwwLgAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T08:50:01.313Z"}, {"$type": "Document", "DocumentIndex": 213, "Title": "PaymentRequestDetailsRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentRequestDetailsRepository.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentRequestDetailsRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentRequestDetailsRepository.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentRequestDetailsRepository.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAqwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T08:03:53.197Z"}, {"$type": "Document", "DocumentIndex": 214, "Title": "PaymentTransactionRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentTransactionRepository.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentTransactionRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentTransactionRepository.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentTransactionRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T08:03:55.193Z"}, {"$type": "Document", "DocumentIndex": 215, "Title": "PaymentTransactionHistoryRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentTransactionHistoryRepository.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentTransactionHistoryRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentTransactionHistoryRepository.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.DataAccess\\Repositories\\PaymentTransactionHistoryRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T08:03:48.413Z"}, {"$type": "Document", "DocumentIndex": 216, "Title": "SequencesRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Repositories\\SequencesRepository.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.DataAccess\\Repositories\\SequencesRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\Repositories\\SequencesRepository.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.DataAccess\\Repositories\\SequencesRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T08:03:47.132Z"}, {"$type": "Document", "DocumentIndex": 245, "Title": "PaymentRequestFilterQuery.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Queries\\PaymentRequestFilterQuery.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\Queries\\PaymentRequestFilterQuery.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\Queries\\PaymentRequestFilterQuery.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\Queries\\PaymentRequestFilterQuery.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T10:56:31.503Z"}, {"$type": "Document", "DocumentIndex": 244, "Title": "PaymentRequestFilter.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Entities\\Filters\\PaymentRequestFilter.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Entities\\Filters\\PaymentRequestFilter.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Entities\\Filters\\PaymentRequestFilter.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Entities\\Filters\\PaymentRequestFilter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T09:52:58.097Z"}, {"$type": "Document", "DocumentIndex": 217, "Title": "CompanyService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\CompanyService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\CompanyService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\CompanyService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\CompanyService.cs", "ViewState": "AgIAAFIAAAAAAAAAAAAuwHEAAACdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T10:14:33.352Z"}, {"$type": "Document", "DocumentIndex": 218, "Title": "ReportingService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.Reporting.Application\\Services\\ReportingService.cs", "RelativeDocumentMoniker": "src\\BlueTape.Reporting.Application\\Services\\ReportingService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.Reporting.Application\\Services\\ReportingService.cs", "RelativeToolTip": "src\\BlueTape.Reporting.Application\\Services\\ReportingService.cs", "ViewState": "AgIAACcAAAAAAAAAAAAwwEQAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T08:38:29.646Z"}, {"$type": "Document", "DocumentIndex": 219, "Title": "BaseTransactionEventHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\BaseTransactionEventHandler.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\BaseTransactionEventHandler.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\BaseTransactionEventHandler.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\BaseTransactionEventHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T11:19:37.051Z"}, {"$type": "Document", "DocumentIndex": 220, "Title": "TransactionClearedEventHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\TransactionClearedEventHandler.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\TransactionClearedEventHandler.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\TransactionClearedEventHandler.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Handlers\\Events\\Transaction\\TransactionClearedEventHandler.cs", "ViewState": "AgIAAFEAAAAAAAAAAAAIwCoAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T11:17:45.655Z"}, {"$type": "Document", "DocumentIndex": 221, "Title": "MessageProfile.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Mappers\\MessageProfile.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Mappers\\MessageProfile.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Mappers\\MessageProfile.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Mappers\\MessageProfile.cs", "ViewState": "AgIAANMAAAAAAAAAAADwv+cAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T12:25:45.679Z"}, {"$type": "Document", "DocumentIndex": 222, "Title": "TestingService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.TestUtilities\\Services\\TestingService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.TestUtilities\\Services\\TestingService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.TestUtilities\\Services\\TestingService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.TestUtilities\\Services\\TestingService.cs", "ViewState": "AgIAAFgCAAAAAAAAAAAswGwCAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T08:36:21.893Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 224, "Title": "IDrawRepaymentMessageSender.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.TestUtilities\\Abstractions\\Senders\\IDrawRepaymentMessageSender.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.TestUtilities\\Abstractions\\Senders\\IDrawRepaymentMessageSender.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.TestUtilities\\Abstractions\\Senders\\IDrawRepaymentMessageSender.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.TestUtilities\\Abstractions\\Senders\\IDrawRepaymentMessageSender.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T08:36:17.462Z"}, {"$type": "Document", "DocumentIndex": 225, "Title": "IInvoicePaymentMessageSender.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.TestUtilities\\Abstractions\\Senders\\IInvoicePaymentMessageSender.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.TestUtilities\\Abstractions\\Senders\\IInvoicePaymentMessageSender.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.TestUtilities\\Abstractions\\Senders\\IInvoicePaymentMessageSender.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.TestUtilities\\Abstractions\\Senders\\IInvoicePaymentMessageSender.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T08:36:16.264Z"}, {"$type": "Document", "DocumentIndex": 223, "Title": "DependencyRegistrar.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.TestUtilities\\DI\\DependencyRegistrar.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.TestUtilities\\DI\\DependencyRegistrar.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.TestUtilities\\DI\\DependencyRegistrar.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.TestUtilities\\DI\\DependencyRegistrar.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAUwBsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T12:34:28.926Z"}, {"$type": "Document", "DocumentIndex": 226, "Title": "DrawRepaymentRequestMessageSender.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.TestUtilities\\Senders\\DrawRepaymentRequestMessageSender.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.TestUtilities\\Senders\\DrawRepaymentRequestMessageSender.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.TestUtilities\\Senders\\DrawRepaymentRequestMessageSender.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.TestUtilities\\Senders\\DrawRepaymentRequestMessageSender.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T12:33:01.689Z"}, {"$type": "Document", "DocumentIndex": 228, "Title": "DrawRepaymentDrawDetails.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\DrawRepayment\\DrawRepaymentDrawDetails.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\DrawRepayment\\DrawRepaymentDrawDetails.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\DrawRepayment\\DrawRepaymentDrawDetails.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\DrawRepayment\\DrawRepaymentDrawDetails.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T12:24:08.788Z"}, {"$type": "Document", "DocumentIndex": 227, "Title": "InvoicePaymentRequestDetails.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\InvoicePayment\\InvoicePaymentRequestDetails.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\InvoicePayment\\InvoicePaymentRequestDetails.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\InvoicePayment\\InvoicePaymentRequestDetails.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\InvoicePayment\\InvoicePaymentRequestDetails.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAgwBEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T12:23:11.448Z"}, {"$type": "Document", "DocumentIndex": 229, "Title": "InvoicePaymentV2RequestMessage.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\InvoicePaymentV2\\InvoicePaymentV2RequestMessage.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\InvoicePaymentV2\\InvoicePaymentV2RequestMessage.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\InvoicePaymentV2\\InvoicePaymentV2RequestMessage.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Domain\\Messages\\PaymentRequestMessage\\InvoicePaymentV2\\InvoicePaymentV2RequestMessage.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T12:23:04.772Z"}, {"$type": "Document", "DocumentIndex": 243, "Title": "ITestingService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.TestUtilities\\Abstractions\\Services\\ITestingService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.TestUtilities\\Abstractions\\Services\\ITestingService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.TestUtilities\\Abstractions\\Services\\ITestingService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.TestUtilities\\Abstractions\\Services\\ITestingService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T12:19:30.5Z"}, {"$type": "Document", "DocumentIndex": 242, "Title": "CreateInvoiceRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\ViewModels\\CreateInvoiceRequest.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.API\\ViewModels\\CreateInvoiceRequest.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.API\\ViewModels\\CreateInvoiceRequest.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.API\\ViewModels\\CreateInvoiceRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T12:18:21.647Z"}, {"$type": "Document", "DocumentIndex": 230, "Title": "PaymentRequestValidator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Validators\\PaymentRequestValidator.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Validators\\PaymentRequestValidator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Validators\\PaymentRequestValidator.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Validators\\PaymentRequestValidator.cs", "ViewState": "AgIAAL4BAAAAAAAAAAAWwGMAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T12:10:46.396Z"}, {"$type": "Document", "DocumentIndex": 231, "Title": "DrawDisbursementRequestCreatorService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\DrawDisbursementRequestCreatorService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\DrawDisbursementRequestCreatorService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\DrawDisbursementRequestCreatorService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\PaymentRequestCreation\\DrawDisbursementRequestCreatorService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T12:10:35.616Z"}, {"$type": "Document", "DocumentIndex": 232, "Title": "TransactionNumberService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\TransactionNumberService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\TransactionNumberService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\TransactionNumberService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\TransactionNumberService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T12:10:30.733Z"}, {"$type": "Document", "DocumentIndex": 233, "Title": "PaymentRequestCommandServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.Application.Tests\\Services\\PaymentRequestCommandServiceTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.Application.Tests\\Services\\PaymentRequestCommandServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.Application.Tests\\Services\\PaymentRequestCommandServiceTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.Application.Tests\\Services\\PaymentRequestCommandServiceTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T11:59:08.459Z"}, {"$type": "Document", "DocumentIndex": 234, "Title": "FinalPaymentRequestCreationServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.Application.Tests\\Services\\FinalPaymentRequestCreationServiceTests.cs", "RelativeDocumentMoniker": "tests\\BlueTape.PaymentService.Application.Tests\\Services\\FinalPaymentRequestCreationServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.PaymentService.Application.Tests\\Services\\FinalPaymentRequestCreationServiceTests.cs", "RelativeToolTip": "tests\\BlueTape.PaymentService.Application.Tests\\Services\\FinalPaymentRequestCreationServiceTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T11:59:07.463Z"}, {"$type": "Document", "DocumentIndex": 235, "Title": "LoanServiceHttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\46ce419f48f1441bbef0fd8674d636b34800\\3f\\11e66c54\\LoanServiceHttpClient.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\46ce419f48f1441bbef0fd8674d636b34800\\3f\\11e66c54\\LoanServiceHttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\46ce419f48f1441bbef0fd8674d636b34800\\3f\\11e66c54\\LoanServiceHttpClient.cs", "RelativeToolTip": "..\\..\\..\\..\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\46ce419f48f1441bbef0fd8674d636b34800\\3f\\11e66c54\\LoanServiceHttpClient.cs", "ViewState": "AgIAAAQAAAAAAAAAAAAIwBQAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T11:48:30.242Z"}, {"$type": "Document", "DocumentIndex": 236, "Title": "AionService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\External\\AionService.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Services\\External\\AionService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Services\\External\\AionService.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Services\\External\\AionService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T11:46:22.695Z"}, {"$type": "Document", "DocumentIndex": 239, "Title": "CollectToFundingTransactionHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\CollectToFundingTransactionHandler.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\CollectToFundingTransactionHandler.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\CollectToFundingTransactionHandler.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\CollectToFundingTransactionHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T11:45:51.092Z"}, {"$type": "Document", "DocumentIndex": 240, "Title": "CollectLockboxOrDacaTransactionHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\CollectLockboxOrDacaTransactionHandler.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\CollectLockboxOrDacaTransactionHandler.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\CollectLockboxOrDacaTransactionHandler.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\CollectLockboxOrDacaTransactionHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T11:41:45.116Z"}, {"$type": "Document", "DocumentIndex": 238, "Title": "CollectFromMerchantTransactionHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\CollectFromMerchantTransactionHandler.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\CollectFromMerchantTransactionHandler.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\CollectFromMerchantTransactionHandler.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\CollectFromMerchantTransactionHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T11:41:44.426Z"}, {"$type": "Document", "DocumentIndex": 237, "Title": "CollectFromBorrowerTransactionHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\CollectFromBorrowerTransactionHandler.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\CollectFromBorrowerTransactionHandler.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\CollectFromBorrowerTransactionHandler.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.Application\\Handlers\\Commands\\PaymentCommands\\CollectFromBorrowerTransactionHandler.cs", "ViewState": "AgIAADwAAAAAAAAAAAAowE4AAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T11:40:36.986Z"}, {"$type": "Document", "DocumentIndex": 241, "Title": "FactoringFinalPaymentCompatibilityStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Strategies\\FactoringFinalPaymentCompatibilityStrategy.cs", "RelativeDocumentMoniker": "src\\BlueTape.PaymentService.CompatibilityService\\Strategies\\FactoringFinalPaymentCompatibilityStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\Strategies\\FactoringFinalPaymentCompatibilityStrategy.cs", "RelativeToolTip": "src\\BlueTape.PaymentService.CompatibilityService\\Strategies\\FactoringFinalPaymentCompatibilityStrategy.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T11:38:52.109Z"}]}]}]}