﻿using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.DataAccess.Mongo.Contexts;
using BlueTape.DataAccess.Mongo.Documents;
using BlueTape.Utilities.Extensions;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Polly;
using System.Threading;

namespace BlueTape.DataAccess.Mongo.Repositories;

public class LoanApplicationRepository(
    ILmsMongoDBContext context,
    ILogger<GenericRepository<LoanApplicationDocument>> logger)
    : GenericRepository<LoanApplicationDocument>(context, logger), ILoanApplicationRepository
{
    private readonly ILmsMongoDBContext _context = context;

    public async Task<List<LoanApplicationDocument>> GetApprovedLoanApplicationsByCompanyId(string companyId,
        CancellationToken ct)
    {
        var expression = Builders<LoanApplicationDocument>.Filter.Where(x => x.CompanyId == companyId &&
                                                                             (x.Status == "approved" ||
                                                                              x.Status == "closed"));

        return await Collection.Find(expression).ToListAsync(ct);
    }


    public async Task<List<LoanApplicationDocument>> GetRejectedLoanApplications(CancellationToken ct)
    {
        var expression = Builders<LoanApplicationDocument>.Filter.Where(x => x.Status == "rejected");

        return await Collection.Find(expression).ToListAsync(ct);
    }

    public async Task<List<LoanApplicationDocument>> GetApprovedLoanApplications(CancellationToken ct)
    {
        var expression = Builders<LoanApplicationDocument>.Filter.Where(x => x.Status == "approved");

        return await Collection.Find(expression).ToListAsync(ct);
    }

    public async Task<LoanApplicationDocument?> GetByLmsId(string lmsId, CancellationToken ct)
    {
        var expression = Builders<LoanApplicationDocument>.Filter.Where(x => x.LmsId == lmsId);

        return await Collection.Find(expression).FirstOrDefaultAsync(ct);
    }

    public async Task<IReadOnlyCollection<LoanApplicationDocument>> GetByLmsIds(IReadOnlyCollection<string> lmsId,
        CancellationToken ct)
    {
        var filterBuilder = Builders<LoanApplicationDocument>.Filter;

        // Create the filter
        var filter = filterBuilder.And(
            filterBuilder.Ne(doc => doc.LmsId, null), // LmsId is not null
            filterBuilder.In(doc => doc.LmsId, lmsId) // LmsId is in the list
        );

        return await Collection.Find(filter).ToListAsync(ct);
    }

    public async Task<IReadOnlyCollection<LightLoanApplicationDocument>> GetLightByLmsIds(IReadOnlyCollection<string> lmsId,
        CancellationToken ct)
    {
        var filterBuilder = Builders<LoanApplicationDocument>.Filter;

        var filter = filterBuilder.And(
            filterBuilder.Ne(doc => doc.LmsId, null),
            filterBuilder.In(doc => doc.LmsId, lmsId)
        );

        var projection = Builders<LoanApplicationDocument>.Projection
            .Include(x => x.Id)
            .Include(x => x.CreatedAt)
            .Include(x => x.UpdatedAt)
            .Include(x => x.CompanyId)
            .Include(x => x.Status)
            .Include(x => x.LmsId)
            .Include(x => x.Metadata)
            .Include(x => x.InvoiceDetails);

        var result = await Collection.Find(filter)
            .Project<LightLoanApplicationDocument>(projection)
            .ToListAsync(ct);

        return result;
    }

    public Task UpdateLoanAppById(string Id, UpdateLoanApplicationDocument updateLoanApplicationDocument, CancellationToken ct)
    {
        if (string.IsNullOrEmpty(Id)) return Task.CompletedTask;

        var filterBuilder = new FilterDefinitionBuilder<LoanApplicationDocument>();
        var filter = filterBuilder.Where(x => x.Id == Id);

        var updateDefinition = BuildUpdateDefinition(updateLoanApplicationDocument);

        return Collection.UpdateOneAsync(filter, updateDefinition, null, ct);
    }

    private static UpdateDefinition<LoanApplicationDocument> BuildUpdateDefinition(UpdateLoanApplicationDocument updateLoanApplication)
    {
        var updateBuilder = new UpdateDefinitionBuilder<LoanApplicationDocument>();
        var updateDefinition = updateBuilder
            .Set(x => x.UpdatedAt, DateTime.UtcNow);

        if (!string.IsNullOrEmpty(updateLoanApplication.LastPaymentDate))
            updateDefinition = updateDefinition.Set(x => x.LastPaymentDate, updateLoanApplication.LastPaymentDate);

        if (!string.IsNullOrEmpty(updateLoanApplication.NextPaymentDate))
            updateDefinition = updateDefinition.Set(x => x.NextPaymentDate, updateLoanApplication.NextPaymentDate);

        if (updateLoanApplication.NextPaymentAmount.HasValue)
            updateDefinition = updateDefinition.Set(x => x.NextPaymentAmount, updateLoanApplication.NextPaymentAmount);

        if (updateLoanApplication.RemainingAmount.HasValue)
            updateDefinition = updateDefinition.Set(x => x.RemainingAmount, updateLoanApplication.RemainingAmount);

        if (updateLoanApplication.ProcessingAmount.HasValue)
            updateDefinition = updateDefinition.Set(x => x.ProcessingAmount, updateLoanApplication.ProcessingAmount);

        if (updateLoanApplication.PastDueAmount.HasValue)
            updateDefinition = updateDefinition.Set(x => x.PastDueAmount, updateLoanApplication.PastDueAmount);

        return updateDefinition;
    }
}