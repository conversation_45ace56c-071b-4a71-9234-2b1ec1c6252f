﻿using BlueTape.PaymentService.Application.Services;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using BlueTape.Utilities.Providers;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.Logging;
using System.Data;

namespace BlueTape.PaymentService.Application.Tests.Services;

public class SequenceNumberServiceTests
{
    private readonly IPaymentRequestRepository _paymentRequestRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IDateProvider _dateProvider;
    private readonly ILogger<SequenceNumberService> _logger;
    private readonly SequenceNumberService _service;

    public SequenceNumberServiceTests()
    {
        _paymentRequestRepository = Substitute.For<IPaymentRequestRepository>();
        _unitOfWork = Substitute.For<IUnitOfWork>();
        _dateProvider = Substitute.For<IDateProvider>();
        _logger = Substitute.For<ILogger<SequenceNumberService>>();

        _service = new SequenceNumberService(
            _paymentRequestRepository,
            _unitOfWork,
            _dateProvider,
            _logger);
    }

    [Fact]
    public async Task UpdateSequenceNumbers_ShouldUpdateSpecifiedPayments()
    {
        // Arrange
        var paymentRequestIds = new List<Guid> { Guid.NewGuid(), Guid.NewGuid(), Guid.NewGuid() };
        var updatedBy = "test-user";
        var currentDateTime = DateTime.UtcNow;

        var paymentRequests = paymentRequestIds.Select((id, index) => new PaymentRequestEntity
        {
            Id = id,
            PaymentMethod = PaymentMethod.Ach,
            RequestType = PaymentRequestType.FinalPayment,
            Status = PaymentRequestStatus.Requested,
            ConfirmedAt = DateTime.UtcNow.AddMinutes(-index),
            SequenceNumber = 0
        }).ToList();

        _paymentRequestRepository.GetByIds(paymentRequestIds, Arg.Any<CancellationToken>())
            .Returns(paymentRequests);

        _dateProvider.CurrentDateTime.Returns(currentDateTime);

        var transaction = Substitute.For<IDbContextTransaction>();
        _unitOfWork.BeginTransactionAsync(Arg.Any<CancellationToken>())
            .Returns(transaction);

        // Act
        var result = await _service.UpdateSequenceNumbers(paymentRequestIds, updatedBy, CancellationToken.None);

        // Assert
        Assert.Equal(paymentRequestIds.Count, result);

        // Verify that the specified payments got sequence numbers
        for (int i = 0; i < paymentRequests.Count; i++)
        {
            Assert.Equal(i + 1, paymentRequests[i].SequenceNumber);
            Assert.Equal(updatedBy, paymentRequests[i].UpdatedBy);
            Assert.Equal(currentDateTime, paymentRequests[i].UpdatedAt);
        }

        await _paymentRequestRepository.Received(paymentRequestIds.Count)
            .Update(Arg.Any<PaymentRequestEntity>(), Arg.Any<CancellationToken>());
        await transaction.Received(1).CommitAsync(Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task ValidateReorderingRestrictions_ShouldReturnTrue_WhenAllPaymentsFound()
    {
        // Arrange
        var paymentRequestIds = new List<Guid> { Guid.NewGuid(), Guid.NewGuid() };
        var paymentRequests = paymentRequestIds.Select(id => new PaymentRequestEntity
        {
            Id = id,
            PaymentMethod = PaymentMethod.Ach,
            RequestType = PaymentRequestType.FinalPayment,
            Status = PaymentRequestStatus.Requested
        }).ToList();

        _paymentRequestRepository.GetByIds(paymentRequestIds, Arg.Any<CancellationToken>())
            .Returns(paymentRequests);

        // Act
        var result = await _service.ValidateReorderingRestrictions(paymentRequestIds, CancellationToken.None);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ValidateReorderingRestrictions_ShouldReturnFalse_WhenSomePaymentsNotFound()
    {
        // Arrange
        var paymentRequestIds = new List<Guid> { Guid.NewGuid(), Guid.NewGuid() };
        var paymentRequests = new List<PaymentRequestEntity>
        {
            new()
            {
                Id = paymentRequestIds[0],
                PaymentMethod = PaymentMethod.Ach,
                RequestType = PaymentRequestType.FinalPayment,
                Status = PaymentRequestStatus.Requested
            }
            // Missing second payment request
        };

        _paymentRequestRepository.GetByIds(paymentRequestIds, Arg.Any<CancellationToken>())
            .Returns(paymentRequests);

        // Act
        var result = await _service.ValidateReorderingRestrictions(paymentRequestIds, CancellationToken.None);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task UpdateSequenceNumbers_ShouldThrowException_WhenValidationFails()
    {
        // Arrange
        var paymentRequestIds = new List<Guid> { Guid.NewGuid(), Guid.NewGuid() };
        var updatedBy = "test-user";

        _paymentRequestRepository.GetByIds(paymentRequestIds, Arg.Any<CancellationToken>())
            .Returns(new List<PaymentRequestEntity>
            {
                new() { Id = paymentRequestIds[0], PaymentMethod = PaymentMethod.Ach, RequestType = PaymentRequestType.FinalPayment, Status = PaymentRequestStatus.Requested }
                // Missing second payment request to cause validation failure
            });

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _service.UpdateSequenceNumbers(paymentRequestIds, updatedBy, CancellationToken.None));

        Assert.Contains("cannot be reordered together", exception.Message);
    }

    [Fact]
    public async Task UpdateSequenceNumbers_ShouldThrowException_WhenTooManyPayments()
    {
        // Arrange
        var paymentRequestIds = Enumerable.Range(0, 101).Select(_ => Guid.NewGuid()).ToList(); // More than 100
        var updatedBy = "test-user";

        // Create payment requests for ALL IDs so validation passes and we reach the count check
        var paymentRequests = paymentRequestIds.Select(id => new PaymentRequestEntity
        {
            Id = id,
            PaymentMethod = PaymentMethod.Ach,
            RequestType = PaymentRequestType.FinalPayment,
            Status = PaymentRequestStatus.Requested
        }).ToList();

        _paymentRequestRepository.GetByIds(paymentRequestIds, Arg.Any<CancellationToken>())
            .Returns(paymentRequests);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _service.UpdateSequenceNumbers(paymentRequestIds, updatedBy, CancellationToken.None));

        Assert.Contains("Cannot reorder more than 100 payments at once", exception.Message);
    }

    [Fact]
    public async Task UpdateSequenceNumbers_ShouldReturnZero_WhenEmptyList()
    {
        // Arrange
        var paymentRequestIds = new List<Guid>();
        var updatedBy = "test-user";

        // Act
        var result = await _service.UpdateSequenceNumbers(paymentRequestIds, updatedBy, CancellationToken.None);

        // Assert
        Assert.Equal(0, result);
    }

    [Fact]
    public async Task ResetAllSequenceNumbers_ShouldCallRepositoryAndReturnCount()
    {
        // Arrange
        var updatedBy = "test-user";
        var currentDateTime = DateTime.UtcNow;
        var expectedCount = 5;

        _dateProvider.CurrentDateTime.Returns(currentDateTime);
        _paymentRequestRepository.ResetAllSequenceNumbers(updatedBy, currentDateTime, Arg.Any<CancellationToken>())
            .Returns(expectedCount);

        var transaction = Substitute.For<IDbContextTransaction>();
        _unitOfWork.BeginTransactionAsync(Arg.Any<CancellationToken>())
            .Returns(transaction);

        // Act
        var result = await _service.ResetAllSequenceNumbers(updatedBy, CancellationToken.None);

        // Assert
        Assert.Equal(expectedCount, result);
        await _paymentRequestRepository.Received(1)
            .ResetAllSequenceNumbers(updatedBy, currentDateTime, Arg.Any<CancellationToken>());
        await transaction.Received(1).CommitAsync(Arg.Any<CancellationToken>());
    }
}
