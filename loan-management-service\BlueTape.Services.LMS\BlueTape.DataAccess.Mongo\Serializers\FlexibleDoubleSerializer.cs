﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;

namespace BlueTape.DataAccess.Mongo.Serializers;

public class FlexibleDoubleSerializer : SerializerBase<double?>
{
    public override double? Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        var bsonReader = context.Reader;
        switch (bsonReader.CurrentBsonType)
        {
            case BsonType.Null:
                bsonReader.ReadNull();
                return null;
            case BsonType.Int32:
                return bsonReader.ReadInt32();
            case BsonType.Int64:
                return bsonReader.ReadInt64();
            case BsonType.Double:
                return bsonReader.ReadDouble();
            case BsonType.Decimal128:
                return (double)bsonReader.ReadDecimal128();
            case BsonType.String:
                return double.TryParse(bsonReader.ReadString(), out var result) ? result : (double?)null;
            case BsonType.Undefined:
                bsonReader.ReadUndefined();
                return null;
            default:
                var message = $"Cannot deserialize BsonType {bsonReader.CurrentBsonType} to double.";
                throw new BsonSerializationException(message);
        }
    }

    public override void Serialize(BsonSerializationContext context, BsonSerializationArgs args, double? value)
    {
        if (value.HasValue)
        {
            context.Writer.WriteDouble(value.Value);
        }
        else
        {
            context.Writer.WriteNull();
        }
    }
}
