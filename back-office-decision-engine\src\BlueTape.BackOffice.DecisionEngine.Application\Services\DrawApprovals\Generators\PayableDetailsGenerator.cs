using BlueTape.BackOffice.DecisionEngine.Application.Extensions;
using BlueTape.BackOffice.DecisionEngine.Application.Services.DrawApprovals.Models.Details;
using BlueTape.BackOffice.DecisionEngine.Application.Services.DrawApprovals.Models.List;
using BlueTape.CompanyService.Companies;
using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.OBS.DTOs.Draft;
using BlueTape.OBS.DTOs.DrawApproval.Responses;
using BlueTape.OBS.DTOs.Linqpal;
using BlueTape.OBS.Enums;

namespace BlueTape.BackOffice.DecisionEngine.Application.Services.DrawApprovals.Generators;

public static class PayableDetailsGenerator
{
    public static PayablesDetails GeneratePayableDetails(DrawApprovalDto? drawApproval, IEnumerable<InvoiceModel> invoices)
    {
        var payablesCount = drawApproval?.Payables.Count ?? 0;

        decimal sum = 0;
        if (drawApproval is not null)
        {
            sum = drawApproval.Payables.Any(x => x.Type == PayableType.Invoice)
                ? drawApproval.Payables.Where(x => x.Type == PayableType.Invoice).Sum(x => x.Amount)
                : drawApproval.Payables.Where(x => x.Type == PayableType.Quote).Sum(x => x.Amount);
        }

        var payablesDetails = new PayablesDetails
        {
            SumAmount = sum,
            PayablesCount = payablesCount,
        };

        var quotePayable = drawApproval?.Payables.FirstOrDefault(x => x.Type == PayableType.Quote);
        payablesDetails.HasQuote = quotePayable != null;
        var hasQuote = payablesDetails.HasQuote.Value;

        if (payablesCount == 1 || hasQuote)
        {
            var invoice = hasQuote ?
                invoices.FirstOrDefault(x => x.Id == quotePayable?.Id) :
                invoices.FirstOrDefault(x => x.Id == drawApproval.Payables.FirstOrDefault(p => p.Type == PayableType.Invoice)?.Id);
            payablesDetails.DueDate = invoice?.InvoiceDueDate.DateTime.ToCstDate();
            payablesDetails.Number = invoice?.InvoiceNumber;
            payablesDetails.SumAmount = hasQuote ? invoice?.TotalAmount ?? sum : sum;
            payablesDetails.Status  = invoice?.Status;
        }

        return payablesDetails;
    }

    public static List<DrawPayablesDetails> GenerateDrawPayablesDetails(IEnumerable<InvoiceModel> invoices, IEnumerable<InvoiceStatusResponse>? invoicesStatuses, CompanyModel? merchant, DraftDto? draft)
    {
        return invoices.Select(x =>
        {
            var dateOfUpload = x.CreatedAt.ToDateOnly();
            return new DrawPayablesDetails()
            {
                DueDate = x.InvoiceDueDate.DateTime.ToCstDate(),
                Number = x.InvoiceNumber,
                Url = x.Document?.Url,
                MaterialSubTotal = x.MaterialSubtotal,
                TaxAmount = x.TaxAmount,
                TotalAmount = x.TotalAmount,
                DateOfUpload = dateOfUpload,
                Date = x.InvoiceDate.DateTime.ToCstDate(),
                Status = invoicesStatuses?.FirstOrDefault(i => i.Id == x.Id)?.Status ?? x.Status,
                Type = x.Type,
                HasQuote = !string.IsNullOrEmpty(x.QuoteId),
                ShippingAddress = x.Address,
                Attention = x.Attention,
                SupplierDetails = new PayablesSupplierDetails()
                {
                    Id = merchant?.Id ?? string.Empty,
                    Email = Convert.ToString(draft?.Data?.BusinessOwner?.Items?.FirstOrDefault(i => i.Identifier != null && i.Identifier.Equals("email", StringComparison.InvariantCultureIgnoreCase))?.Content) ?? string.Empty,
                    Phone = Convert.ToString(draft?.Data?.BusinessOwner?.Items?.FirstOrDefault(i => i.Identifier != null && i.Identifier.Equals("phone", StringComparison.InvariantCultureIgnoreCase))?.Content) ?? string.Empty,
                    Name = merchant?.Name
                }
            };
        }).ToList();
    }
}
