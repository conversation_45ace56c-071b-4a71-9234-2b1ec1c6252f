using BlueTape.BackOffice.DecisionEngine.Domain.Models;
using BlueTape.Integrations.Aion.Transactions;
using BlueTape.OBS.DTOs;
using BlueTape.PaymentService.Models;
using BlueTape.Utilities.Models;

namespace BlueTape.BackOffice.DecisionEngine.DataAccess.External.PaymentApi.Proxy;

public interface IPaymentApiProxy
{
    Task ApprovePaymentRequest(Guid id, ApprovePaymentRequestModel request, string userId, CancellationToken cancellationToken);
    Task<List<AionAccount?>?> GetAllAccounts(string? paymentProvider, CancellationToken cancellationToken);
    Task<List<AionAccountShort?>?> GetAccounts(string? paymentProvider, CancellationToken cancellationToken);
    Task<GetQueryWithPaginationResultDto<PaymentRequestModel>?> GetPaymentRequests(PaymentRequestFilterQuery query, CancellationToken cancellationToken);
    Task<PaginatedResponse<TransactionListObj>?> GetAccountTransactions(TransactionsQuery request, CancellationToken ct);
    Task CancelPaymentByDrawId(Guid drawId, string userId, CancellationToken ct);
    Task<PaymentRequestModel?> GetById(Guid id, CancellationToken cancellationToken);
    Task<IEnumerable<PaymentRequestModel>> GetDisbursementQueuesPaymentRequests(string provider, string subscriptionCode, CancellationToken cancellationToken);
    Task UpdateSequenceOrder(List<Guid> paymentRequestIds, string userId, CancellationToken cancellationToken);
}
