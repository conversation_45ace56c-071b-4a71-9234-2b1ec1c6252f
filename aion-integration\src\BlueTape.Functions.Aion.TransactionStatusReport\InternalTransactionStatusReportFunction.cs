using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Abstractions.MessageSenders;
using BlueTape.Aion.Application.Constants;
using BlueTape.Aion.Domain.Constants;
using BlueTape.Aion.Domain.Extensions;
using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using BlueTape.Common.ExceptionHandling.Extensions;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.ServiceBusMessaging.Attributes;
using BueTape.Aion.Infrastructure.ServiceBusMessages;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Serilog.Context;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.Functions.Aion.TransactionStatusReport;

[ExcludeFromCodeCoverage]
public class InternalTransactionStatusReportFunction(
    ITraceIdAccessor traceIdAccessor,
    IErrorNotificationService notificationService,
    ILogger<InternalTransactionStatusReportFunction> logger,
    IAionInternalTransactionMessageSender aionInternalTransactionMessageSender,
    IConfiguration configuration,
    IKeyVaultService keyVaultService)
{
    private const string BlueTapeCorrelationId = "BlueTapeCorrelationId";

    [Function("InternalTransactionStatusReportFunction")]
    public async Task Run([TimerTrigger($"%{AionFunctionConstants.AionInternalTransactionJobPeriod}%")] TimerInfo myTimer, CancellationToken ct)
    {
        traceIdAccessor.TraceId = $"{Guid.NewGuid()}-{nameof(InternalTransactionStatusReportFunction)}";

        using (GlobalLogContext.PushProperty("Method", "Scheduler"))
        using (GlobalLogContext.PushProperty(BlueTapeCorrelationId, traceIdAccessor.TraceId))
        {
            try
            {
                logger.LogInformation("Start InternalTransactionStatusReportFunction status report");
                var environment = Environment.GetEnvironmentVariable(EnvironmentConstants.Environment);

                if (environment?.IsEnvironmentDevOrBeta() ?? false)
                {
                    logger.LogInformation("InternalTransactionStatusReportFunction status report function execution is suppresed due to environment: {environment}", environment);
                    return;
                }

                // Check if we should execute based on high frequency mode flag
                if (!await ShouldExecuteBasedOnFrequencyMode(environment, ct))
                {
                    logger.LogInformation("InternalTransactionStatusReportFunction execution skipped - not in high frequency mode and not time for normal execution");
                    return;
                }

                var body = new AionInternalTransactionMessage();
                var paymentSubscriptions = Enum.GetValues<PaymentSubscriptionType>();
                foreach (var paymentSubscription in paymentSubscriptions)
                {
                    body.AionInternalTransactionReportRequest.InternalTransactionReportRequests.Add(new()
                    {
                        InternalTransactionPage = 1,
                        PaymentSubscriptionType = paymentSubscription
                    });
                }

                await aionInternalTransactionMessageSender.SendMessage(new ServiceBusMessageBt<AionInternalTransactionMessage>(body, new ServiceBusMessageAttributes()
                {
                    CorrelationId = traceIdAccessor.TraceId,
                    MessageId = traceIdAccessor.TraceId
                }), ct);
            }
            catch (DomainException domainEx)
            {
                await notificationService.Notify(domainEx.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), "AionService"), ct);
                logger.LogError(domainEx, "InternalTransactionStatusReportFunction status scheduler function failed: {message}", domainEx.Message);
                throw;
            }
            catch (Exception ex)
            {
                await notificationService.Notify(ex.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), "AionService", maxMessageLength: 400), ct);
                logger.LogError(ex, "InternalTransactionStatusReportFunction status scheduler function failed: {message}", ex.Message);
                throw;
            }
        }
    }

    private async Task<bool> ShouldExecuteBasedOnFrequencyMode(string environment, CancellationToken ct)
    {
        try
        {
            if (environment.IsEnvironmentNotProd())
                return true;

            var isHighFrequencyMode = await GetHighFrequencyModeFlag(ct);

            if (isHighFrequencyMode)
            {
                logger.LogInformation("High frequency mode is enabled - executing function");
                return true;
            }

            var currentMinute = DateTime.UtcNow.Minute;
            var shouldExecute = currentMinute is >= 14 and <= 16; ; // Execute every 15 minutes at :00, :15, :30, :45

            if (shouldExecute)
            {
                logger.LogInformation("Normal frequency mode - executing function at scheduled time");
            }
            else
            {
                logger.LogDebug("Normal frequency mode - skipping execution (not scheduled time)");
            }

            return shouldExecute;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error checking frequency mode flag - defaulting to execute");
            return true; // Default to executing if we can't check the flag
        }
    }

    private async Task<bool> GetHighFrequencyModeFlag(CancellationToken ct)
    {
        try
        {
            // First try configuration (which includes Key Vault values)
            var configValue = configuration[KeyVaultKeysConstants.AionTransactionReportHighFrequencyMode];
            if (!string.IsNullOrWhiteSpace(configValue) && bool.TryParse(configValue, out var configResult))
            {
                return configResult;
            }

            // Fallback to direct Key Vault access
            var secretValue = await keyVaultService.GetSecret(KeyVaultKeysConstants.AionTransactionReportHighFrequencyMode);
            if (bool.TryParse(secretValue, out var result))
            {
                return result;
            }

            logger.LogWarning("Invalid or missing high frequency mode flag value - defaulting to false");
            return false;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve high frequency mode flag - defaulting to false");
            return false;
        }
    }
}
