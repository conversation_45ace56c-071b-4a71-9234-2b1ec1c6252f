﻿using BlueTape.PaymentService.DataAccess.Contexts;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Entities.Base;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using BlueTape.PaymentService.UnitOfWork.Records;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using System.Linq.Expressions;

namespace BlueTape.PaymentService.UnitOfWork;

public class UnitOfWork(
    DatabaseContext context,
    IUoWGenericRepository<PaymentRequestEntity> paymentRequestRepository,
    IUoWGenericRepository<PaymentRequestDetailsEntity> paymentRequestDetailsRepository,
    IUoWGenericRepository<PaymentRequestCommandEntity> paymentRequestCommandRepository,
    IUoWGenericRepository<PaymentRequestPayableEntity> paymentRequestPayableRepository,
    IUoWGenericRepository<PaymentTransactionHistoryEntity> paymentTransactionHistoryRepository,
    IUoWGenericRepository<PaymentTransactionEntity> paymentTransactionRepository,
    IUoWGenericRepository<PaymentRequestFeeEntity> paymentRequestFeeRepository,
    IUoWGenericRepository<ForbiddenCompanyEntity> forbiddenCompanyRepository,
    IUoWGenericRepository<EventLogEntity> paymentRequestNotification) : IUnitOfWork
{
    private bool _disposed;

    private static readonly char[] IncludeSeparators = [','];

    public IUoWGenericRepository<ForbiddenCompanyEntity> ForbiddenCompanyRepository { get; } = forbiddenCompanyRepository;

    public IUoWGenericRepository<PaymentRequestEntity> PaymentRequestRepository { get; } = paymentRequestRepository;

    public IUoWGenericRepository<PaymentRequestDetailsEntity> PaymentRequestDetailsRepository { get; } = paymentRequestDetailsRepository;

    public IUoWGenericRepository<PaymentRequestCommandEntity> PaymentRequestCommandRepository { get; } = paymentRequestCommandRepository;

    public IUoWGenericRepository<PaymentRequestPayableEntity> PaymentRequestPayableRepository { get; } = paymentRequestPayableRepository;

    public IUoWGenericRepository<PaymentTransactionHistoryEntity> PaymentTransactionHistoryRepository { get; } = paymentTransactionHistoryRepository;

    public IUoWGenericRepository<PaymentTransactionEntity> PaymentTransactionRepository { get; } = paymentTransactionRepository;

    public IUoWGenericRepository<PaymentRequestFeeEntity> PaymentRequestFeeRepository { get; } = paymentRequestFeeRepository;

    public IUoWGenericRepository<EventLogEntity> EventLogRepository { get; } = paymentRequestNotification;

    public async Task<IEnumerable<TEntity>> Get<TEntity>(
        CancellationToken cancellationToken,
        Expression<Func<TEntity, bool>>? filter = null,
        Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>>? orderBy = null,
        string includeProperties = "") where TEntity : EntityWithId
    {
        IQueryable<TEntity> query = context.Set<TEntity>();

        if (filter != null)
        {
            query = query.Where(filter);
        }

        query = EnrichQueryWithIncludeProperties(query, includeProperties);

        if (orderBy != null)
        {
            return await orderBy(query).ToListAsync(cancellationToken);
        }

        return await query.ToListAsync(cancellationToken);
    }

    public Task<TEntity?> GetById<TEntity>(Guid id, CancellationToken cancellationToken, string includeProperties = "")
        where TEntity : EntityWithId
    {
        IQueryable<TEntity> query = context.Set<TEntity>();
        query = EnrichQueryWithIncludeProperties(query, includeProperties);

        return query.FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
    }

    public Task<PaymentRequestCommandEntity?> GetCommandByTransactionId(Guid transactionId, CancellationToken ct)
    {
        return context.Set<PaymentRequestCommandEntity>()
            .FirstOrDefaultAsync(x => x.TransactionId == transactionId, ct);
    }

    public Task<List<PaymentRequestCommandEntity>> GetCommandsByPaymentRequestId(Guid paymentRequestId, CancellationToken ct)
    {
        return context.Set<PaymentRequestCommandEntity>()
            .Include(x => x.Transaction)
            .Include(x => x.PaymentRequest)
            .Where(x => x.PaymentRequest != null && x.PaymentRequestId == paymentRequestId)
            .ToListAsync(ct);
    }

    public async Task SaveRangePaymentRequestsChanges(List<PaymentRequestEntity> paymentRequests, List<PaymentRequestCommandEntity> commands, List<PaymentTransactionPayload> transactionPayloads, CancellationToken ct)
    {
        var histories = new List<PaymentTransactionHistoryEntity>();
        transactionPayloads.ForEach(x => histories.Add(x.GetTransactionHistoryFromPayload()));

        await PaymentTransactionHistoryRepository.InsertRange(histories, ct);
        await PaymentRequestCommandRepository.UpdateRange(commands, ct);
        await PaymentRequestRepository.UpdateRange(paymentRequests.Distinct(), ct);

        await SaveAsync(ct);
    }

    public async Task SaveRangeTransactionChangesWithoutHistories(List<PaymentRequestCommandEntity> commands, List<PaymentTransactionEntity> transactions, CancellationToken ct)
    {
        await PaymentRequestCommandRepository.UpdateRange(commands, ct);
        await PaymentTransactionRepository.UpdateRange(transactions, ct);

        await SaveAsync(ct);
    }

    public async Task SaveTransactionChanges(PaymentTransactionEntity transaction, PaymentRequestCommandEntity command,
        PaymentTransactionPayload transactionPayload, CancellationToken ct)
    {
        var history = transactionPayload.GetTransactionHistoryFromPayload();

        await PaymentTransactionRepository.Update(transaction, ct);
        await PaymentRequestCommandRepository.Update(command, ct);
        await PaymentTransactionHistoryRepository.Insert(history, ct);

        await SaveAsync(ct);
    }

    public async Task SaveTransactionChangesWithoutCommand(PaymentTransactionEntity transaction, PaymentTransactionPayload transactionPayload,
        CancellationToken ct)
    {
        var history = transactionPayload.GetTransactionHistoryFromPayload();

        await PaymentTransactionRepository.Update(transaction, ct);
        await PaymentTransactionHistoryRepository.Insert(history, ct);

        await SaveAsync(ct);
    }

    public async Task UpdateTransactionAndCommandWithoutSaving(PaymentTransactionEntity transaction, PaymentRequestCommandEntity command,
        CancellationToken ct)
    {
        await PaymentTransactionRepository.Update(transaction, ct);
        await PaymentRequestCommandRepository.Update(command, ct);
    }

    public async Task<PaymentTransactionEntity> UpdateCommandAndInsertTransactionWithoutSaving(PaymentRequestCommandEntity command,
        PaymentTransactionEntity newTransaction, CancellationToken ct)
    {
        await PaymentRequestCommandRepository.Update(command, ct);
        return await PaymentTransactionRepository.Insert(newTransaction, ct);
    }

    public async Task UpdatePaymentRequestAndInsertCommand(PaymentRequestEntity paymentRequest, PaymentRequestCommandEntity newCommand,
        CancellationToken ct)
    {
        await PaymentRequestRepository.Update(paymentRequest, ct);
        await PaymentRequestCommandRepository.Insert(newCommand, ct);

        await SaveAsync(ct);
    }

    public async Task HandleRollbackTransactions(PaymentRequestCommandEntity recalledCommand, List<PaymentTransactionEntity> rollbackTransactions,
        List<PaymentRequestCommandEntity> rollbackCommands, CancellationToken ct)
    {
        await PaymentRequestCommandRepository.Update(recalledCommand, ct);
        await PaymentTransactionRepository.InsertRange(rollbackTransactions, ct);
        await PaymentRequestCommandRepository.InsertRange(rollbackCommands, ct);

        await SaveAsync(ct);
    }

    public async Task HandlePaymentRequestStatusChange(List<PaymentTransactionEntity> transactions,
        List<PaymentTransactionPayload> transactionPayloads, PaymentRequestEntity paymentRequest,
        List<PaymentRequestCommandEntity> paymentRequestCommandItems, CommandStatus status, string updatedBy, CancellationToken ct)
    {
        var histories = new List<PaymentTransactionHistoryEntity>();
        transactionPayloads.ForEach(x => histories.Add(x.GetTransactionHistoryFromPayload()));

        if (paymentRequestCommandItems.Count != 0)
        {
            paymentRequestCommandItems.ForEach(item =>
            {
                item.Status = status;
                item.UpdatedBy = updatedBy;
            });

            await PaymentRequestCommandRepository.UpdateRange(paymentRequestCommandItems, ct);
        }

        await PaymentTransactionRepository.UpdateRange(transactions, ct);
        await PaymentTransactionHistoryRepository.InsertRange(histories, ct);
        await PaymentRequestRepository.Update(paymentRequest, ct);

        await SaveAsync(ct);
    }

    public async Task InsertCommandAndTransaction(PaymentRequestCommandEntity command, PaymentTransactionEntity transaction, CancellationToken ct)
    {
        await PaymentRequestCommandRepository.Insert(command, ct);
        await PaymentTransactionRepository.Insert(transaction, ct);

        await SaveAsync(ct);
    }

    public async Task InsertCommandsAndTransactionsRange(List<PaymentRequestCommandEntity> commands, List<PaymentTransactionEntity> transactions, CancellationToken ct)
    {
        await PaymentRequestCommandRepository.InsertRange(commands, ct);
        await PaymentTransactionRepository.InsertRange(transactions, ct);

        await SaveAsync(ct);
    }

    public async Task UpdateCommand(PaymentRequestCommandEntity command, CancellationToken ct)
    {
        await PaymentRequestCommandRepository.Update(command, ct);
        await SaveAsync(ct);
    }

    public async Task UpdateCommandsRange(List<PaymentRequestCommandEntity> commands, CancellationToken ct)
    {
        await PaymentRequestCommandRepository.UpdateRange(commands, ct);
        await SaveAsync(ct);
    }

    public async Task UpdatePaymentRequest(PaymentRequestEntity paymentRequest, CancellationToken ct)
    {
        await PaymentRequestRepository.Update(paymentRequest, ct);
        await SaveAsync(ct);
    }

    public async Task UpdatePaymentRequestRange(IEnumerable<PaymentRequestEntity> paymentRequests, CancellationToken ct)
    {
        await PaymentRequestRepository.UpdateRange(paymentRequests, ct);
        await SaveAsync(ct);
    }

    public async Task UpdateTransaction(PaymentTransactionEntity transaction, CancellationToken ct)
    {
        await PaymentTransactionRepository.Update(transaction, ct);
        await SaveAsync(ct);
    }

    public async Task InsertPaymentRequestWithoutSaving(PaymentRequestEntity paymentRequest, CancellationToken ct)
        => await PaymentRequestRepository.Insert(paymentRequest, ct);

    public async Task InsertPaymentRequestDetails(PaymentRequestDetailsEntity details, CancellationToken ct)
    {
        await PaymentRequestDetailsRepository.Insert(details, ct);
        await SaveAsync(ct);
    }

    public async Task UpdatePaymentRequestDetails(PaymentRequestDetailsEntity details, CancellationToken ct)
    {
        await PaymentRequestDetailsRepository.Update(details, ct);
        await SaveAsync(ct);
    }

    public async Task<bool> AreCompaniesPaymentsForbidden(IEnumerable<string> companyIds, CancellationToken ct)
    {
        var companies = await ForbiddenCompanyRepository.Get(ct, x => companyIds.Contains(x.CompanyId));
        return companies.Any();
    }

    public Task<IDbContextTransaction> BeginTransactionAsync(CancellationToken cancellationToken)
    {
        return context.Database.BeginTransactionAsync(cancellationToken);
    }

    public Task<int> SaveAsync(CancellationToken cancellationToken)
    {
        return context.SaveChangesAsync(cancellationToken);
    }

    public async Task<List<PaymentTransactionHistoryEntity?>> GetMostRecentNotEnoughBalanceHistoryItemsByTransactionIds(IEnumerable<Guid> transactionIds, CancellationToken ct)
        => await context.Set<PaymentTransactionHistoryEntity>()
                .Where(x => transactionIds.Contains(x.TransactionId) && x.ResultCode != null && x.ResultCode.Equals(BlueTapePaymentErrorCodes.BT001.ToString()))
                .GroupBy(x => x.TransactionId)
                .Select(x => x.OrderByDescending(x => x.CreatedAt).FirstOrDefault())
                .ToListAsync(ct);

    public async Task<List<PaymentTransactionEntity>> GetAchPaymentTransactionsByDateRange(DateTime startDate, DateTime endDate)
    {
        var finishedTransactionStatuses = new[]
            { TransactionStatus.Cleared, TransactionStatus.Error, TransactionStatus.Canceled, TransactionStatus.Recalled, TransactionStatus.Aborted };

        var reportTransactionTypes = new[] { PaymentTransactionType.AchPull, PaymentTransactionType.AchPush, PaymentTransactionType.InstantPush, PaymentTransactionType.WirePush };

        var finishedTransactions = context.Set<PaymentTransactionEntity>()
            .AsNoTracking()
            .Include(pt => pt.PaymentRequest)
            .ThenInclude(r => r.PaymentRequestPayables)
            .Where(pt => reportTransactionTypes.Contains(pt.TransactionType)
                         && pt.UpdatedAt >= startDate
                         && pt.UpdatedAt <= endDate
                         && finishedTransactionStatuses.Contains(pt.Status)
                         && pt.PaymentRequest.RequestType == PaymentRequestType.InvoicePayment);

        var unfinishedTransactions = context.Set<PaymentTransactionEntity>()
            .AsNoTracking()
            .Include(pt => pt.PaymentRequest)
            .ThenInclude(r => r.PaymentRequestPayables)
            .Where(pt => reportTransactionTypes.Contains(pt.TransactionType)
                         && pt.CreatedAt <= endDate
                         && !finishedTransactionStatuses.Contains(pt.Status)
                         && pt.PaymentRequest.RequestType == PaymentRequestType.InvoicePayment);

        var result = await finishedTransactions
            .Union(unfinishedTransactions)
            .OrderByDescending(pt => pt.CreatedAt)
            .ToListAsync();

        return result;
    }

    public Task<List<PaymentRequestEntity>> GetPaymentsByPayableIds(IEnumerable<string> payableIds, CancellationToken ct)
    {
        return context.Set<PaymentRequestEntity>()
            .AsNoTracking()
            .Include(x => x.PaymentRequestDetails)
            .Where(x => x.PaymentRequestPayables.Any(p => payableIds.Contains(p.PayableId)))
            .ToListAsync(ct);
    }

    public Task<List<PaymentRequestEntity>> GetPaymentsByIds(IEnumerable<Guid> ids, CancellationToken ct)
    {
        return context.Set<PaymentRequestEntity>()
            .AsNoTracking()
            .Include(x => x.Transactions)
            .Include(x => x.PaymentRequestDetails)
            .Where(x => ids.Contains(x.Id))
            .ToListAsync(ct);
    }

    public Task<List<PaymentRequestEntity>> GetPaymentsByDrawId(Guid drawId, IEnumerable<PaymentRequestType>? types, CancellationToken ct)
    {
        var query = context.Set<PaymentRequestEntity>()
            .Include(x => x.PaymentRequestPayables)
            .Include(x => x.PaymentRequestFees)
            .Include(x => x.Transactions)
            .Include(x => x.PaymentRequestCommands)
            .Include(x => x.PaymentRequestDetails)
            .Where(x => x.PaymentRequestDetails != null && x.PaymentRequestDetails.DrawId == drawId);

        var paymentRequestTypes = types?.ToList();
        if (paymentRequestTypes != null && paymentRequestTypes.Any())
            query = query.Where(x => paymentRequestTypes.Contains(x.RequestType));

        return query.ToListAsync(ct);
    }

    private static IQueryable<TEntity> EnrichQueryWithIncludeProperties<TEntity>(IQueryable<TEntity> query, string includeProperties)
        where TEntity : EntityWithId
    {
        foreach (var includeProperty in includeProperties.Split
                     (IncludeSeparators, StringSplitOptions.RemoveEmptyEntries))
        {
            query = query.Include(includeProperty);
        }

        return query;
    }

    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;

        if (!_disposed && disposing)
        {
            context.Dispose();
        }
        _disposed = true;
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}
