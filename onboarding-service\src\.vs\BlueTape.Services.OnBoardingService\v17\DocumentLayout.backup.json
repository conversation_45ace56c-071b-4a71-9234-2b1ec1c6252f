{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}|BlueTape.Services.OnBoardingService.Domain\\BlueTape.Services.OnBoardingService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.domain\\extensions\\stringextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}|BlueTape.Services.OnBoardingService.Domain\\BlueTape.Services.OnBoardingService.Domain.csproj|solutionrelative:bluetape.services.onboardingservice.domain\\extensions\\stringextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6B6F19A4-05D8-4BB0-B75C-7144E056D087}|Functions\\BlueTape.Functions.OnBoardingJob\\BlueTape.Functions.OnBoardingJob.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\functions\\bluetape.functions.onboardingjob\\loanapplicationsyncconsumer.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6B6F19A4-05D8-4BB0-B75C-7144E056D087}|Functions\\BlueTape.Functions.OnBoardingJob\\BlueTape.Functions.OnBoardingJob.csproj|solutionrelative:functions\\bluetape.functions.onboardingjob\\loanapplicationsyncconsumer.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.compatibility\\services\\notifications\\notificationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|solutionrelative:bluetape.services.onboardingservice.compatibility\\services\\notifications\\notificationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.compatibility\\services\\compatibilityservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|solutionrelative:bluetape.services.onboardingservice.compatibility\\services\\compatibilityservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.compatibility\\services\\outputsmanagerservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|solutionrelative:bluetape.services.onboardingservice.compatibility\\services\\outputsmanagerservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.compatibility\\services\\notifications\\connectornotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|solutionrelative:bluetape.services.onboardingservice.compatibility\\services\\notifications\\connectornotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\services\\creditapplicationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\services\\creditapplicationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\services\\accountauthorizationdetailschangesservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\services\\accountauthorizationdetailschangesservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\services\\companyservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\services\\companyservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\services\\creditapplicationauthorizationdetailsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\services\\creditapplicationauthorizationdetailsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\services\\customeraccountsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\services\\customeraccountsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\services\\decisionengineexecutionservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\services\\decisionengineexecutionservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\services\\decisionenginestepsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\services\\decisionenginestepsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\services\\drawapprovalsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\services\\drawapprovalsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\services\\linqpalinteractionservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\services\\linqpalinteractionservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\services\\loanservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\services\\loanservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\services\\userservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\services\\userservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\services\\drawapprovalnotesservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\services\\drawapprovalnotesservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\services\\creditapplicationsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\services\\creditapplicationsyncservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\services\\creditapplicationexecutionservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\services\\creditapplicationexecutionservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\draftrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\draftrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\drawapprovalrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\drawapprovalrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\genericrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\genericrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\loanapplicationrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\loanapplicationrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\loanpaymentplanrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\loanpaymentplanrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\parseddraftrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\parseddraftrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\settingsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\settingsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\loanpricingpackagerepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\loanpricingpackagerepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\invoicerepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\invoicerepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\drawapprovalnotesrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\drawapprovalnotesrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\decisionenginestepsbviresultsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\decisionenginestepsbviresultsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\customeraccountrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\customeraccountrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\creditapplicationnotesrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\creditapplicationnotesrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\creditapplicationauthorizationdetailsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\creditapplicationauthorizationdetailsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\cardpricingpackagerepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\cardpricingpackagerepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\authorizationdetailsrefreshconfigurationrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\authorizationdetailsrefreshconfigurationrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\companyrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\companyrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\decisionenginestepsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\decisionenginestepsrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\repositories\\userrolerepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\repositories\\userrolerepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{04C8B807-B9A8-49C3-BD49-C9269EF4635E}|Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\BlueTape.Services.OnBoardingService.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\tests\\bluetape.services.onboardingservice.application.tests\\services\\creditapplicationsyncservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{04C8B807-B9A8-49C3-BD49-C9269EF4635E}|Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\BlueTape.Services.OnBoardingService.Application.Tests.csproj|solutionrelative:tests\\bluetape.services.onboardingservice.application.tests\\services\\creditapplicationsyncservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\externalservices\\nodeexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\externalservices\\nodeexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.api\\controllers\\creditapplicationauthorizationdetailscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|solutionrelative:bluetape.services.onboardingservice.api\\controllers\\creditapplicationauthorizationdetailscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\services\\accountauthorizationsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\services\\accountauthorizationsservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|solutionrelative:bluetape.services.onboardingservice.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|solutionrelative:bluetape.services.onboardingservice.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.api\\constants\\controllersconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|solutionrelative:bluetape.services.onboardingservice.api\\constants\\controllersconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.api\\controllers\\creditapplicationscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|solutionrelative:bluetape.services.onboardingservice.api\\controllers\\creditapplicationscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\pipelines\\invoicepipelines.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\pipelines\\invoicepipelines.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\SourceServer\\7e0cfd4be61758ac1a4c1eced071a862f07b45de0ab38ab5110f4815e54982d6\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DrawApprovalRepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{04C8B807-B9A8-49C3-BD49-C9269EF4635E}|Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\BlueTape.Services.OnBoardingService.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\tests\\bluetape.services.onboardingservice.application.tests\\services\\companyservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{04C8B807-B9A8-49C3-BD49-C9269EF4635E}|Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\BlueTape.Services.OnBoardingService.Application.Tests.csproj|solutionrelative:tests\\bluetape.services.onboardingservice.application.tests\\services\\companyservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.compatibility\\bluetape.services.onboardingservice.compatibility.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|solutionrelative:bluetape.services.onboardingservice.compatibility\\bluetape.services.onboardingservice.compatibility.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.compatibility\\di\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|solutionrelative:bluetape.services.onboardingservice.compatibility\\di\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.api\\controllers\\admincontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|solutionrelative:bluetape.services.onboardingservice.api\\controllers\\admincontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.api\\controllers\\drawapprovalcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|solutionrelative:bluetape.services.onboardingservice.api\\controllers\\drawapprovalcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}|BlueTape.Services.OnBoardingService.Domain\\BlueTape.Services.OnBoardingService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.domain\\documents\\drawapproval\\drawapprovaldocument.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}|BlueTape.Services.OnBoardingService.Domain\\BlueTape.Services.OnBoardingService.Domain.csproj|solutionrelative:bluetape.services.onboardingservice.domain\\documents\\drawapproval\\drawapprovaldocument.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{94CC420E-5E78-4736-9EBE-3AE0C654FB16}|BlueTape.Services.OnBoardingService.RefreshDetectorService\\BlueTape.Services.OnBoardingService.RefreshDetectorService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.refreshdetectorservice\\authorizationdetailsrefreshservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{94CC420E-5E78-4736-9EBE-3AE0C654FB16}|BlueTape.Services.OnBoardingService.RefreshDetectorService\\BlueTape.Services.OnBoardingService.RefreshDetectorService.csproj|solutionrelative:bluetape.services.onboardingservice.refreshdetectorservice\\authorizationdetailsrefreshservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{D6DC8ED4-A89B-4091-86A3-6BADE959A547}|Functions\\BlueTape.Functions.AuthorizationDetailsRefreshService\\BlueTape.Functions.AuthorizationDetailsRefreshService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\functions\\bluetape.functions.authorizationdetailsrefreshservice\\authorizationdetailsrefreshservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{D6DC8ED4-A89B-4091-86A3-6BADE959A547}|Functions\\BlueTape.Functions.AuthorizationDetailsRefreshService\\BlueTape.Functions.AuthorizationDetailsRefreshService.csproj|solutionrelative:functions\\bluetape.functions.authorizationdetailsrefreshservice\\authorizationdetailsrefreshservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\extensions\\drawapprovalextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\extensions\\drawapprovalextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{26A17375-3FEC-42BA-BB08-4FF2EB47A90C}|BlueTape.Services.OnBoardingService.DataAccess.LMS\\BlueTape.Services.OnBoardingService.DataAccess.LMS.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess.lms\\externalservices\\loanexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{26A17375-3FEC-42BA-BB08-4FF2EB47A90C}|BlueTape.Services.OnBoardingService.DataAccess.LMS\\BlueTape.Services.OnBoardingService.DataAccess.LMS.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess.lms\\externalservices\\loanexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\abstractions\\idrawapprovalservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\abstractions\\idrawapprovalservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}|BlueTape.Services.OnBoardingService.Domain\\BlueTape.Services.OnBoardingService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.domain\\documents\\drawapproval\\automatedapprovaldetailsdocument.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}|BlueTape.Services.OnBoardingService.Domain\\BlueTape.Services.OnBoardingService.Domain.csproj|solutionrelative:bluetape.services.onboardingservice.domain\\documents\\drawapproval\\automatedapprovaldetailsdocument.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{26A17375-3FEC-42BA-BB08-4FF2EB47A90C}|BlueTape.Services.OnBoardingService.DataAccess.LMS\\BlueTape.Services.OnBoardingService.DataAccess.LMS.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess.lms\\httpclients\\loanservicehttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{26A17375-3FEC-42BA-BB08-4FF2EB47A90C}|BlueTape.Services.OnBoardingService.DataAccess.LMS\\BlueTape.Services.OnBoardingService.DataAccess.LMS.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess.lms\\httpclients\\loanservicehttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.api\\bluetape.services.onboardingservice.api.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|", "RelativeMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|solutionrelative:bluetape.services.onboardingservice.api\\bluetape.services.onboardingservice.api.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{28148A78-1BF4-4390-AF25-586C66EC4D97}|BlueTape.Services.OnBoardingService.DataAccess.CompanyService\\BlueTape.Services.OnBoardingService.DataAccess.CompanyService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess.companyservice\\services\\companyexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{28148A78-1BF4-4390-AF25-586C66EC4D97}|BlueTape.Services.OnBoardingService.DataAccess.CompanyService\\BlueTape.Services.OnBoardingService.DataAccess.CompanyService.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess.companyservice\\services\\companyexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{071D5773-F59C-4C47-897E-624FCD8850B1}|BlueTape.Services.OnBoardingService.DataAccess.InvoiceService\\BlueTape.Services.OnBoardingService.DataAccess.InvoiceService.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess.invoiceservice\\services\\invoiceexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{071D5773-F59C-4C47-897E-624FCD8850B1}|BlueTape.Services.OnBoardingService.DataAccess.InvoiceService\\BlueTape.Services.OnBoardingService.DataAccess.InvoiceService.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess.invoiceservice\\services\\invoiceexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}|BlueTape.Services.OnBoardingService.Domain\\BlueTape.Services.OnBoardingService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.domain\\enums\\creditapplicationnotestemplatetype.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}|BlueTape.Services.OnBoardingService.Domain\\BlueTape.Services.OnBoardingService.Domain.csproj|solutionrelative:bluetape.services.onboardingservice.domain\\enums\\creditapplicationnotestemplatetype.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}|BlueTape.Services.OnBoardingService.Domain\\BlueTape.Services.OnBoardingService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.domain\\documents\\creditapplication\\creditapplicationdocument.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}|BlueTape.Services.OnBoardingService.Domain\\BlueTape.Services.OnBoardingService.Domain.csproj|solutionrelative:bluetape.services.onboardingservice.domain\\documents\\creditapplication\\creditapplicationdocument.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{04C8B807-B9A8-49C3-BD49-C9269EF4635E}|Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\BlueTape.Services.OnBoardingService.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\tests\\bluetape.services.onboardingservice.application.tests\\services\\drawapprovalservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{04C8B807-B9A8-49C3-BD49-C9269EF4635E}|Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\BlueTape.Services.OnBoardingService.Application.Tests.csproj|solutionrelative:tests\\bluetape.services.onboardingservice.application.tests\\services\\drawapprovalservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}|BlueTape.Services.OnBoardingService.Domain\\BlueTape.Services.OnBoardingService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.domain\\documents\\paymentplan\\loanpaymentplandocument.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}|BlueTape.Services.OnBoardingService.Domain\\BlueTape.Services.OnBoardingService.Domain.csproj|solutionrelative:bluetape.services.onboardingservice.domain\\documents\\paymentplan\\loanpaymentplandocument.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\extensions\\creditapplicationextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\extensions\\creditapplicationextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.compatibility\\abstractions\\icompatibilityservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|solutionrelative:bluetape.services.onboardingservice.compatibility\\abstractions\\icompatibilityservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\7928d95f642bfd07a414d3705dad0e860c063886fac53ab04a605e6dd10742a3\\Socket.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\4dcf567db33b8407e8e7e85491df71d46c06985d33f5ad8d904e52c3b325c6c5\\ExecutionContext.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\0ee7e684f8df99cf994c4186c4d28e639e235e4c6a61fff017a69e149cb42616\\HttpClient.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\f2256876427a059b9953c63da8b5ed989c4c449b3c6605ccdb8777c9e3a4ab0f\\ValueTask.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\436f8735f9701c121e542e705b8002c28cf053641f7e971e41e47cab32739fae\\DiagnosticsHandler.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\0e599f47268ea62b9af937dc43e1103c8accf8988ef11d1cec2bae5c647caaf7\\TaskAwaiter.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\943866804c986b3f1c185b0aaf5a6b2eb778d67c9141c5459bf5f86981d49a11\\AsyncTaskMethodBuilder.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\ec37f338c900506dbe108db913b9a235fc03f03f698e7fbf02e4594760a0b8fd\\SslStream.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\cf1f6f2a1a3d758edc99396db98dc7bfbaa5e37960fc94994c9a5df62250d808\\SocketAsyncEventArgs.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\di\\applicationdependenciesextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\di\\applicationdependenciesextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.api\\appsettings.dev.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|solutionrelative:bluetape.services.onboardingservice.api\\appsettings.dev.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.api\\appsettings.beta.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|solutionrelative:bluetape.services.onboardingservice.api\\appsettings.beta.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|solutionrelative:bluetape.services.onboardingservice.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{F0478359-F8EE-4D8B-BE45-D70C72FA5727}|BlueTape.Functions.Hosting\\BlueTape.Functions.Hosting.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.functions.hosting\\extensions\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F0478359-F8EE-4D8B-BE45-D70C72FA5727}|BlueTape.Functions.Hosting\\BlueTape.Functions.Hosting.csproj|solutionrelative:bluetape.functions.hosting\\extensions\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6B6F19A4-05D8-4BB0-B75C-7144E056D087}|Functions\\BlueTape.Functions.OnBoardingJob\\BlueTape.Functions.OnBoardingJob.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\functions\\bluetape.functions.onboardingjob\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6B6F19A4-05D8-4BB0-B75C-7144E056D087}|Functions\\BlueTape.Functions.OnBoardingJob\\BlueTape.Functions.OnBoardingJob.csproj|solutionrelative:functions\\bluetape.functions.onboardingjob\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{E22B6574-4CA6-424B-8E3E-81DFF2EB0AB7}|Functions\\BlueTape.Functions.AuthorizationDetailsRefreshDetector\\BlueTape.Functions.AuthorizationDetailsRefreshDetector.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\functions\\bluetape.functions.authorizationdetailsrefreshdetector\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E22B6574-4CA6-424B-8E3E-81DFF2EB0AB7}|Functions\\BlueTape.Functions.AuthorizationDetailsRefreshDetector\\BlueTape.Functions.AuthorizationDetailsRefreshDetector.csproj|solutionrelative:functions\\bluetape.functions.authorizationdetailsrefreshdetector\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.compatibility\\constants\\lexisnexisconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|solutionrelative:bluetape.services.onboardingservice.compatibility\\constants\\lexisnexisconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.compatibility\\constants\\compatibilityconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|solutionrelative:bluetape.services.onboardingservice.compatibility\\constants\\compatibilityconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.api\\appsettings.prod.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|solutionrelative:bluetape.services.onboardingservice.api\\appsettings.prod.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.api\\appsettings.qa.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|solutionrelative:bluetape.services.onboardingservice.api\\appsettings.qa.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{6B6F19A4-05D8-4BB0-B75C-7144E056D087}|Functions\\BlueTape.Functions.OnBoardingJob\\BlueTape.Functions.OnBoardingJob.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\functions\\bluetape.functions.onboardingjob\\host.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{6B6F19A4-05D8-4BB0-B75C-7144E056D087}|Functions\\BlueTape.Functions.OnBoardingJob\\BlueTape.Functions.OnBoardingJob.csproj|solutionrelative:functions\\bluetape.functions.onboardingjob\\host.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\bluetape.services.onboardingservice.application.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\bluetape.services.onboardingservice.application.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{04C8B807-B9A8-49C3-BD49-C9269EF4635E}|Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\BlueTape.Services.OnBoardingService.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\tests\\bluetape.services.onboardingservice.application.tests\\services\\linqpalinteractionservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{04C8B807-B9A8-49C3-BD49-C9269EF4635E}|Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\BlueTape.Services.OnBoardingService.Application.Tests.csproj|solutionrelative:tests\\bluetape.services.onboardingservice.application.tests\\services\\linqpalinteractionservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.compatibility\\services\\process\\plaiddataservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B6BC0E3B-AEEF-4CDA-81E3-827C27D09AD2}|BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj|solutionrelative:bluetape.services.onboardingservice.compatibility\\services\\process\\plaiddataservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.api\\controllers\\drawapprovalnotescontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{62EB19F8-8DD7-457A-9194-4647D5EF0ACD}|BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj|solutionrelative:bluetape.services.onboardingservice.api\\controllers\\drawapprovalnotescontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\constants\\nodeserviceconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\constants\\nodeserviceconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\abstractions\\ilinqpalinteractionservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\abstractions\\ilinqpalinteractionservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\abstractions\\externalservices\\inodeexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\abstractions\\externalservices\\inodeexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\abstractions\\iloanapplicationrepository.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\abstractions\\iloanapplicationrepository.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.dataaccess\\abstractions\\iuserrolerepository.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{FFB1D083-24EB-4777-ABDB-103935B9E2AE}|BlueTape.Services.OnBoardingService.DataAccess\\BlueTape.Services.OnBoardingService.DataAccess.csproj|solutionrelative:bluetape.services.onboardingservice.dataaccess\\abstractions\\iuserrolerepository.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}|BlueTape.Services.OnBoardingService.Domain\\BlueTape.Services.OnBoardingService.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.domain\\documents\\user\\userroledocument.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{ADED75CF-BEA6-4A8C-A434-73DAB14641EC}|BlueTape.Services.OnBoardingService.Domain\\BlueTape.Services.OnBoardingService.Domain.csproj|solutionrelative:bluetape.services.onboardingservice.domain\\documents\\user\\userroledocument.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\onboarding-service\\src\\bluetape.services.onboardingservice.application\\abstractions\\icompanyservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{BB85DCF7-9194-4DBF-AA33-0EBFFC2E8706}|BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj|solutionrelative:bluetape.services.onboardingservice.application\\abstractions\\icompanyservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "StringExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Domain\\Extensions\\StringExtensions.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Domain\\Extensions\\StringExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Domain\\Extensions\\StringExtensions.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Domain\\Extensions\\StringExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T16:12:41.434Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "AccountAuthorizationDetailsChangesService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\AccountAuthorizationDetailsChangesService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Services\\AccountAuthorizationDetailsChangesService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\AccountAuthorizationDetailsChangesService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Services\\AccountAuthorizationDetailsChangesService.cs", "ViewState": "AgIAACIAAAAAAAAAAAAAADkAAABDAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T07:56:06.523Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "CreditApplicationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\CreditApplicationService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Services\\CreditApplicationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\CreditApplicationService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Services\\CreditApplicationService.cs", "ViewState": "AgIAAC0AAAAAAAAAAAAkwMEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T12:18:06.786Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ConnectorNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\Services\\Notifications\\ConnectorNotificationService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Compatibility\\Services\\Notifications\\ConnectorNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\Services\\Notifications\\ConnectorNotificationService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Compatibility\\Services\\Notifications\\ConnectorNotificationService.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T09:49:49.309Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "OutputsManagerService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\Services\\OutputsManagerService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Compatibility\\Services\\OutputsManagerService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\Services\\OutputsManagerService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Compatibility\\Services\\OutputsManagerService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-28T07:23:49.602Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "CompatibilityService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\Services\\CompatibilityService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Compatibility\\Services\\CompatibilityService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\Services\\CompatibilityService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Compatibility\\Services\\CompatibilityService.cs", "ViewState": "AgIAAIoAAAAAAAAAAAArwKAAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-12T18:38:24.683Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "NotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\Services\\Notifications\\NotificationService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Compatibility\\Services\\Notifications\\NotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\Services\\Notifications\\NotificationService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Compatibility\\Services\\Notifications\\NotificationService.cs", "ViewState": "AgIAAEwAAAAAAAAAAAAswI8BAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T10:13:34.841Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "LoanApplicationSyncConsumer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\Functions\\BlueTape.Functions.OnBoardingJob\\LoanApplicationSyncConsumer.cs", "RelativeDocumentMoniker": "Functions\\BlueTape.Functions.OnBoardingJob\\LoanApplicationSyncConsumer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\Functions\\BlueTape.Functions.OnBoardingJob\\LoanApplicationSyncConsumer.cs", "RelativeToolTip": "Functions\\BlueTape.Functions.OnBoardingJob\\LoanApplicationSyncConsumer.cs", "ViewState": "AgIAABcAAAAAAAAAAAAQwCsAAABDAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T13:49:44.126Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "CustomerAccountsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\CustomerAccountsService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Services\\CustomerAccountsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\CustomerAccountsService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Services\\CustomerAccountsService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T14:21:20.31Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "DecisionEngineExecutionService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\DecisionEngineExecutionService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Services\\DecisionEngineExecutionService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\DecisionEngineExecutionService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Services\\DecisionEngineExecutionService.cs", "ViewState": "AgIAABMAAAAAAAAAAAApwCsAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T13:47:48.512Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "DecisionEngineStepsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\DecisionEngineStepsService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Services\\DecisionEngineStepsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\DecisionEngineStepsService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Services\\DecisionEngineStepsService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T11:18:05.5Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "DrawApprovalsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\DrawApprovalsService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Services\\DrawApprovalsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\DrawApprovalsService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Services\\DrawApprovalsService.cs", "ViewState": "AgIAAC4AAAAAAAAAAAAmwEYAAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T12:00:55.788Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "CreditApplicationAuthorizationDetailsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\CreditApplicationAuthorizationDetailsService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Services\\CreditApplicationAuthorizationDetailsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\CreditApplicationAuthorizationDetailsService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Services\\CreditApplicationAuthorizationDetailsService.cs", "ViewState": "AgIAACEAAAAAAAAAAAAowDUAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T15:55:27.562Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "CompanyService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\CompanyService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Services\\CompanyService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\CompanyService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Services\\CompanyService.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAQwBEAAABNAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T09:32:08.855Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "LinqPalInteractionService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\LinqPalInteractionService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Services\\LinqPalInteractionService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\LinqPalInteractionService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Services\\LinqPalInteractionService.cs", "ViewState": "AgIAAEoAAAAAAAAAAAAIwGgAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T08:50:39.665Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "LoanService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\LoanService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Services\\LoanService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\LoanService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Services\\LoanService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-27T20:30:41.297Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "UserService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\UserService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Services\\UserService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\UserService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Services\\UserService.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAjwAoAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T09:31:08.78Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "DrawApprovalNotesService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\DrawApprovalNotesService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Services\\DrawApprovalNotesService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\DrawApprovalNotesService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Services\\DrawApprovalNotesService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T13:56:09.552Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "CreditApplicationSyncService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\CreditApplicationSyncService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Services\\CreditApplicationSyncService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\CreditApplicationSyncService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Services\\CreditApplicationSyncService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T14:20:38.504Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "CreditApplicationExecutionService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\CreditApplicationExecutionService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Services\\CreditApplicationExecutionService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\CreditApplicationExecutionService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Services\\CreditApplicationExecutionService.cs", "ViewState": "AgIAAJAAAAAAAAAAAAAowKAAAABCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T13:48:52.654Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "DraftRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DraftRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DraftRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DraftRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DraftRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-28T06:58:44.56Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "DrawApprovalRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DrawApprovalRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DrawApprovalRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DrawApprovalRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DrawApprovalRepository.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAAACYAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T16:30:28.519Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "GenericRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\GenericRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\GenericRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\GenericRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\GenericRepository.cs", "ViewState": "AgIAADIAAAAAAAAAAAAIwEgAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-28T07:08:13.459Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "LoanApplicationRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\LoanApplicationRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\LoanApplicationRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\LoanApplicationRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\LoanApplicationRepository.cs", "ViewState": "AgIAACYAAAAAAAAAAAAwwEEAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T15:58:10.582Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "LoanPaymentPlanRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\LoanPaymentPlanRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\LoanPaymentPlanRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\LoanPaymentPlanRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\LoanPaymentPlanRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T07:51:32.912Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 25, "Title": "ParsedDraftRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\ParsedDraftRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\ParsedDraftRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\ParsedDraftRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\ParsedDraftRepository.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAowAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T07:52:28.89Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 26, "Title": "SettingsRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\SettingsRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\SettingsRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\SettingsRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\SettingsRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T07:52:29.567Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "LoanPricingPackageRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\LoanPricingPackageRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\LoanPricingPackageRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\LoanPricingPackageRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\LoanPricingPackageRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T07:52:50.944Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 28, "Title": "InvoiceRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\InvoiceRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\InvoiceRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\InvoiceRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\InvoiceRepository.cs", "ViewState": "AgIAAAQAAAAAAAAAAAAuwBEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-12T09:47:44.802Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 29, "Title": "DrawApprovalNotesRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DrawApprovalNotesRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DrawApprovalNotesRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DrawApprovalNotesRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DrawApprovalNotesRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T07:51:33.597Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 30, "Title": "DecisionEngineStepsBVIResultsRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DecisionEngineStepsBVIResultsRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DecisionEngineStepsBVIResultsRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DecisionEngineStepsBVIResultsRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DecisionEngineStepsBVIResultsRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T07:53:20.471Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 31, "Title": "CustomerAccountRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CustomerAccountRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CustomerAccountRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CustomerAccountRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CustomerAccountRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T07:53:11.231Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 32, "Title": "CreditApplicationNotesRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CreditApplicationNotesRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CreditApplicationNotesRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CreditApplicationNotesRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CreditApplicationNotesRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T07:53:14.012Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 34, "Title": "CardPricingPackageRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CardPricingPackageRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CardPricingPackageRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CardPricingPackageRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CardPricingPackageRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T07:53:15.824Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 35, "Title": "AuthorizationDetailsRefreshConfigurationRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\AuthorizationDetailsRefreshConfigurationRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\AuthorizationDetailsRefreshConfigurationRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\AuthorizationDetailsRefreshConfigurationRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\AuthorizationDetailsRefreshConfigurationRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T07:53:16.352Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 37, "Title": "DecisionEngineStepsRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DecisionEngineStepsRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DecisionEngineStepsRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DecisionEngineStepsRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DecisionEngineStepsRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T07:53:05.566Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 33, "Title": "CreditApplicationAuthorizationDetailsRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CreditApplicationAuthorizationDetailsRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CreditApplicationAuthorizationDetailsRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CreditApplicationAuthorizationDetailsRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CreditApplicationAuthorizationDetailsRepository.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAwwCIAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T15:55:32.853Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 36, "Title": "CompanyRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CompanyRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CompanyRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CompanyRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\CompanyRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T10:31:53.453Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 38, "Title": "UserRoleRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\UserRoleRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\UserRoleRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\UserRoleRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\UserRoleRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T07:52:30.477Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 39, "Title": "CreditApplicationSyncServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\Services\\CreditApplicationSyncServiceTests.cs", "RelativeDocumentMoniker": "Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\Services\\CreditApplicationSyncServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\Services\\CreditApplicationSyncServiceTests.cs", "RelativeToolTip": "Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\Services\\CreditApplicationSyncServiceTests.cs", "ViewState": "AgIAABQAAAAAAAAAAAAgwBkAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T14:24:09.568Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 48, "Title": "DrawApprovalRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\SourceServer\\7e0cfd4be61758ac1a4c1eced071a862f07b45de0ab38ab5110f4815e54982d6\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DrawApprovalRepository.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\SourceServer\\7e0cfd4be61758ac1a4c1eced071a862f07b45de0ab38ab5110f4815e54982d6\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DrawApprovalRepository.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\SourceServer\\7e0cfd4be61758ac1a4c1eced071a862f07b45de0ab38ab5110f4815e54982d6\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DrawApprovalRepository.cs [Read Only]", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\SourceServer\\7e0cfd4be61758ac1a4c1eced071a862f07b45de0ab38ab5110f4815e54982d6\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Repositories\\DrawApprovalRepository.cs [Read Only]", "ViewState": "AgIAAOUAAAAAAAAAAAAAAB4BAAABAAAAAQAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T16:44:49.554Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 40, "Title": "NodeExternalService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\ExternalServices\\NodeExternalService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\ExternalServices\\NodeExternalService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\ExternalServices\\NodeExternalService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\ExternalServices\\NodeExternalService.cs", "ViewState": "AgIAAFQAAAAAAAAAAAAwwGEAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T12:20:00.264Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 42, "Title": "AccountAuthorizationsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Source\\Repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\AccountAuthorizationsService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Services\\AccountAuthorizationsService.cs", "ToolTip": "C:\\Users\\<USER>\\Source\\Repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Services\\AccountAuthorizationsService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Services\\AccountAuthorizationsService.cs", "ViewState": "AgIAAFYBAAAAAAAAAAAAAFYBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T11:18:47.883Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 41, "Title": "CreditApplicationAuthorizationDetailsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\Controllers\\CreditApplicationAuthorizationDetailsController.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.API\\Controllers\\CreditApplicationAuthorizationDetailsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\Controllers\\CreditApplicationAuthorizationDetailsController.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.API\\Controllers\\CreditApplicationAuthorizationDetailsController.cs", "ViewState": "AgIAABwAAAAAAAAAAAAWwCwAAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T15:55:16.976Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 43, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\Program.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.API\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\Program.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.API\\Program.cs", "ViewState": "AgIAADAAAAAAAAAAAAAQwEoAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T14:57:17.285Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 44, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.API\\Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\Properties\\launchSettings.json", "RelativeToolTip": "BlueTape.Services.OnBoardingService.API\\Properties\\launchSettings.json", "ViewState": "AgIAACYAAAAAAAAAAAAUwD0AAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-25T14:52:57.743Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 45, "Title": "ControllersConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\Constants\\ControllersConstants.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.API\\Constants\\ControllersConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\Constants\\ControllersConstants.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.API\\Constants\\ControllersConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T15:55:10.4Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "InvoicePipelines.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Pipelines\\InvoicePipelines.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Pipelines\\InvoicePipelines.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Pipelines\\InvoicePipelines.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Pipelines\\InvoicePipelines.cs", "ViewState": "AgIAABoAAAAAAAAAAAD4vzUAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T16:49:32.504Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "CreditApplicationsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\Controllers\\CreditApplicationsController.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.API\\Controllers\\CreditApplicationsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\Controllers\\CreditApplicationsController.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.API\\Controllers\\CreditApplicationsController.cs", "ViewState": "AgIAAMkAAAAAAAAAAAAAAOIAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T13:39:47.206Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 49, "Title": "CompanyServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\Services\\CompanyServiceTests.cs", "RelativeDocumentMoniker": "Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\Services\\CompanyServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\Services\\CompanyServiceTests.cs", "RelativeToolTip": "Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\Services\\CompanyServiceTests.cs", "ViewState": "AgIAABMAAAAAAAAAAAAEwCQAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T09:40:56.794Z"}, {"$type": "Document", "DocumentIndex": 100, "Title": "IUserRoleRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Abstractions\\IUserRoleRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Abstractions\\IUserRoleRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Abstractions\\IUserRoleRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Abstractions\\IUserRoleRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T09:33:49.624Z"}, {"$type": "Document", "DocumentIndex": 101, "Title": "UserRoleDocument.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Domain\\Documents\\User\\UserRoleDocument.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Domain\\Documents\\User\\UserRoleDocument.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Domain\\Documents\\User\\UserRoleDocument.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Domain\\Documents\\User\\UserRoleDocument.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T09:34:39.023Z"}, {"$type": "Document", "DocumentIndex": 102, "Title": "ICompanyService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Abstractions\\ICompanyService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Abstractions\\ICompanyService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Abstractions\\ICompanyService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Abstractions\\ICompanyService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T09:35:42.808Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "BlueTape.Services.OnBoardingService.Compatibility.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Compatibility\\BlueTape.Services.OnBoardingService.Compatibility.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-06T11:54:54.645Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "AdminController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\Controllers\\AdminController.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.API\\Controllers\\AdminController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\Controllers\\AdminController.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.API\\Controllers\\AdminController.cs", "ViewState": "AgIAAEQAAAAAAAAAAAAwwGIAAABTAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T12:17:30.675Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "DependencyInjection.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\DI\\DependencyInjection.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Compatibility\\DI\\DependencyInjection.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\DI\\DependencyInjection.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Compatibility\\DI\\DependencyInjection.cs", "ViewState": "AgIAACgAAAAAAAAAAAAgwD4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T11:55:56.709Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "AuthorizationDetailsRefreshService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.RefreshDetectorService\\AuthorizationDetailsRefreshService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.RefreshDetectorService\\AuthorizationDetailsRefreshService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.RefreshDetectorService\\AuthorizationDetailsRefreshService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.RefreshDetectorService\\AuthorizationDetailsRefreshService.cs", "ViewState": "AgIAAAYAAAAAAAAAAIBJwBYAAABIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T12:56:43.494Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "DrawApprovalDocument.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Domain\\Documents\\DrawApproval\\DrawApprovalDocument.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Domain\\Documents\\DrawApproval\\DrawApprovalDocument.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Domain\\Documents\\DrawApproval\\DrawApprovalDocument.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Domain\\Documents\\DrawApproval\\DrawApprovalDocument.cs", "ViewState": "AgIAAJsAAAAAAAAAAAAiwJwAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-28T06:59:43.773Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "DrawApprovalController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\Controllers\\DrawApprovalController.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.API\\Controllers\\DrawApprovalController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\Controllers\\DrawApprovalController.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.API\\Controllers\\DrawApprovalController.cs", "ViewState": "AgIAAH4AAAAAAAAAAAArwIwAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-01T06:48:13.634Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "AuthorizationDetailsRefreshService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\Functions\\BlueTape.Functions.AuthorizationDetailsRefreshService\\AuthorizationDetailsRefreshService.cs", "RelativeDocumentMoniker": "Functions\\BlueTape.Functions.AuthorizationDetailsRefreshService\\AuthorizationDetailsRefreshService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\Functions\\BlueTape.Functions.AuthorizationDetailsRefreshService\\AuthorizationDetailsRefreshService.cs", "RelativeToolTip": "Functions\\BlueTape.Functions.AuthorizationDetailsRefreshService\\AuthorizationDetailsRefreshService.cs", "ViewState": "AgIAABwAAAAAAAAAAAAgwDMAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T12:56:26.351Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "DrawApprovalExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Extensions\\DrawApprovalExtensions.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Extensions\\DrawApprovalExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Extensions\\DrawApprovalExtensions.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Extensions\\DrawApprovalExtensions.cs", "ViewState": "AgIAABsAAAAAAAAAAAAjwC4AAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T17:27:11.851Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "LoanExternalService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess.LMS\\ExternalServices\\LoanExternalService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess.LMS\\ExternalServices\\LoanExternalService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess.LMS\\ExternalServices\\LoanExternalService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess.LMS\\ExternalServices\\LoanExternalService.cs", "ViewState": "AgIAAH0AAAAAAAAAAAAjwJcAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-27T20:31:14.228Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "IDrawApprovalService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Abstractions\\IDrawApprovalService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Abstractions\\IDrawApprovalService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Abstractions\\IDrawApprovalService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Abstractions\\IDrawApprovalService.cs", "ViewState": "AgIAAB0AAAAAAAAAAIA7wCkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T14:22:05.501Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "AutomatedApprovalDetailsDocument.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Domain\\Documents\\DrawApproval\\AutomatedApprovalDetailsDocument.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Domain\\Documents\\DrawApproval\\AutomatedApprovalDetailsDocument.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Domain\\Documents\\DrawApproval\\AutomatedApprovalDetailsDocument.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Domain\\Documents\\DrawApproval\\AutomatedApprovalDetailsDocument.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-28T07:01:35.899Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "LoanServiceHttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess.LMS\\HttpClients\\LoanServiceHttpClient.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess.LMS\\HttpClients\\LoanServiceHttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess.LMS\\HttpClients\\LoanServiceHttpClient.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess.LMS\\HttpClients\\LoanServiceHttpClient.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-27T20:31:03.067Z"}, {"$type": "Document", "DocumentIndex": 64, "Title": "InvoiceExternalService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess.InvoiceService\\Services\\InvoiceExternalService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess.InvoiceService\\Services\\InvoiceExternalService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess.InvoiceService\\Services\\InvoiceExternalService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess.InvoiceService\\Services\\InvoiceExternalService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-12T09:49:37.574Z"}, {"$type": "Document", "DocumentIndex": 62, "Title": "BlueTape.Services.OnBoardingService.API", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj", "RelativeToolTip": "BlueTape.Services.OnBoardingService.API\\BlueTape.Services.OnBoardingService.API.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-25T15:08:42.887Z"}, {"$type": "Document", "DocumentIndex": 63, "Title": "CompanyExternalService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess.CompanyService\\Services\\CompanyExternalService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess.CompanyService\\Services\\CompanyExternalService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess.CompanyService\\Services\\CompanyExternalService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess.CompanyService\\Services\\CompanyExternalService.cs", "ViewState": "AgIAAAAAAAAAAAAAAIBJwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-12T09:50:17.525Z"}, {"$type": "Document", "DocumentIndex": 99, "Title": "ILoanApplicationRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Abstractions\\ILoanApplicationRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Abstractions\\ILoanApplicationRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Abstractions\\ILoanApplicationRepository.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Abstractions\\ILoanApplicationRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T16:04:11.556Z"}, {"$type": "Document", "DocumentIndex": 68, "Title": "LoanPaymentPlanDocument.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Domain\\Documents\\PaymentPlan\\LoanPaymentPlanDocument.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Domain\\Documents\\PaymentPlan\\LoanPaymentPlanDocument.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Domain\\Documents\\PaymentPlan\\LoanPaymentPlanDocument.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Domain\\Documents\\PaymentPlan\\LoanPaymentPlanDocument.cs", "ViewState": "AgIAAAAAAAAAAAAAAIBZwAgAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T16:34:20.888Z"}, {"$type": "Document", "DocumentIndex": 66, "Title": "CreditApplicationDocument.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Domain\\Documents\\CreditApplication\\CreditApplicationDocument.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Domain\\Documents\\CreditApplication\\CreditApplicationDocument.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Domain\\Documents\\CreditApplication\\CreditApplicationDocument.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Domain\\Documents\\CreditApplication\\CreditApplicationDocument.cs", "ViewState": "AgIAABUAAAAAAAAAAAAowCEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T17:26:35.657Z"}, {"$type": "Document", "DocumentIndex": 65, "Title": "CreditApplicationNotesTemplateType.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Domain\\Enums\\CreditApplicationNotesTemplateType.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Domain\\Enums\\CreditApplicationNotesTemplateType.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Domain\\Enums\\CreditApplicationNotesTemplateType.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Domain\\Enums\\CreditApplicationNotesTemplateType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T17:26:54.219Z"}, {"$type": "Document", "DocumentIndex": 69, "Title": "CreditApplicationExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Extensions\\CreditApplicationExtensions.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Extensions\\CreditApplicationExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Extensions\\CreditApplicationExtensions.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Extensions\\CreditApplicationExtensions.cs", "ViewState": "AgIAABIAAAAAAAAAAAAQwBgAAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T13:47:27.224Z"}, {"$type": "Document", "DocumentIndex": 67, "Title": "DrawApprovalServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\Services\\DrawApprovalServiceTests.cs", "RelativeDocumentMoniker": "Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\Services\\DrawApprovalServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\Services\\DrawApprovalServiceTests.cs", "RelativeToolTip": "Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\Services\\DrawApprovalServiceTests.cs", "ViewState": "AgIAACcAAAAAAAAAAAAcwDEAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-04T08:01:49.496Z"}, {"$type": "Document", "DocumentIndex": 70, "Title": "ICompatibilityService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\Abstractions\\ICompatibilityService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Compatibility\\Abstractions\\ICompatibilityService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\Abstractions\\ICompatibilityService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Compatibility\\Abstractions\\ICompatibilityService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAABgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T12:07:49.787Z"}, {"$type": "Document", "DocumentIndex": 72, "Title": "ExecutionContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\4dcf567db33b8407e8e7e85491df71d46c06985d33f5ad8d904e52c3b325c6c5\\ExecutionContext.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\4dcf567db33b8407e8e7e85491df71d46c06985d33f5ad8d904e52c3b325c6c5\\ExecutionContext.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\4dcf567db33b8407e8e7e85491df71d46c06985d33f5ad8d904e52c3b325c6c5\\ExecutionContext.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\4dcf567db33b8407e8e7e85491df71d46c06985d33f5ad8d904e52c3b325c6c5\\ExecutionContext.cs", "ViewState": "AgIAAHoAAAAAAAAAAAAewIkAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T11:52:18.959Z"}, {"$type": "Document", "DocumentIndex": 71, "Title": "Socket.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\7928d95f642bfd07a414d3705dad0e860c063886fac53ab04a605e6dd10742a3\\Socket.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\7928d95f642bfd07a414d3705dad0e860c063886fac53ab04a605e6dd10742a3\\Socket.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\7928d95f642bfd07a414d3705dad0e860c063886fac53ab04a605e6dd10742a3\\Socket.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\7928d95f642bfd07a414d3705dad0e860c063886fac53ab04a605e6dd10742a3\\Socket.cs", "ViewState": "AgIAAIQBAAAAAAAAAAAewJMBAAADAAAAAQAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T11:50:52.021Z"}, {"$type": "Document", "DocumentIndex": 78, "Title": "SslStream.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\ec37f338c900506dbe108db913b9a235fc03f03f698e7fbf02e4594760a0b8fd\\SslStream.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\ec37f338c900506dbe108db913b9a235fc03f03f698e7fbf02e4594760a0b8fd\\SslStream.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\ec37f338c900506dbe108db913b9a235fc03f03f698e7fbf02e4594760a0b8fd\\SslStream.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\ec37f338c900506dbe108db913b9a235fc03f03f698e7fbf02e4594760a0b8fd\\SslStream.cs", "ViewState": "AgIAAL8AAAAAAAAAAAAewNEAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T11:52:44.931Z"}, {"$type": "Document", "DocumentIndex": 76, "Title": "TaskAwaiter.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\0e599f47268ea62b9af937dc43e1103c8accf8988ef11d1cec2bae5c647caaf7\\TaskAwaiter.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\0e599f47268ea62b9af937dc43e1103c8accf8988ef11d1cec2bae5c647caaf7\\TaskAwaiter.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\0e599f47268ea62b9af937dc43e1103c8accf8988ef11d1cec2bae5c647caaf7\\TaskAwaiter.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\0e599f47268ea62b9af937dc43e1103c8accf8988ef11d1cec2bae5c647caaf7\\TaskAwaiter.cs", "ViewState": "AgIAAJAAAAAAAAAAAAAewJ8AAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T11:53:53.287Z"}, {"$type": "Document", "DocumentIndex": 74, "Title": "ValueTask.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\f2256876427a059b9953c63da8b5ed989c4c449b3c6605ccdb8777c9e3a4ab0f\\ValueTask.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\f2256876427a059b9953c63da8b5ed989c4c449b3c6605ccdb8777c9e3a4ab0f\\ValueTask.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\f2256876427a059b9953c63da8b5ed989c4c449b3c6605ccdb8777c9e3a4ab0f\\ValueTask.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\f2256876427a059b9953c63da8b5ed989c4c449b3c6605ccdb8777c9e3a4ab0f\\ValueTask.cs", "ViewState": "AgIAAMoBAAAAAAAAAAAewNkBAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T11:53:55.046Z"}, {"$type": "Document", "DocumentIndex": 77, "Title": "AsyncTaskMethodBuilder.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\943866804c986b3f1c185b0aaf5a6b2eb778d67c9141c5459bf5f86981d49a11\\AsyncTaskMethodBuilder.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\943866804c986b3f1c185b0aaf5a6b2eb778d67c9141c5459bf5f86981d49a11\\AsyncTaskMethodBuilder.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\943866804c986b3f1c185b0aaf5a6b2eb778d67c9141c5459bf5f86981d49a11\\AsyncTaskMethodBuilder.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\943866804c986b3f1c185b0aaf5a6b2eb778d67c9141c5459bf5f86981d49a11\\AsyncTaskMethodBuilder.cs", "ViewState": "AgIAAHMAAAAAAAAAAAAewIIAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T11:53:53.26Z"}, {"$type": "Document", "DocumentIndex": 75, "Title": "DiagnosticsHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\436f8735f9701c121e542e705b8002c28cf053641f7e971e41e47cab32739fae\\DiagnosticsHandler.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\436f8735f9701c121e542e705b8002c28cf053641f7e971e41e47cab32739fae\\DiagnosticsHandler.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\436f8735f9701c121e542e705b8002c28cf053641f7e971e41e47cab32739fae\\DiagnosticsHandler.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\436f8735f9701c121e542e705b8002c28cf053641f7e971e41e47cab32739fae\\DiagnosticsHandler.cs", "ViewState": "AgIAAOAAAAAAAAAAAAAewPYAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T11:54:02.672Z"}, {"$type": "Document", "DocumentIndex": 73, "Title": "HttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\0ee7e684f8df99cf994c4186c4d28e639e235e4c6a61fff017a69e149cb42616\\HttpClient.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\0ee7e684f8df99cf994c4186c4d28e639e235e4c6a61fff017a69e149cb42616\\HttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\0ee7e684f8df99cf994c4186c4d28e639e235e4c6a61fff017a69e149cb42616\\HttpClient.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\0ee7e684f8df99cf994c4186c4d28e639e235e4c6a61fff017a69e149cb42616\\HttpClient.cs", "ViewState": "AgIAAHgCAAAAAAAAAAAewIcCAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T11:54:06.713Z"}, {"$type": "Document", "DocumentIndex": 79, "Title": "SocketAsyncEventArgs.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\cf1f6f2a1a3d758edc99396db98dc7bfbaa5e37960fc94994c9a5df62250d808\\SocketAsyncEventArgs.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\cf1f6f2a1a3d758edc99396db98dc7bfbaa5e37960fc94994c9a5df62250d808\\SocketAsyncEventArgs.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\cf1f6f2a1a3d758edc99396db98dc7bfbaa5e37960fc94994c9a5df62250d808\\SocketAsyncEventArgs.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\cf1f6f2a1a3d758edc99396db98dc7bfbaa5e37960fc94994c9a5df62250d808\\SocketAsyncEventArgs.cs", "ViewState": "AgIAAJwAAAAAAAAAAAAewKsAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-08T11:51:00.974Z"}, {"$type": "Document", "DocumentIndex": 83, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\appsettings.json", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.API\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\appsettings.json", "RelativeToolTip": "BlueTape.Services.OnBoardingService.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAOBvwCUAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-06T13:49:58.138Z"}, {"$type": "Document", "DocumentIndex": 81, "Title": "appsettings.dev.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\appsettings.dev.json", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.API\\appsettings.dev.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\appsettings.dev.json", "RelativeToolTip": "BlueTape.Services.OnBoardingService.API\\appsettings.dev.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-06T13:50:13.141Z"}, {"$type": "Document", "DocumentIndex": 82, "Title": "appsettings.beta.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\appsettings.beta.json", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.API\\appsettings.beta.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\appsettings.beta.json", "RelativeToolTip": "BlueTape.Services.OnBoardingService.API\\appsettings.beta.json", "ViewState": "AgIAAAAAAAAAAAAAAIBZwCAAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-06T13:50:07.775Z"}, {"$type": "Document", "DocumentIndex": 86, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\Functions\\BlueTape.Functions.AuthorizationDetailsRefreshDetector\\Program.cs", "RelativeDocumentMoniker": "Functions\\BlueTape.Functions.AuthorizationDetailsRefreshDetector\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\Functions\\BlueTape.Functions.AuthorizationDetailsRefreshDetector\\Program.cs", "RelativeToolTip": "Functions\\BlueTape.Functions.AuthorizationDetailsRefreshDetector\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T15:00:03.95Z"}, {"$type": "Document", "DocumentIndex": 80, "Title": "ApplicationDependenciesExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\DI\\ApplicationDependenciesExtensions.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\DI\\ApplicationDependenciesExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\DI\\ApplicationDependenciesExtensions.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\DI\\ApplicationDependenciesExtensions.cs", "ViewState": "AgIAACUAAAAAAAAAAAAQwDQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T11:56:21.851Z"}, {"$type": "Document", "DocumentIndex": 85, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\Functions\\BlueTape.Functions.OnBoardingJob\\Program.cs", "RelativeDocumentMoniker": "Functions\\BlueTape.Functions.OnBoardingJob\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\Functions\\BlueTape.Functions.OnBoardingJob\\Program.cs", "RelativeToolTip": "Functions\\BlueTape.Functions.OnBoardingJob\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T15:00:05.897Z"}, {"$type": "Document", "DocumentIndex": 84, "Title": "DependencyInjection.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Functions.Hosting\\Extensions\\DependencyInjection.cs", "RelativeDocumentMoniker": "BlueTape.Functions.Hosting\\Extensions\\DependencyInjection.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Functions.Hosting\\Extensions\\DependencyInjection.cs", "RelativeToolTip": "BlueTape.Functions.Hosting\\Extensions\\DependencyInjection.cs", "ViewState": "AgIAACsAAAAAAAAAAAAmwDoAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T15:00:38.767Z"}, {"$type": "Document", "DocumentIndex": 88, "Title": "CompatibilityConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\Constants\\CompatibilityConstants.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Compatibility\\Constants\\CompatibilityConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\Constants\\CompatibilityConstants.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Compatibility\\Constants\\CompatibilityConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T14:20:24.419Z"}, {"$type": "Document", "DocumentIndex": 87, "Title": "LexisNexisConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\Constants\\LexisNexisConstants.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Compatibility\\Constants\\LexisNexisConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\Constants\\LexisNexisConstants.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Compatibility\\Constants\\LexisNexisConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-06T14:20:26.063Z"}, {"$type": "Document", "DocumentIndex": 91, "Title": "host.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\Functions\\BlueTape.Functions.OnBoardingJob\\host.json", "RelativeDocumentMoniker": "Functions\\BlueTape.Functions.OnBoardingJob\\host.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\Functions\\BlueTape.Functions.OnBoardingJob\\host.json", "RelativeToolTip": "Functions\\BlueTape.Functions.OnBoardingJob\\host.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-06T13:49:45.16Z"}, {"$type": "Document", "DocumentIndex": 89, "Title": "appsettings.prod.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\appsettings.prod.json", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.API\\appsettings.prod.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\appsettings.prod.json", "RelativeToolTip": "BlueTape.Services.OnBoardingService.API\\appsettings.prod.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-06T13:50:14.247Z"}, {"$type": "Document", "DocumentIndex": 90, "Title": "appsettings.qa.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\appsettings.qa.json", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.API\\appsettings.qa.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\appsettings.qa.json", "RelativeToolTip": "BlueTape.Services.OnBoardingService.API\\appsettings.qa.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-06T13:50:16.534Z"}, {"$type": "Document", "DocumentIndex": 92, "Title": "BlueTape.Services.OnBoardingService.Application.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\BlueTape.Services.OnBoardingService.Application.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAACQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-06T11:55:03.465Z"}, {"$type": "Document", "DocumentIndex": 94, "Title": "PlaidDataService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\Services\\Process\\PlaidDataService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Compatibility\\Services\\Process\\PlaidDataService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Compatibility\\Services\\Process\\PlaidDataService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Compatibility\\Services\\Process\\PlaidDataService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-04T09:27:27.055Z"}, {"$type": "Document", "DocumentIndex": 93, "Title": "LinqPalInteractionServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\Services\\LinqPalInteractionServiceTests.cs", "RelativeDocumentMoniker": "Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\Services\\LinqPalInteractionServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\Services\\LinqPalInteractionServiceTests.cs", "RelativeToolTip": "Tests\\BlueTape.Services.OnBoardingService.Application.Tests\\Services\\LinqPalInteractionServiceTests.cs", "ViewState": "AgIAAFMAAAAAAAAAAAAawGUAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T12:37:32.251Z"}, {"$type": "Document", "DocumentIndex": 95, "Title": "DrawApprovalNotesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\Controllers\\DrawApprovalNotesController.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.API\\Controllers\\DrawApprovalNotesController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.API\\Controllers\\DrawApprovalNotesController.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.API\\Controllers\\DrawApprovalNotesController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T11:34:18.57Z"}, {"$type": "Document", "DocumentIndex": 97, "Title": "ILinqPalInteractionService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Abstractions\\ILinqPalInteractionService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.Application\\Abstractions\\ILinqPalInteractionService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.Application\\Abstractions\\ILinqPalInteractionService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.Application\\Abstractions\\ILinqPalInteractionService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAcAAABuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T12:19:21.52Z"}, {"$type": "Document", "DocumentIndex": 96, "Title": "NodeServiceConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Constants\\NodeServiceConstants.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Constants\\NodeServiceConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Constants\\NodeServiceConstants.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Constants\\NodeServiceConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T11:04:12.771Z"}, {"$type": "Document", "DocumentIndex": 98, "Title": "INodeExternalService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Abstractions\\ExternalServices\\INodeExternalService.cs", "RelativeDocumentMoniker": "BlueTape.Services.OnBoardingService.DataAccess\\Abstractions\\ExternalServices\\INodeExternalService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\onboarding-service\\src\\BlueTape.Services.OnBoardingService.DataAccess\\Abstractions\\ExternalServices\\INodeExternalService.cs", "RelativeToolTip": "BlueTape.Services.OnBoardingService.DataAccess\\Abstractions\\ExternalServices\\INodeExternalService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T12:20:27.892Z"}]}]}]}