﻿using BlueTape.Common.Extensions.Abstractions;
using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.InvoiceClient.Abstractions;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.Utilities.Providers;
using FluentValidation;
using Microsoft.Extensions.Logging;
using BlueTape.Services.LMS.Application.Abstractions.Services.LoanServices;
using BlueTape.DataAccess.Mongo.Documents;
using BlueTape.Services.LMS.Application.Extensions;

namespace BlueTape.Services.LMS.AutoPay.Infrastructure.ProcessDueStrategy;

public class ProcessDueLocStrategy(
    ICompanyHttpClient companyHttpClient,
    IInvoiceHttpClient invoiceHttpClient,
    ILoanRepository loanRepository,
    ILoanApplicationRepository loanApplicationRepository,
    ILoanService loanService,
    IValidator<DueLoanItem> dueLoanItemValidator,
    IDateProvider dateProvider,
    IPaymentExternalService paymentExternalService,
    INotificationService notificationService,
    ISlackNotificationService slackNotificationService,
    ITraceIdAccessor traceIdAccessor,
    ILogger<ProcessDueBaseStrategy> logger) : ProcessDueBaseStrategy(companyHttpClient, loanRepository,
    dueLoanItemValidator, dateProvider, slackNotificationService, traceIdAccessor, logger)
{
    private readonly ILogger<ProcessDueBaseStrategy> _logger = logger;
    public override bool IsApplicable(ProductType productType) => productType is ProductType.LineOfCredit;
    protected override Task InitializeAchPayment(AutoPayModel autoPay, CancellationToken cancellationToken)
    {
        return paymentExternalService.CreateDrawRepaymentAch(autoPay, cancellationToken);
    }

    protected override Task InitializeCardPayment(AutoPayModel autoPay, CancellationToken cancellationToken)
    {
        return paymentExternalService.CreateDrawRepaymentCard(autoPay, cancellationToken);
    }

    protected override BankAccountModel? GetApplicableBankAccountByProduct(List<BankAccountModel> bankAccounts)
    {
        return bankAccounts.FirstOrDefault(x => x.IsPrimaryForCredit.GetValueOrDefault());
    }

    protected override async Task SyncLoanData(AutoPayModel autoPay, CancellationToken cancellationToken)
    {
        var loanApp = await loanApplicationRepository.GetByLmsId(autoPay.DueLoanItem.LoanId.ToString(), cancellationToken);
        if (loanApp == null) return;

        var loan = await loanService.GetById(autoPay.DueLoanItem.LoanId, true, cancellationToken);

        var updateLoanModel = new UpdateLoanApplicationDocument()
        {
            LastPaymentDate = loan.LastPaymentDate is null ? "Invalid date" : loan.LastPaymentDate.Value.ToString("yyyy-MM-dd"),
            NextPaymentDate = loan.LoanDetails?.NextPaymentDate is null ? "Invalid date" : loan.LoanDetails.NextPaymentDate.Value.ToString("yyyy-MM-dd"),
            NextPaymentAmount = (double?)(loan.LoanDetails?.NextPaymentAmount ?? 0),
            RemainingAmount = (double?)(loan.LoanDetails?.LoanOutstandingAmount ?? 0),
            ProcessingAmount = (double?)(loan.LoanDetails?.TotalProcessingPaymentsAmount ?? 0),
            PastDueAmount = (double?)(loan.LoanDetails?.LateAmount ?? 0)
        };

        await loanApplicationRepository.UpdateLoanAppById(loanApp.Id, updateLoanModel, cancellationToken);
    }

    protected override async Task SendUpcomingPaymentNotifications(AutoPayModel autoPay, LoanEntity loan, int dueDays, CancellationToken cancellationToken)
    {
        var ids = autoPay.DueLoanItem.PayableIds.ToArray();
        if (ids.Length == 0) return;

        var invoices = await invoiceHttpClient.GetInvoicesByIdsAsync(ids, cancellationToken);
        if (invoices == null)
        {
            _logger.LogInformation("No invoices for loan with id: {LoanId}", autoPay.DueLoanItem.LoanId);
            return;
        }

        await notificationService.NotifyUsersUpcomingPayment(autoPay, invoices, dueDays, cancellationToken);
    }
}