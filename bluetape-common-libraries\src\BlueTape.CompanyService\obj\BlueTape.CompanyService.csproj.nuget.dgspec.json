{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.CompanyService\\BlueTape.CompanyService.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\BlueTape.AzureKeyVault\\BlueTape.AzureKeyVault.csproj": {"version": "1.0.3", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\BlueTape.AzureKeyVault\\BlueTape.AzureKeyVault.csproj", "projectName": "BlueTape.AzureKeyVault", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\BlueTape.AzureKeyVault\\BlueTape.AzureKeyVault.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\BlueTape.AzureKeyVault\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.Services.Utilities\\BlueTape.Utilities.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.Services.Utilities\\BlueTape.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Azure.Identity": {"target": "Package", "version": "[1.10.4, )"}, "Azure.Security.KeyVault.Keys": {"target": "Package", "version": "[4.5.0, )"}, "Azure.Security.KeyVault.Secrets": {"target": "Package", "version": "[4.5.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\BlueTape.OBS\\BlueTape.OBS.csproj": {"version": "1.6.72", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\BlueTape.OBS\\BlueTape.OBS.csproj", "projectName": "BlueTape.OBS", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\BlueTape.OBS\\BlueTape.OBS.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\BlueTape.OBS\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\BlueTape.ServiceBusMessaging\\BlueTape.ServiceBusMessaging.csproj": {"version": "1.0.9", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\BlueTape.ServiceBusMessaging\\BlueTape.ServiceBusMessaging.csproj", "projectName": "BlueTape.ServiceBusMessaging", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\BlueTape.ServiceBusMessaging\\BlueTape.ServiceBusMessaging.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\BlueTape.ServiceBusMessaging\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\BlueTape.AzureKeyVault\\BlueTape.AzureKeyVault.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\BlueTape.AzureKeyVault\\BlueTape.AzureKeyVault.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Azure.Messaging.ServiceBus": {"target": "Package", "version": "[7.17.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.Common.Extensions\\BlueTape.Common.Extensions.csproj": {"version": "1.1.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.Common.Extensions\\BlueTape.Common.Extensions.csproj", "projectName": "BlueTape.Common.Extensions", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.Common.Extensions\\BlueTape.Common.Extensions.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.Common.Extensions\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.CompanyService.Common\\BlueTape.CompanyService.Common.csproj": {"version": "1.1.21", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.CompanyService.Common\\BlueTape.CompanyService.Common.csproj", "projectName": "BlueTape.CompanyService.Common", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.CompanyService.Common\\BlueTape.CompanyService.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.CompanyService.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\BlueTape.ServiceBusMessaging\\BlueTape.ServiceBusMessaging.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\BlueTape.ServiceBusMessaging\\BlueTape.ServiceBusMessaging.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.LS\\BlueTape.LS.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.LS\\BlueTape.LS.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.CompanyService\\BlueTape.CompanyService.csproj": {"version": "1.3.4", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.CompanyService\\BlueTape.CompanyService.csproj", "projectName": "BlueTape.CompanyService", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.CompanyService\\BlueTape.CompanyService.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.CompanyService\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.CompanyService.Common\\BlueTape.CompanyService.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.CompanyService.Common\\BlueTape.CompanyService.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.LS.Domain\\BlueTape.LS.Domain.csproj": {"version": "1.1.36", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.LS.Domain\\BlueTape.LS.Domain.csproj", "projectName": "BlueTape.LS.Domain", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.LS.Domain\\BlueTape.LS.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.LS.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.Services.Utilities\\BlueTape.Utilities.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.Services.Utilities\\BlueTape.Utilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.LS\\BlueTape.LS.csproj": {"version": "1.1.78", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.LS\\BlueTape.LS.csproj", "projectName": "BlueTape.LS", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.LS\\BlueTape.LS.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.LS\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.LS.Domain\\BlueTape.LS.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.LS.Domain\\BlueTape.LS.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.Services.Utilities\\BlueTape.Utilities.csproj": {"version": "1.4.6", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.Services.Utilities\\BlueTape.Utilities.csproj", "projectName": "BlueTape.Utilities", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.Services.Utilities\\BlueTape.Utilities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.Services.Utilities\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\BlueTape.OBS\\BlueTape.OBS.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\BlueTape.OBS\\BlueTape.OBS.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.Common.Extensions\\BlueTape.Common.Extensions.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\bluetape-common-libraries\\src\\BlueTape.Common.Extensions\\BlueTape.Common.Extensions.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AWSSDK.KeyManagementService": {"target": "Package", "version": "[3.7.300.46, )"}, "AWSSDK.SecretsManager": {"target": "Package", "version": "[3.7.302.21, )"}, "AWSSDK.SecretsManager.Caching": {"target": "Package", "version": "[1.0.6, )"}, "AWSSDK.SecurityToken": {"target": "Package", "version": "[3.7.300.47, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.Http.Polly": {"target": "Package", "version": "[6.0.9, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[8.0.1, )"}, "MongoDB.Bson": {"target": "Package", "version": "[2.25.0, )"}, "Polly": {"target": "Package", "version": "[7.2.3, )"}, "Serilog": {"target": "Package", "version": "[2.12.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[6.0.1, )"}, "Serilog.Enrichers.GlobalLogContext": {"target": "Package", "version": "[2.1.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[4.1.0, )"}, "Serilog.Sinks.Logz.Io": {"target": "Package", "version": "[7.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}