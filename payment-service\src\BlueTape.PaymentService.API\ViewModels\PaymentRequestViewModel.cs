﻿using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.Domain.Enums;

namespace BlueTape.PaymentService.API.ViewModels;

public class PaymentRequestViewModel
{
    public Guid Id { get; set; }
    public decimal Amount { get; set; }
    public decimal FeeAmount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public string FlowTemplateCode { get; set; } = null!;
    public DateOnly Date { get; set; }
    public int MerchantAchDelayInBusinessDays { get; set; }
    public DateTime? ExecuteAfter { get; set; }

    public PaymentRequestStatus Status { get; set; }
    public SubjectType SubjectType { get; set; }
    public PaymentRequestType RequestType { get; set; }
    public PaymentSubscriptionType PaymentSubscription { get; set; }
    public PaymentMethod PaymentMethod { get; set; }

    public string? PayerId { get; set; }
    public string? PayeeId { get; set; }
    public string? SellerId { get; set; }
    public ICollection<PaymentRequestPayableViewModel> PaymentRequestPayables { get; set; } = new List<PaymentRequestPayableViewModel>();
    public ICollection<PaymentRequestFeeViewModel> PaymentRequestFees { get; set; } = new List<PaymentRequestFeeViewModel>();
    public ICollection<PaymentTransactionViewModel> Transactions { get; set; } = new List<PaymentTransactionViewModel>();
    public ICollection<PaymentRequestCommandViewModel> PaymentRequestCommands { get; set; } = new List<PaymentRequestCommandViewModel>();

    public PaymentRequestDetailsViewModel? PaymentRequestDetails { get; set; }

    public Guid? CreditId { get; set; }

    public ConfirmationType ConfirmationType { get; set; }
    public DateTime? ConfirmedAt { get; set; }
    public string? ConfirmedBy { get; set; }

    public int SequenceNumber { get; set; } = 0;

    public string CreatedBy { get; set; } = string.Empty;
    public string UpdatedBy { get; set; } = string.Empty;

    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}