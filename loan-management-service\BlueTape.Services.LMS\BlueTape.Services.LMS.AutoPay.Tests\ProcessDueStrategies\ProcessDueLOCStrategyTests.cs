using BlueTape.Common.Extensions.Abstractions;
using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.DataAccess.Mongo.Abstractions;
using BlueTape.DataAccess.Mongo.Documents;
using BlueTape.InvoiceClient.Abstractions;
using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Abstractions.Services.LoanServices;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.AutoPay.Infrastructure.ProcessDueStrategy;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.Utilities.Providers;
using FluentValidation;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Services.LMS.AutoPay.Tests.ProcessDueStrategies;

[Serializable]
public class SyncLoanDataTestCase
{
    public DateOnly? LastPaymentDate { get; set; }
    public DateOnly? NextPaymentDate { get; set; }
    public decimal NextPaymentAmount { get; set; }
    public decimal OutstandingAmount { get; set; }
    public decimal ProcessingAmount { get; set; }
    public decimal LateAmount { get; set; }
    public bool IsLoanDetailsNull { get; set; }
    public string? ExpectedLastPaymentDate { get; set; }
    public string? ExpectedNextPaymentDate { get; set; }
    public decimal? ExpectedNextPaymentAmount { get; set; }
    public decimal ExpectedRemainingAmount { get; set; }
    public decimal ExpectedProcessingAmount { get; set; }
    public decimal ExpectedPastDueAmount { get; set; }
    public string TestName { get; set; } = string.Empty;
}

public class ProcessDueLOCStrategyTests
{
    private readonly Mock<ICompanyHttpClient> _companyHttpClientMock;
    private readonly Mock<IInvoiceHttpClient> _invoiceHttpClientMock;
    private readonly Mock<ILoanRepository> _loanRepositoryMock;
    private readonly Mock<ILoanApplicationRepository> _loanApplicationRepositoryMock;
    private readonly Mock<ILoanService> _loanServiceMock;
    private readonly Mock<IValidator<DueLoanItem>> _dueLoanItemValidatorMock;
    private readonly Mock<IDateProvider> _dateProviderMock;
    private readonly Mock<IPaymentExternalService> _paymentExternalServiceMock;
    private readonly Mock<INotificationService> _notificationServiceMock;
    private readonly Mock<ISlackNotificationService> _slackNotificationServiceMock;
    private readonly Mock<ITraceIdAccessor> _traceIdAccessorMock;
    private readonly Mock<ILogger<ProcessDueBaseStrategy>> _loggerMock;
    private readonly ProcessDueLocStrategy _strategy;

    public ProcessDueLOCStrategyTests()
    {
        _companyHttpClientMock = new Mock<ICompanyHttpClient>();
        _invoiceHttpClientMock = new Mock<IInvoiceHttpClient>();
        _loanRepositoryMock = new Mock<ILoanRepository>();
        _loanApplicationRepositoryMock = new Mock<ILoanApplicationRepository>();
        _loanServiceMock = new Mock<ILoanService>();
        _dueLoanItemValidatorMock = new Mock<IValidator<DueLoanItem>>();
        _dateProviderMock = new Mock<IDateProvider>();
        _paymentExternalServiceMock = new Mock<IPaymentExternalService>();
        _notificationServiceMock = new Mock<INotificationService>();
        _slackNotificationServiceMock = new Mock<ISlackNotificationService>();
        _traceIdAccessorMock = new Mock<ITraceIdAccessor>();
        _loggerMock = new Mock<ILogger<ProcessDueBaseStrategy>>();

        _strategy = new ProcessDueLocStrategy(
            _companyHttpClientMock.Object,
            _invoiceHttpClientMock.Object,
            _loanRepositoryMock.Object,
            _loanApplicationRepositoryMock.Object,
            _loanServiceMock.Object,
            _dueLoanItemValidatorMock.Object,
            _dateProviderMock.Object,
            _paymentExternalServiceMock.Object,
            _notificationServiceMock.Object,
            _slackNotificationServiceMock.Object,
            _traceIdAccessorMock.Object,
            _loggerMock.Object
        );
    }

    [Fact]
    public async Task ProcessDue_NoLoans_ShouldLogAndReturn()
    {
        // Arrange
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>()
        };

        // Act
        await _strategy.ProcessDue(message, CancellationToken.None);

        // Assert
        _companyHttpClientMock.Verify(
            x => x.GetBankAccountsByCompanyIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => true),
                null,
                It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
            Times.Once);
    }

    [Fact]
    public async Task ProcessDue_WithLoans_ShouldProcessEachLoan()
    {
        // Arrange
        var today = DateOnly.FromDateTime(DateTime.UtcNow);
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>
            {
                new() { LoanId = Guid.NewGuid(), NextPaymentDate = today, NextPaymentAmount = 1000 }
            }
        };

        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsPrimaryForCredit = true, PaymentMethodType = "bank" }
        };

        var loan = new LoanEntity { IsAutoCollectionPaused = false };

        _companyHttpClientMock.Setup(x =>
                x.GetBankAccountsByCompanyIdAsync(message.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(today);

        // Act
        await _strategy.ProcessDue(message, CancellationToken.None);

        // Assert
        _paymentExternalServiceMock.Verify(x => x.CreateDrawRepaymentAch(
                It.Is<AutoPayModel>(m => m.PaymentAmount == 1000),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Theory]
    [MemberData(nameof(GetSyncLoanDataTestCases))]
    public async Task SyncLoanData_ShouldUpdateLoanApplicationProperties(SyncLoanDataTestCase testCase)
    {
        var loanId = Guid.NewGuid();
        var loanIdString = loanId.ToString();
        var today = DateOnly.FromDateTime(DateTime.UtcNow);
        string invoiceId = "test-payable-id";

        var loanApp = new LoanApplicationDocument
        {
            LmsId = loanIdString
        };

        var loan = new Loan
        {
            Id = loanId,
            LastPaymentDate = testCase.LastPaymentDate,
            LoanDetails = testCase.IsLoanDetailsNull
                ? null
                : new LoanDetails
                {
                    NextPaymentDate = testCase.NextPaymentDate,
                    NextPaymentAmount = testCase.NextPaymentAmount,
                    LoanOutstandingAmount = testCase.OutstandingAmount,
                    TotalProcessingPaymentsAmount = testCase.ProcessingAmount,
                    LateAmount = testCase.LateAmount
                }
        };

        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans =
            [
                new()
                {
                    LoanId = loanId,
                    NextPaymentDate = today.AddDays(3),
                    NextPaymentAmount = 1000,
                    PayableIds = [invoiceId]
                }
            ]
        };

        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsPrimaryForCredit = true, PaymentMethodType = "bank" }
        };

        var loanEntity = new LoanEntity { IsAutoCollectionPaused = false };

        _companyHttpClientMock.Setup(x =>
                x.GetBankAccountsByCompanyIdAsync(message.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);

        _loanRepositoryMock.Setup(x => x.GetById(loanId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(loanEntity);

        _loanApplicationRepositoryMock
            .Setup(x => x.GetByLmsId(loanIdString, It.IsAny<CancellationToken>()))
            .ReturnsAsync(loanApp);

        _loanServiceMock
            .Setup(x => x.GetById(loanId, true, It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);

        _loanApplicationRepositoryMock
            .Setup(x => x.UpdateLoanAppById(It.IsAny<string>(), It.IsAny<UpdateLoanApplicationDocument>(), It.IsAny<CancellationToken>()));

        await _strategy.ProcessDue(message, CancellationToken.None);

        _loanApplicationRepositoryMock.Verify(x => x.UpdateLoanAppById(It.IsAny<string>(),
            It.Is<UpdateLoanApplicationDocument>(doc =>
            (doc.LastPaymentDate == testCase.ExpectedLastPaymentDate || doc.LastPaymentDate == "Invalid date") &&
            (doc.NextPaymentDate == testCase.ExpectedNextPaymentDate || doc.NextPaymentDate == "Invalid date") &&
            doc.NextPaymentAmount == (double?)testCase.ExpectedNextPaymentAmount &&
            doc.RemainingAmount == (double?)testCase.ExpectedRemainingAmount &&
            doc.ProcessingAmount == (double?)testCase.ExpectedProcessingAmount &&
            doc.PastDueAmount == (double?)testCase.ExpectedPastDueAmount
        ), It.IsAny<CancellationToken>()), Times.Once);
    }

    // Change the return type and method implementation
    public static TheoryData<SyncLoanDataTestCase> GetSyncLoanDataTestCases()
    {
        var theoryData = new TheoryData<SyncLoanDataTestCase>();

        // Test case 1: All properties have values
        theoryData.Add(new SyncLoanDataTestCase
        {
            TestName = "All properties have values",
            LastPaymentDate = new DateOnly(2025, 1, 1),
            NextPaymentDate = new DateOnly(2025, 2, 1),
            NextPaymentAmount = 100.50m,
            OutstandingAmount = 1000.75m,
            ProcessingAmount = 200.25m,
            LateAmount = 50.25m,
            IsLoanDetailsNull = false,
            ExpectedLastPaymentDate = "2025-01-01",
            ExpectedNextPaymentDate = "2025-02-01",
            ExpectedNextPaymentAmount = 100.50m,
            ExpectedRemainingAmount = 1000.75m,
            ExpectedProcessingAmount = 200.25m,
            ExpectedPastDueAmount = 50.25m
        });

        // Test case 2: LastPaymentDate is null
        theoryData.Add(new SyncLoanDataTestCase
        {
            TestName = "LastPaymentDate is null",
            LastPaymentDate = null,
            NextPaymentDate = new DateOnly(2025, 2, 1),
            NextPaymentAmount = 100.50m,
            OutstandingAmount = 1000.75m,
            ProcessingAmount = 200.25m,
            LateAmount = 50.25m,
            IsLoanDetailsNull = false,
            ExpectedLastPaymentDate = null,
            ExpectedNextPaymentDate = "2025-02-01",
            ExpectedNextPaymentAmount = 100.50m,
            ExpectedRemainingAmount = 1000.75m,
            ExpectedProcessingAmount = 200.25m,
            ExpectedPastDueAmount = 50.25m
        });

        // Test case 3: NextPaymentDate is null
        theoryData.Add(new SyncLoanDataTestCase
        {
            TestName = "NextPaymentDate is null",
            LastPaymentDate = new DateOnly(2025, 1, 1),
            NextPaymentDate = null,
            NextPaymentAmount = 100.50m,
            OutstandingAmount = 1000.75m,
            ProcessingAmount = 200.25m,
            LateAmount = 50.25m,
            IsLoanDetailsNull = false,
            ExpectedLastPaymentDate = "2025-01-01",
            ExpectedNextPaymentDate = null,
            ExpectedNextPaymentAmount = 100.50m,
            ExpectedRemainingAmount = 1000.75m,
            ExpectedProcessingAmount = 200.25m,
            ExpectedPastDueAmount = 50.25m
        });

        // Test case 4: LoanDetails is fully null
        theoryData.Add(new SyncLoanDataTestCase
        {
            TestName = "LoanDetails is fully null",
            LastPaymentDate = new DateOnly(2025, 1, 1),
            NextPaymentDate = null,
            NextPaymentAmount = 0m,
            OutstandingAmount = 0m,
            ProcessingAmount = 0m,
            LateAmount = 0m,
            IsLoanDetailsNull = true,
            ExpectedLastPaymentDate = "2025-01-01",
            ExpectedNextPaymentDate = null,
            ExpectedNextPaymentAmount = 0m,
            ExpectedRemainingAmount = 0m,
            ExpectedProcessingAmount = 0m,
            ExpectedPastDueAmount = 0m
        });

        return theoryData;
    }

    [Fact]
    public async Task GetApplicableBankAccount_WithLOCPrimaryAccount_ShouldReturnLOCPrimaryAccount()
    {
        // Arrange
        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsPrimaryForCredit = false },
            new() { IsPrimaryForCredit = true },
            new() { IsPrimaryForCredit = false }
        };

        _companyHttpClientMock
            .Setup(x => x.GetBankAccountsByCompanyIdAsync("test-company", It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);

        // Act
        var result =
            await _companyHttpClientMock.Object.GetBankAccountsByCompanyIdAsync("test-company", CancellationToken.None);
        var locAccount = result.FirstOrDefault(x => x.IsPrimaryForCredit == true);

        // Assert
        locAccount.ShouldNotBeNull();
        locAccount.IsPrimaryForCredit.GetValueOrDefault().ShouldBeTrue();
    }

    [Fact]
    public async Task ProcessDue_WithOverdueAmount_ShouldProcessOverduePayment()
    {
        // Arrange
        var today = DateOnly.FromDateTime(DateTime.UtcNow);
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>
            {
                new()
                {
                    LoanId = Guid.NewGuid(),
                    NextPaymentDate = today.AddDays(5),
                    IsOverdue = true,
                    OverDueAmount = 500
                }
            }
        };

        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsPrimaryForCredit = true, PaymentMethodType = "bank" }
        };

        var loan = new LoanEntity { IsAutoCollectionPaused = false };

        _companyHttpClientMock.Setup(x =>
                x.GetBankAccountsByCompanyIdAsync(message.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(today);

        // Act
        await _strategy.ProcessDue(message, CancellationToken.None);

        // Assert
        _paymentExternalServiceMock.Verify(x => x.CreateDrawRepaymentAch(
                It.Is<AutoPayModel>(m => m.PaymentAmount == 500),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task ProcessDue_DueInThreeDays_ShouldSendNotification()
    {
        string invoiceId = "test-payable-id";
        // Arrange
        var today = DateOnly.FromDateTime(DateTime.UtcNow);
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>
            {
                new()
                {
                    LoanId = Guid.NewGuid(),
                    NextPaymentDate = today.AddDays(3),
                    NextPaymentAmount = 1000,
                    PayableIds = [invoiceId]
                }
            }
        };

        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsPrimaryForCredit = true, PaymentMethodType = "bank" }
        };

        var loan = new LoanEntity { IsAutoCollectionPaused = false };

        _companyHttpClientMock.Setup(x =>
                x.GetBankAccountsByCompanyIdAsync(message.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(today);
        _invoiceHttpClientMock.Setup(x => x.GetInvoicesByIdsAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([new InvoiceModel() { Id = invoiceId, InvoiceNumber = invoiceId }]);

        // Act
        await _strategy.ProcessDue(message, CancellationToken.None);

        // Assert
        _notificationServiceMock.Verify(x => x.NotifyUsersUpcomingPayment(
                It.IsAny<AutoPayModel>(),
                It.IsAny<List<InvoiceModel>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>(),
                false),
            Times.Once);
    }

    [Fact]
    public async Task ProcessDue_WithCardPayment_ShouldProcessCardPayment()
    {
        // Arrange
        var today = DateOnly.FromDateTime(DateTime.UtcNow);
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>
            {
                new()
                {
                    LoanId = Guid.NewGuid(),
                    NextPaymentDate = today,
                    NextPaymentAmount = 1000
                }
            }
        };

        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsPrimaryForCredit = true, PaymentMethodType = "card" }
        };

        var loan = new LoanEntity { IsAutoCollectionPaused = false };

        _companyHttpClientMock.Setup(x =>
                x.GetBankAccountsByCompanyIdAsync(message.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(today);

        // Act
        await _strategy.ProcessDue(message, CancellationToken.None);

        // Assert
        _paymentExternalServiceMock.Verify(x => x.CreateDrawRepaymentCard(
                It.Is<AutoPayModel>(m => m.PaymentAmount == 1000),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task ProcessDue_WithMultipleLoans_ShouldProcessAllLoans()
    {
        // Arrange
        var today = DateOnly.FromDateTime(DateTime.UtcNow);
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>
            {
                new() { LoanId = Guid.NewGuid(), NextPaymentDate = today, NextPaymentAmount = 1000 },
                new() { LoanId = Guid.NewGuid(), NextPaymentDate = today, NextPaymentAmount = 2000 }
            }
        };

        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsPrimaryForCredit = true, PaymentMethodType = "bank" }
        };

        var loan = new LoanEntity { IsAutoCollectionPaused = false };

        _companyHttpClientMock.Setup(x =>
                x.GetBankAccountsByCompanyIdAsync(message.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(today);

        // Act
        await _strategy.ProcessDue(message, CancellationToken.None);

        // Assert
        _paymentExternalServiceMock.Verify(x => x.CreateDrawRepaymentAch(
                It.IsAny<AutoPayModel>(),
                It.IsAny<CancellationToken>()),
            Times.Exactly(2));
    }

    [Fact]
    public async Task ProcessDue_WithValidationError_ShouldContinueProcessingOtherLoans()
    {
        // Arrange
        var today = DateOnly.FromDateTime(DateTime.UtcNow);
        var firstLoanId = Guid.NewGuid();
        var secondLoanId = Guid.NewGuid();
        var message = new DueLoanMessage
        {
            CompanyId = "test-company",
            Loans = new List<DueLoanItem>
            {
                new() { LoanId = firstLoanId, NextPaymentDate = today, NextPaymentAmount = 1000 },
                new() { LoanId = secondLoanId, NextPaymentDate = today, NextPaymentAmount = 2000 }
            }
        };

        var bankAccounts = new List<BankAccountModel>
        {
            new() { IsPrimaryForCredit = true, PaymentMethodType = "bank" }
        };

        var loan = new LoanEntity { IsAutoCollectionPaused = false };

        _companyHttpClientMock.Setup(x =>
                x.GetBankAccountsByCompanyIdAsync(message.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(loan);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(today);

        // Setup validation to fail for the first loan
        _dueLoanItemValidatorMock.Setup(x =>
                x.ValidateAsync(It.Is<DueLoanItem>(l => l.LoanId == firstLoanId), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new ValidationException("Validation failed"));

        // Setup validation to pass for the second loan
        _dueLoanItemValidatorMock.Setup(x =>
                x.ValidateAsync(It.Is<DueLoanItem>(l => l.LoanId == secondLoanId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new FluentValidation.Results.ValidationResult());

        // Act
        await _strategy.ProcessDue(message, CancellationToken.None);

        // Assert
        _paymentExternalServiceMock.Verify(x => x.CreateDrawRepaymentAch(
                It.Is<AutoPayModel>(m => m.PaymentAmount == 2000),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }
}