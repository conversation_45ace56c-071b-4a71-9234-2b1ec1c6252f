﻿using BlueTape.PaymentService.API.ViewModels;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.IntegrationTests.PaymentFlowTests.Base;
using BlueTape.PaymentService.IntegrationTests.TestConstants;
using BlueTape.PaymentService.IntegrationTests.TestData;

namespace BlueTape.PaymentService.IntegrationTests.PaymentFlowTests;

[Collection(Configuration.SequentialExecution)]
public class MixedCasesTests() : BasePaymentFlowTest(dbName: "mixedCasesDb")
{
    private readonly int firstStepNumber = 1;

    [Theory]
    [InlineData(DomainConstants.InvoicePayment)]
    [InlineData(DomainConstants.FactoringFinalPayment)]
    [InlineData(DomainConstants.InvoicePaymentV2)]
    [InlineData(DomainConstants.DrawRepayment)]
    [InlineData(DomainConstants.FinalPayment)]
    [InlineData(DomainConstants.FinalPaymentV2)]
    [InlineData(DomainConstants.InvoiceDisbursementV2)]
    public async Task Payments_MixedErrors_ProperBehaviour(string templateCode)
    {
        var message = PaymentRequestData.CreateValidPaymentRequest(templateCode);
        var payments = new List<PaymentRequestViewModel>();

        for (var i = 0; i < 5; i++)
        {
            var payment = await CreateAndAssertPayment(message);
            payments.Add(payment!);
        }

        var paymentIds = payments.Select(x => x.Id).ToList();

        var firstPayment = payments.First();
        var httpClientExceptionPayment = await CreateAndAssertPayment(PaymentRequestData.CreateValidPaymentRequest(templateCode, amount: Configuration.HttpClientExceptionAmount));
        var aionResponseExceptionPayment = await CreateAndAssertPayment(PaymentRequestData.CreateValidPaymentRequest(templateCode, amount: Configuration.AionExceptionAmount));
        var internalExceptionPayment = await CreateAndAssertPayment(PaymentRequestData.CreateValidPaymentRequest(templateCode, amount: Configuration.InternalExceptionAmount));
        var tooManyRequestPayment = await CreateAndAssertPayment(PaymentRequestData.CreateValidPaymentRequest(templateCode, amount: Configuration.TooManyRequestsAmount));
        var noEnoughBalancePayment = await CreateAndAssertPayment(PaymentRequestData.CreateValidPaymentRequest(templateCode, amount: (double)Configuration.DefaultBalanceForAion * 2));

        var steps = TemplateOptions.GetTemplateSteps(templateCode);
        for (int i = firstStepNumber; i <= steps; i++)
        {
            await ExecuteAndAssertTestCommandCycle(firstPayment.Id, i);
        }

        await ExecutePaymentScheduledJob();

        var details = await GetPaymentRequestById(firstPayment.Id);
        details.Status.ShouldBe(PaymentRequestStatus.Settled);

        var processedRequests = (await GetPaymentRequests())?.Result;
        var settledPayments = processedRequests!.Where(x => paymentIds.Contains(x.Id)).ToList();

        foreach (var settledPayment in settledPayments)
        {
            var commands = await GetPaymentRequestCommands(settledPayment.Id.ToString());
            AssertTransactionStatuses(settledPayment.Transactions, TransactionStatus.Cleared, steps);
            AssertCommandStatuses(commands!, CommandStatus.Executed, steps);
            settledPayment.Status.ShouldBe(PaymentRequestStatus.Settled);
        }

        details = await GetPaymentRequestById(httpClientExceptionPayment.Id);
        AssertTransactionStatuses(details.Transactions, TransactionStatus.Error, firstStepNumber);
        AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Failed, firstStepNumber);
        details.Status.ShouldBe(PaymentRequestStatus.Failed);

        details = await GetPaymentRequestById(aionResponseExceptionPayment.Id);
        if (aionResponseExceptionPayment.FlowTemplateCode is DomainConstants.FinalPayment or DomainConstants.FinalPaymentV2)
        {
            AssertTransactionStatuses(details.Transactions, TransactionStatus.Error, firstStepNumber);
            AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Failed, firstStepNumber);
            details.Status.ShouldBe(PaymentRequestStatus.Failed);
        }
        else
        {
            AssertTransactionStatuses(details.Transactions, TransactionStatus.Error, firstStepNumber);
            AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Failed, firstStepNumber);
            details.Status.ShouldBe(PaymentRequestStatus.Failed);
        }

        details = await GetPaymentRequestById(internalExceptionPayment.Id);
        AssertTransactionStatuses(details.Transactions, TransactionStatus.Error, firstStepNumber);
        AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Failed, firstStepNumber);
        details.Status.ShouldBe(PaymentRequestStatus.Failed);

        details = await GetPaymentRequestById(tooManyRequestPayment.Id);
        AssertTransactionStatuses(details.Transactions, TransactionStatus.Placed, firstStepNumber);
        AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Pending, firstStepNumber);
        details.Status.ShouldBe(PaymentRequestStatus.Requested);

        details = await GetPaymentRequestById(noEnoughBalancePayment.Id);
        switch (templateCode)
        {
            case DomainConstants.DrawRepayment:
            case DomainConstants.InvoicePaymentV2:
                AssertTransactionStatuses(details.Transactions, TransactionStatus.Cleared, firstStepNumber);
                AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Executed, firstStepNumber);
                details.Status.ShouldBe(PaymentRequestStatus.Settled);
                break;
            case DomainConstants.FinalPaymentV2:
            case DomainConstants.FactoringFinalPayment:
            case DomainConstants.InvoiceDisbursementV2:
                AssertTransactionStatuses(details.Transactions, TransactionStatus.Placed, firstStepNumber);
                AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Placed, firstStepNumber);
                details.Status.ShouldBe(PaymentRequestStatus.Requested);
                break;
            case DomainConstants.FinalPayment:
            case DomainConstants.FactoringDisbursement:
            case DomainConstants.DrawDisbursement:
                AssertTransactionStatuses(details.Transactions, TransactionStatus.Placed, firstStepNumber);
                AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Pending, firstStepNumber);
                details.Status.ShouldBe(PaymentRequestStatus.Requested);
                break;
            case DomainConstants.InvoicePayment:
                AssertTransactionStatuses(details.Transactions, TransactionStatus.Placed, 2);
                AssertCommandStatuses(details.PaymentRequestCommands, CommandStatus.Placed, 2);
                details.Status.ShouldBe(PaymentRequestStatus.Processing);
                break;
        }
    }
}
