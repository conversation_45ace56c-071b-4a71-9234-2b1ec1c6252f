using BlueTape.BackOffice.DecisionEngine.Application.Messages.Models;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Admin.Models.Payments;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Payments.Models;
using BlueTape.BackOffice.DecisionEngine.Domain.Models;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.OBS.DTOs;
using Microsoft.AspNetCore.Mvc;

namespace BlueTape.BackOffice.DecisionEngine.Application.Services.Payments;

public interface IBackofficePaymentService
{
    Task<AionAccount> GetAccountByTypeCode(AccountCodeType accountCodeType, CancellationToken ct);
    Task<List<AionTransaction>?> GetAccountTransactions(AccountCodeType accountCode, CancellationToken ct);
    List<AionAccountShort> GetManualPaymentAccounts(string userId, CancellationToken ct);
    List<PaymentMethodDto> GetManualPaymentMethods(string userId, CancellationToken ct);
    Task<GetQueryWithPaginationResultDto<PaymentResponse>> GetDisbursements(PaymentRequestFilterQuery query, CancellationToken cancellationToken);
    Task CreateManualPull(CreateManualPull createManualPull, string userId, CancellationToken ct);
    Task<DrawRepaymentManualRequestMessage> SendManualInternalPaymentRequestMessage(CreateManualPaymentRequest request, string userId, CancellationToken ct);
    Task<GetQueryWithPaginationResultDto<PaymentsSubscriptionResponse>> GetSubscriptions(SubscriptionPaymentsQuery query, CancellationToken cancellationToken);
    Task CreateSubscriptionPayment(CreateSubscriptionPaymentRequest request, string userId, CancellationToken ct);
    Task<FileStreamResult> ExportSubscriptions(SubscriptionPaymentsQuery query, CancellationToken cancellationToken);
    Task ApprovePaymentRequest(Guid id, ApprovePaymentRequestModel request, string userId, CancellationToken ct);
    Task<string?> GetAvailablePaymentMethods(CancellationToken cancellationToken);
    Task<IEnumerable<PaymentRequestModel>> GetDisbursementQueuesPaymentRequests(string provider, string subscriptionCode, CancellationToken cancellationToken);
    Task UpdateSequenceOrder(List<Guid> paymentRequestIds, string userId, CancellationToken cancellationToken);
}
