{"openapi": "3.0.1", "info": {"title": "BlueTape.PaymentService.API", "version": "1.0"}, "paths": {"/admin/paymentRequests/disbursementQueues/sequence": {"put": {"tags": ["Admin"], "summary": "Updates the sequence order of payment requests in disbursement queues", "parameters": [{"name": "updatedBy", "in": "header", "description": "User ID performing the update", "required": true, "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"description": "Request containing the ordered list of payment request IDs", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSequenceOrderViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateSequenceOrderViewModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateSequenceOrderViewModel"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UpdateSequenceOrderResponseViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateSequenceOrderResponseViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateSequenceOrderResponseViewModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/api/AionDailyLimits/{subscriptionType}": {"get": {"tags": ["Aion Daily Limits Testing"], "parameters": [{"name": "subscriptionType", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/PaymentSubscriptionType"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AionDailyLimitConfig"}}}}}}}, "/api/AionDailyLimits/{subscriptionType}/check/{transactionType}": {"get": {"tags": ["Aion Daily Limits Testing"], "parameters": [{"name": "subscriptionType", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/PaymentSubscriptionType"}}, {"name": "transactionType", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/PaymentTransactionType"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "boolean"}}}}}}}, "/api/AionDailyLimits/{subscriptionType}/set/{transactionType}": {"post": {"tags": ["Aion Daily Limits Testing"], "parameters": [{"name": "subscriptionType", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/PaymentSubscriptionType"}}, {"name": "transactionType", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/PaymentTransactionType"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/api/AionDailyLimits/{subscriptionType}/reset": {"post": {"tags": ["Aion Daily Limits Testing"], "parameters": [{"name": "subscriptionType", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/PaymentSubscriptionType"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/api/AionDailyLimits/get-all": {"get": {"tags": ["Aion Daily Limits Testing"], "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/api/AionDailyLimits/reset-all": {"post": {"tags": ["Aion Daily Limits Testing"], "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/Companies": {"get": {"tags": ["Companies"], "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyModel"}}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}, "post": {"tags": ["Companies"], "parameters": [{"name": "userId", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForbidCompanyRequestViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ForbidCompanyRequestViewModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ForbidCompanyRequestViewModel"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CompanyModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CompanyModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/Companies/{companyId}": {"delete": {"tags": ["Companies"], "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/Companies/{companyId}/is-company-payments-forbidden": {"get": {"tags": ["Companies"], "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/customer-have-to-choose-different-payment-method": {"patch": {"tags": ["EmailNotification"], "parameters": [{"name": "payerId", "in": "query", "schema": {"type": "string"}}, {"name": "merchantName", "in": "query", "schema": {"type": "string"}}, {"name": "paymentRequestId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "transactionHistoryId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/payment-is-created": {"post": {"tags": ["EmailNotification"], "parameters": [{"name": "paymentRequestId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/transactions/{id}/public-transaction-number": {"patch": {"tags": ["Helper"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "generatedBy", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/errorCodes": {"get": {"tags": ["Helper"], "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/sendConnectorMessage": {"post": {"tags": ["Helper"], "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConnectorMessagePayload"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConnectorMessagePayload"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ConnectorMessagePayload"}}}}, "responses": {"200": {"description": "Success"}}}}, "/health": {"get": {"tags": ["Helper"], "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/loanPayments/{id}": {"post": {"tags": ["LoanPayments"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "invoiceId", "in": "query", "schema": {"type": "string"}}, {"name": "drawId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "amount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/Notifications": {"get": {"tags": ["Notifications"], "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestNotificationViewModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestNotificationViewModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestNotificationViewModel"}}}}}}}}, "/Notifications/payment-request/{id}": {"get": {"tags": ["Notifications"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestNotificationViewModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestNotificationViewModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestNotificationViewModel"}}}}}}}}, "/Notifications/{id}": {"get": {"tags": ["Notifications"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PaymentRequestNotificationViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestNotificationViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestNotificationViewModel"}}}}}}}, "/Operations/invoice/{id}/is-operation-exist": {"get": {"tags": ["Operations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/Operations/invoice/{id}/add-placed": {"post": {"tags": ["Operations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/Operations/invoice/{id}/update-placed": {"post": {"tags": ["Operations"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/PaymentWindow/config": {"get": {"tags": ["Payment Window Configuration"], "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentWindowConfig"}}}}}}, "put": {"tags": ["Payment Window Configuration"], "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentWindowConfig"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentWindowConfig"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PaymentWindowConfig"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/PaymentWindow/status": {"get": {"tags": ["Payment Window Configuration"], "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/api/PaymentWindow/next-window": {"get": {"tags": ["Payment Window Configuration"], "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/api/PaymentWindow/toggle": {"post": {"tags": ["Payment Window Configuration"], "parameters": [{"name": "enabled", "in": "query", "schema": {"type": "boolean"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/api/PaymentWindow/duration/{durationMinutes}": {"put": {"tags": ["Payment Window Configuration"], "parameters": [{"name": "durationMinutes", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/PaymentRequests/{id}": {"get": {"tags": ["PaymentRequests"], "summary": "Gets a single payment request full details", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}}}}}, "delete": {"tags": ["PaymentRequests"], "summary": "Cancels a payment request", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "userId", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/PaymentRequests/draw/{id}": {"delete": {"tags": ["PaymentRequests"], "summary": "Cancels a payment request", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "userId", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/PaymentRequests": {"get": {"tags": ["PaymentRequests"], "summary": "Lists payment requests by various filtering options", "parameters": [{"name": "Id", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "DrawId", "in": "query", "schema": {"type": "string"}}, {"name": "Filter", "in": "query", "schema": {"type": "string"}}, {"name": "FlowTemplateCodes", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "From", "in": "query", "schema": {"type": "string", "format": "date"}}, {"name": "To", "in": "query", "schema": {"type": "string", "format": "date"}}, {"name": "CustomerId", "in": "query", "schema": {"type": "string"}}, {"name": "SellerId", "in": "query", "schema": {"type": "string"}}, {"name": "PayableId", "in": "query", "schema": {"type": "string"}}, {"name": "PaymentRequestStatuses", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "IsConfirmed", "in": "query", "schema": {"type": "boolean"}}, {"name": "SortOrder", "in": "query", "schema": {"$ref": "#/components/schemas/SortOrder"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Items", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModelPaginatedResultViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModelPaginatedResultViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModelPaginatedResultViewModel"}}}}}}, "post": {"tags": ["PaymentRequests"], "summary": "Create a payment request", "parameters": [{"name": "created<PERSON>y", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentRequestViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentRequestViewModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePaymentRequestViewModel"}}}}, "responses": {"201": {"description": "Created", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/PaymentRequests/{id}/transactions": {"post": {"tags": ["PaymentRequests"], "summary": "Creates a transaction for payment request", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "created<PERSON>y", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentRequestTransactionViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentRequestTransactionViewModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePaymentRequestTransactionViewModel"}}}}, "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/PaymentRequests/{id}/commands": {"get": {"tags": ["PaymentRequests"], "summary": "Get commands", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestCommandViewModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestCommandViewModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestCommandViewModel"}}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/PaymentRequests/{id}/transactions/{transactionId}/execute": {"post": {"tags": ["PaymentRequests"], "summary": "Executes a transaction", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "updatedBy", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/paymentRequests/payable/{id}/is-payment-request-exist": {"get": {"tags": ["PaymentRequests"], "summary": "Checks whether a payment request exists for a payable", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/paymentRequests/{id}/transactions/{transactionId}/retry": {"patch": {"tags": ["PaymentRequests"], "summary": "Manually retry a failed transaction", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "userId", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PaymentTransactionViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PaymentTransactionViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentTransactionViewModel"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/PaymentRequests/{id}/retry": {"patch": {"tags": ["PaymentRequests"], "summary": "Retry transaction by payment request id", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "userId", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/paymentRequests/{id}/transactions/{transactionId}/retry/pull-from-customer": {"patch": {"tags": ["PaymentRequests"], "summary": "Manually retry a failed pull from customer transaction", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "transactionId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "userId", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RetryFailedTransactionRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RetryFailedTransactionRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RetryFailedTransactionRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PaymentTransactionViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PaymentTransactionViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentTransactionViewModel"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/paymentRequests/{id}/pause": {"put": {"tags": ["PaymentRequests"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "userId", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PausePaymentRequestViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PausePaymentRequestViewModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PausePaymentRequestViewModel"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/paymentRequests/total-amount": {"get": {"tags": ["PaymentRequests"], "parameters": [{"name": "includeProcessingStatus", "in": "query", "schema": {"type": "boolean"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}}}}}}, "/paymentRequests/{id}/approve": {"patch": {"tags": ["PaymentRequests"], "summary": "Approve manual payment approvalRequest", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "created<PERSON>y", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentApprovalRequestViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentApprovalRequestViewModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PaymentApprovalRequestViewModel"}}}}, "responses": {"200": {"description": "Success"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/PaymentRequests/{id}/mark-payment-request-succeeded": {"patch": {"tags": ["PaymentRequests"], "summary": "Mark payment request as succeeded", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "updatedBy", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarkPaymentRequestSucceededViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MarkPaymentRequestSucceededViewModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MarkPaymentRequestSucceededViewModel"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/PaymentRequests/accounts": {"get": {"tags": ["PaymentRequests"], "summary": "Get accounts", "parameters": [{"name": "paymentProvider", "in": "query", "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AccountResponseObj"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AccountResponseObj"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AccountResponseObj"}}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/QueueEvents/payment-request": {"post": {"tags": ["QueueEvents"], "summary": "Handles message from queue to create Pay Now payment request", "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}}}}}}, "/QueueEvents/payment-scheduled-job": {"post": {"tags": ["QueueEvents"], "summary": "Triggers payment scheduled job", "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/QueueEvents/transaction-status-update-scheduled-job": {"get": {"tags": ["QueueEvents"], "summary": "Triggers transaction status update scheduled job", "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/QueueEvents/notification-consumer": {"post": {"tags": ["QueueEvents"], "summary": "Triggers notification consumer", "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationMessagePayloadV2"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationMessagePayloadV2"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NotificationMessagePayloadV2"}}}}, "responses": {"200": {"description": "Success"}}}}, "/QueueEvents/transaction-status-update-job": {"post": {"tags": ["QueueEvents"], "summary": "Triggers transaction status update job", "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionStatusMessagePayload"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionStatusMessagePayload"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TransactionStatusMessagePayload"}}}}, "responses": {"200": {"description": "Success"}}}}, "/QueueEvents/legacy/sync-operation/{id}": {"post": {"tags": ["QueueEvents"], "summary": "Triggers legacy operation sync in MongoDB by payment request id", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/QueueEvents/legacy/perform-operation/{id}": {"post": {"tags": ["QueueEvents"], "summary": "Performs legacy operation in MongoDB by payment request id", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/QueueEvents/command-event-processor/{commandId}": {"get": {"tags": ["QueueEvents"], "parameters": [{"name": "commandId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/paymentRequests/disbursementQueues": {"get": {"tags": ["Queues"], "summary": "Gets disbursement queues payment requests", "parameters": [{"name": "provider", "in": "query", "description": "The payment provider (aion, cbw)", "schema": {"type": "string"}}, {"name": "subscriptionCode", "in": "query", "description": "The subscription code of the payment provider as string", "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestViewModel"}}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/all/year/{year}/month/{month}/day/{day}": {"get": {"tags": ["Report"], "parameters": [{"name": "year", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "month", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "day", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "reportType", "in": "query", "schema": {"$ref": "#/components/schemas/ReportType"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/all-reports": {"get": {"tags": ["Report"], "parameters": [{"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "jsonReceivers", "in": "query", "schema": {"type": "string"}}, {"name": "reportType", "in": "query", "schema": {"$ref": "#/components/schemas/ReportType"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/all-reports-v2": {"get": {"tags": ["Report"], "parameters": [{"name": "today", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/tests/create-invoice": {"post": {"tags": ["Testing"], "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/tests/create-payment-request": {"post": {"tags": ["Testing"], "parameters": [{"name": "created<PERSON>y", "in": "header", "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateInvoicePaymentV2Request"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateInvoicePaymentV2Request"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateInvoicePaymentV2Request"}}}}, "responses": {"200": {"description": "Success"}}}}, "/tests/create-payment-request-message": {"post": {"tags": ["Testing"], "parameters": [{"name": "type", "in": "query", "schema": {"$ref": "#/components/schemas/PaymentRequestType"}}, {"name": "invoiceId", "in": "query", "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/tests/create-payment-request-old": {"post": {"tags": ["Testing"], "parameters": [{"name": "type", "in": "query", "schema": {"$ref": "#/components/schemas/PaymentRequestType"}}, {"name": "invoiceId", "in": "query", "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/tests/create-factoring-payment-flow": {"post": {"tags": ["Testing"], "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/tests/create-trade-credit-payment-flow": {"post": {"tags": ["Testing"], "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/tests/create-bunch-of-payment-requests": {"post": {"tags": ["Testing"], "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateInvoiceRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/tests/move-payment-request-status/{id}": {"get": {"tags": ["Testing"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "status", "in": "query", "schema": {"$ref": "#/components/schemas/TransactionStatus"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/tests/move-payment-request-status/last-week-payments": {"get": {"tags": ["Testing"], "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/tests/generate-tabapay-report/invoice/{invoiceIdOrNumber}": {"get": {"tags": ["Testing"], "parameters": [{"name": "invoiceIdOrNumber", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/tests/execute-payment-request/{paymentRequestId}": {"get": {"tags": ["Testing"], "parameters": [{"name": "paymentRequestId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/tests/change-payment-subscription": {"get": {"tags": ["Testing"], "parameters": [{"name": "paymentRequestId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "newSubscriptionType", "in": "query", "schema": {"$ref": "#/components/schemas/PaymentSubscriptionType"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/tests/create-ihc-repayment": {"post": {"tags": ["Testing"], "parameters": [{"name": "X-Correlation-Id", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateIhcRepaymentRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateIhcRepaymentRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateIhcRepaymentRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/tests/cancel-loan-auto-collection/{loanId}": {"post": {"tags": ["Testing"], "parameters": [{"name": "loanId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/Transactions/{id}": {"get": {"tags": ["Transactions"], "summary": "Gets a single transactions full details", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PaymentTransactionViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PaymentTransactionViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentTransactionViewModel"}}}}}}, "delete": {"tags": ["Transactions"], "summary": "Cancels a transaction", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "userId", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/Transactions/command/executed/{commandId}": {"put": {"tags": ["Transactions"], "parameters": [{"name": "commandId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "userId", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success"}}}}, "/Transactions/{id}/payment-transaction-history": {"get": {"tags": ["Transactions"], "summary": "Get transaction request history by transaction id", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentTransactionHistoryViewModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentTransactionHistoryViewModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentTransactionHistoryViewModel"}}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}, "/Transactions": {"get": {"tags": ["Transactions"], "summary": "Returns list transactions by various filtering options", "parameters": [{"name": "Id", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "From", "in": "query", "schema": {"type": "string", "format": "date"}}, {"name": "To", "in": "query", "schema": {"type": "string", "format": "date"}}, {"name": "TransactionNumber", "in": "query", "schema": {"type": "string"}}, {"name": "ReferenceNumber", "in": "query", "schema": {"type": "string"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Items", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PaymentTransactionViewModelPaginatedResultViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PaymentTransactionViewModelPaginatedResultViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentTransactionViewModelPaginatedResultViewModel"}}}}}}}, "/Transactions/accountCode": {"get": {"tags": ["Transactions"], "summary": "Get transaction request history by transaction id", "parameters": [{"name": "AccountCodeType", "in": "query", "schema": {"$ref": "#/components/schemas/AccountCodeType"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "X-Correlation-Id", "in": "header"}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionListObjPaginatedResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionListObjPaginatedResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionListObjPaginatedResponse"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}}, "components": {"schemas": {"AccountCodeType": {"enum": ["GL", "FBO", "SERVICE", "REVENUE", "OP", "FUNDING", "TABAPAY", "COLLECTION", "COLLECTIONREPAYMENT", "DISBURSEMENT", "CARDCOLLECTION", "LOCKBOXCOLLECTION", "DACACOLLECTION", "SPV_COLLECTION_ARCADIA", "SPV_COLLECTION_RAISTONE", "SPV_COLLECTION_AION", "SPV_FUNDING_ARCADIA", "SPV_FUNDING_RAISTONE", "SPV_FUNDING_AION"], "type": "string"}, "AccountResponseObj": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "paymentProvider": {"type": "string", "nullable": true}, "accountNumber": {"type": "string", "nullable": true}, "availableBalance": {"type": "number", "format": "double"}, "currentBalance": {"type": "number", "format": "double"}, "amountOnHold": {"type": "number", "format": "double"}, "accountCodeType": {"$ref": "#/components/schemas/AccountCodeType"}}, "additionalProperties": false}, "AccountStatusEnum": {"enum": ["New", "Active", "InCollection", "Inactive", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Closed"], "type": "string"}, "AddressModel": {"type": "object", "properties": {"address": {"type": "string", "nullable": true}, "unitNumber": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "zip": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AionDailyLimitConfig": {"type": "object", "properties": {"wireLimitExceeded": {"type": "boolean"}, "instantLimitExceeded": {"type": "boolean"}, "achPullLimitExceeded": {"type": "boolean"}, "achPushLimitExceeded": {"type": "boolean"}, "lastUpdated": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "ArAdvanceModel": {"type": "object", "properties": {"isEnabled": {"type": "boolean", "nullable": true}, "merchantLimit": {"type": "number", "format": "double", "nullable": true}, "defaultCustomerLimit": {"type": "number", "format": "double", "nullable": true}, "defaultFactoringTerm": {"type": "string", "nullable": true}, "defaultSupplierPackage": {"type": "string", "nullable": true}, "defaultDebtInvestor": {"$ref": "#/components/schemas/DebtInvestorType"}}, "additionalProperties": false}, "AutoApproveType": {"enum": ["AllCustomers", "SelectedCustomers"], "type": "string"}, "AutomatedDrawApprovalModel": {"type": "object", "properties": {"isEnabled": {"type": "boolean", "nullable": true}, "drawLimit": {"type": "number", "format": "double", "nullable": true}, "creditLimitPercentage": {"type": "number", "format": "double", "nullable": true}, "dailyAmountLimit": {"type": "number", "format": "double", "nullable": true}, "weeklyAmountLimit": {"type": "number", "format": "double", "nullable": true}, "lastUpdatedBy": {"type": "string", "nullable": true}, "lastUpdatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "CommandStatus": {"enum": ["Placed", "Executing", "Executed", "Failed", "Canceled", "Pending", "Aborted", "Retried"], "type": "string"}, "CompanyAionSettingsModel": {"type": "object", "properties": {"сounterPartyId": {"type": "string", "nullable": true}, "сounterPartyObjectId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CompanyCreditModel": {"type": "object", "properties": {"loCnumber": {"type": "string", "nullable": true}, "limit": {"type": "number", "format": "double", "nullable": true}, "purchaseType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CompanyInHouseCreditModel": {"type": "object", "properties": {"isAutoPayRequired": {"type": "boolean", "nullable": true}, "isAutoPayEnabledByCompanyUser": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "CompanyModel": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/CompanyTypeEnum"}, "status": {"$ref": "#/components/schemas/CompanyStatusEnum"}, "bankAccounts": {"type": "array", "items": {"type": "string"}, "nullable": true}, "id": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "name": {"type": "string", "nullable": true}, "entity": {"type": "string", "nullable": true}, "legalName": {"type": "string", "nullable": true}, "contactName": {"type": "string", "nullable": true}, "website": {"type": "string", "nullable": true}, "address": {"$ref": "#/components/schemas/AddressModel"}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "isGuest": {"type": "boolean", "nullable": true}, "isBusiness": {"type": "boolean", "nullable": true}, "settings": {"$ref": "#/components/schemas/CompanySettingsModel"}, "credit": {"$ref": "#/components/schemas/CompanyCreditModel"}, "merchantAutomaticPullAllowed": {"type": "boolean"}, "aionSettings": {"$ref": "#/components/schemas/CompanyAionSettingsModel"}, "owner": {"$ref": "#/components/schemas/UserModel"}, "accountStatus": {"$ref": "#/components/schemas/AccountStatusEnum"}, "accountStatusUpdatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isAccountStatusManuallySet": {"type": "boolean", "nullable": true}, "manualStatusBy": {"type": "string", "nullable": true}, "manualStatusNote": {"type": "string", "nullable": true}, "statusEvent": {"type": "string", "nullable": true}, "publicIdentifier": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CompanySettingsModel": {"type": "object", "properties": {"approveRead": {"type": "boolean", "nullable": true}, "onboarded": {"type": "boolean", "nullable": true}, "onboardingType": {"type": "array", "items": {"type": "string"}, "nullable": true}, "invitedBy": {"type": "string", "nullable": true}, "loanPlans": {"type": "array", "items": {"type": "string"}, "nullable": true}, "invoiceLoanPlans": {"type": "array", "items": {"$ref": "#/components/schemas/InvoiceLoanPlanModel"}, "nullable": true}, "businessType": {"type": "string", "nullable": true}, "cardPricingPackageId": {"type": "string", "nullable": true}, "loanPricingPackageId": {"type": "string", "nullable": true}, "acceptAchPayment": {"type": "boolean", "nullable": true}, "achDelayDisabled": {"type": "boolean", "nullable": true}, "achDiscount": {"$ref": "#/components/schemas/ArAdvanceModel"}, "repayment": {"$ref": "#/components/schemas/RepaymentModel"}, "ocrModelId": {"type": "string", "nullable": true}, "arAdvance": {"$ref": "#/components/schemas/ArAdvanceModel"}, "canEditAuthorization": {"type": "boolean", "nullable": true}, "canPostTransactions": {"type": "boolean", "nullable": true}, "canUploadInvoice": {"type": "boolean", "nullable": true}, "dueDay": {"type": "integer", "format": "int32", "nullable": true}, "email": {"$ref": "#/components/schemas/EmailConfigurationModel"}, "sendFinalPaymentWhenLoanIsPaid": {"type": "boolean", "nullable": true}, "supplierCanPay": {"type": "boolean", "nullable": true}, "tutorialViewed": {"type": "boolean", "nullable": true}, "welcomeViewed": {"type": "boolean", "nullable": true}, "automatedDrawApproval": {"$ref": "#/components/schemas/AutomatedDrawApprovalModel"}, "defaultDebtInvestorTradeCredit": {"$ref": "#/components/schemas/DebtInvestorType"}, "depositDetails": {"$ref": "#/components/schemas/DepositDetailsModel"}, "downPaymentDetails": {"$ref": "#/components/schemas/DownPaymentDetailsModel"}, "directTerms": {"$ref": "#/components/schemas/DirectTermsModel"}, "inHouseCredit": {"$ref": "#/components/schemas/CompanyInHouseCreditModel"}}, "additionalProperties": false}, "CompanyStatusEnum": {"enum": ["New", "Applied", "Approved", "Rejected", "Validated"], "type": "string"}, "CompanyTypeEnum": {"enum": ["Supplier", "Contractor"], "type": "string"}, "ConfirmationType": {"enum": ["None", "Manual"], "type": "string"}, "ConnectorMessagePayload": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "operationType": {"$ref": "#/components/schemas/OperationType"}, "amount": {"type": "number", "format": "double"}, "fee": {"type": "number", "format": "double"}, "paymentMethod": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateIhcRepaymentRequest": {"type": "object", "properties": {"loanId": {"type": "string", "format": "uuid"}, "amount": {"type": "number", "format": "double"}}, "additionalProperties": false}, "CreateInvoicePaymentV2Request": {"type": "object", "properties": {"amount": {"type": "number", "format": "double"}, "drawId": {"type": "string", "nullable": true}, "receiver": {"$ref": "#/components/schemas/PaymentRequestReceiverRequest"}}, "additionalProperties": false}, "CreateInvoiceRequest": {"type": "object", "properties": {"supplierEmail": {"type": "string", "nullable": true}, "customerEmail": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double", "nullable": true}, "loanId": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "CreatePaymentRequestFeeViewModel": {"type": "object", "properties": {"amount": {"type": "number", "format": "double"}, "type": {"$ref": "#/components/schemas/FeeType"}, "companyId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "CreatePaymentRequestPayableViewModel": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "payableAmount": {"type": "number", "format": "double", "nullable": true}, "requestedAmount": {"type": "number", "format": "double", "nullable": true}, "payableType": {"$ref": "#/components/schemas/PayableType"}, "discount": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "CreatePaymentRequestTransactionViewModel": {"type": "object", "properties": {"transactionType": {"$ref": "#/components/schemas/PaymentTransactionType"}, "paymentMethod": {"$ref": "#/components/schemas/PaymentMethod"}, "originatorAccountId": {"type": "string", "nullable": true}, "receiverAccountId": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "discount": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "date": {"type": "string", "format": "date"}, "transactionNumber": {"type": "string", "nullable": true}, "referenceNumber": {"type": "string", "nullable": true}, "metaData": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreatePaymentRequestViewModel": {"type": "object", "properties": {"subjectType": {"$ref": "#/components/schemas/SubjectType"}, "requestType": {"$ref": "#/components/schemas/PaymentRequestType"}, "paymentMethod": {"$ref": "#/components/schemas/PaymentMethod"}, "flowTemplateCode": {"type": "string", "nullable": true}, "payerId": {"type": "string", "nullable": true}, "payeeId": {"type": "string", "nullable": true}, "sellerId": {"type": "string", "nullable": true}, "customerAccountId": {"type": "string", "nullable": true}, "creditId": {"type": "string", "format": "uuid", "nullable": true}, "amount": {"type": "number", "format": "double"}, "feeAmount": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "date": {"type": "string", "format": "date"}, "confirmationType": {"$ref": "#/components/schemas/ConfirmationType"}, "paymentRequestPayables": {"type": "array", "items": {"$ref": "#/components/schemas/CreatePaymentRequestPayableViewModel"}, "nullable": true}, "paymentRequestFees": {"type": "array", "items": {"$ref": "#/components/schemas/CreatePaymentRequestFeeViewModel"}, "nullable": true}}, "additionalProperties": false}, "DayOfWeek": {"enum": ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"], "type": "string"}, "DebtInvestorType": {"enum": ["Arcadia", "Raistone", "Aion"], "type": "string"}, "DefaultPaymentConfig": {"type": "object", "properties": {"allowedPaymentMethods": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentMethod"}, "nullable": true}, "paymentMethodPriorities": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DepositDetailsModel": {"type": "object", "properties": {"isSecured": {"type": "boolean"}, "depositAmount": {"type": "number", "format": "double", "nullable": true}, "isDepositPaid": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "DirectTermsModel": {"type": "object", "properties": {"loanPlans": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "DownPaymentDetailsModel": {"type": "object", "properties": {"isRequired": {"type": "boolean"}, "downPaymentPercentage": {"type": "number", "format": "double", "nullable": true}, "expireDays": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "EmailConfigurationModel": {"type": "object", "properties": {"senderEmail": {"type": "string", "nullable": true}, "sendInvitationTemplate": {"type": "string", "nullable": true}, "sendInvoiceTemplate": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ErrorModel": {"type": "object", "properties": {"errorType": {"$ref": "#/components/schemas/ErrorType"}, "code": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}, "errorData": {"nullable": true}}, "additionalProperties": false}, "ErrorType": {"enum": ["<PERSON><PERSON><PERSON>", "ValidationError", "BusinessLogicError", "InternalError", "AionError", "ExternalServicesError"], "type": "string"}, "FeeType": {"enum": ["Purchaser", "Merchant", "Ach<PERSON>arlyPaymentDiscount"], "type": "string"}, "ForbidCompanyRequestViewModel": {"type": "object", "properties": {"companyId": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FundingSource": {"enum": ["Arcadia", "Raistone", "Aion"], "type": "string"}, "InvoiceLoanPlanModel": {"type": "object", "properties": {"minAmount": {"type": "number", "format": "double", "nullable": true}, "maxAmount": {"type": "number", "format": "double", "nullable": true}, "plans": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "MarkPaymentRequestSucceededViewModel": {"type": "object", "properties": {"transactionReferenceNumber": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}}, "additionalProperties": false}, "NotificationMessagePayloadV2": {"type": "object", "properties": {"notificationType": {"$ref": "#/components/schemas/NotificationType"}, "id": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "NotificationType": {"enum": ["None", "TransactionHistoryUpdate", "PaymentRequestCreated"], "type": "string"}, "OperationType": {"enum": ["None", "CustomerCreated", "CustomerUpdated", "QuoteCreated", "QuoteUpdated", "InvoiceCreated", "QuoteProcessed", "InvoicePaid", "InvoiceRejected", "InvoiceUpdated", "InvoiceCancelled", "InvoiceNotified", "InvoiceStatusUpdate", "LoanApplicationApproved", "LoanApplicationRejected", "LoanApplicationCanceled"], "type": "string"}, "PausePaymentRequestViewModel": {"type": "object", "properties": {"isPaused": {"type": "boolean"}, "pauseReason": {"$ref": "#/components/schemas/PaymentPauseReason"}, "pauseComments": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PayableType": {"enum": ["Invoice"], "type": "string"}, "PaymentApprovalRequestViewModel": {"type": "object", "properties": {"paymentMethod": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaymentMethod": {"enum": ["Card", "Ach", "TradeCredit", "SameDayAch", "Instant", "Wire"], "type": "string"}, "PaymentPauseReason": {"enum": ["Other", "InsufficientFunds", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PaymentDispute", "TechnicalIssues", "ForbiddenCompany"], "type": "string"}, "PaymentProvider": {"enum": ["Aion"], "type": "string"}, "PaymentRequestCommandViewModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "stepName": {"type": "string", "nullable": true}, "sequenceNumber": {"type": "integer", "format": "int32"}, "status": {"$ref": "#/components/schemas/CommandStatus"}, "paymentRequestId": {"type": "string", "format": "uuid"}, "transactionId": {"type": "string", "format": "uuid"}, "createdBy": {"type": "string", "nullable": true}, "updatedBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaymentRequestDetailsViewModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "lmsPaymentId": {"type": "string", "format": "uuid", "nullable": true}, "drawId": {"type": "string", "format": "uuid", "nullable": true}, "fundingSource": {"$ref": "#/components/schemas/FundingSource"}, "isPaused": {"type": "boolean", "nullable": true}, "pauseReason": {"$ref": "#/components/schemas/PaymentPauseReason"}, "pauseComments": {"type": "string", "nullable": true}, "pauseCreatedBy": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "paymentRequestId": {"type": "string", "format": "uuid"}, "invoiceNumber": {"type": "string", "description": "User-entered invoice number", "nullable": true}, "reason": {"type": "string", "description": "User-entered reason for payment request", "nullable": true}, "isAutoPayment": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "PaymentRequestFeeViewModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "amount": {"type": "number", "format": "double"}, "type": {"$ref": "#/components/schemas/FeeType"}, "companyId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaymentRequestNotificationViewModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "sender": {"type": "string", "nullable": true}, "recipient": {"type": "string", "nullable": true}, "busMessage": {"type": "string", "nullable": true}, "subject": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/NotificationType"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}, "paymentRequestId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "PaymentRequestPayableViewModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "paymentRequestId": {"type": "string", "format": "uuid"}, "payableId": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "discount": {"type": "number", "format": "double"}}, "additionalProperties": false}, "PaymentRequestReceiverRequest": {"type": "object", "properties": {"companyId": {"type": "string", "nullable": true}, "bankAccountId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaymentRequestStatus": {"enum": ["Requested", "Processing", "Processed", "Settled", "Failed", "Cancelled", "Aborted"], "type": "string"}, "PaymentRequestType": {"enum": ["InvoicePayment", "DrawRepayment", "FinalPayment", "InvoicePaymentV2", "InvoiceDisbursementV2", "FactoringDisbursement", "FactoringFinalPayment", "DrawDisbursement", "FinalPaymentV2", "InvoicePaymentCard", "DrawRepaymentCard", "DrawRepaymentManual", "IhcRepayment", "SubscriptionFeePayment"], "type": "string"}, "PaymentRequestViewModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "amount": {"type": "number", "format": "double"}, "feeAmount": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "flowTemplateCode": {"type": "string", "nullable": true}, "date": {"type": "string", "format": "date"}, "merchantAchDelayInBusinessDays": {"type": "integer", "format": "int32"}, "executeAfter": {"type": "string", "format": "date-time", "nullable": true}, "status": {"$ref": "#/components/schemas/PaymentRequestStatus"}, "subjectType": {"$ref": "#/components/schemas/SubjectType"}, "requestType": {"$ref": "#/components/schemas/PaymentRequestType"}, "paymentSubscription": {"$ref": "#/components/schemas/PaymentSubscriptionType"}, "paymentMethod": {"$ref": "#/components/schemas/PaymentMethod"}, "payerId": {"type": "string", "nullable": true}, "payeeId": {"type": "string", "nullable": true}, "sellerId": {"type": "string", "nullable": true}, "paymentRequestPayables": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestPayableViewModel"}, "nullable": true}, "paymentRequestFees": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestFeeViewModel"}, "nullable": true}, "transactions": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentTransactionViewModel"}, "nullable": true}, "paymentRequestCommands": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestCommandViewModel"}, "nullable": true}, "paymentRequestDetails": {"$ref": "#/components/schemas/PaymentRequestDetailsViewModel"}, "creditId": {"type": "string", "format": "uuid", "nullable": true}, "confirmationType": {"$ref": "#/components/schemas/ConfirmationType"}, "confirmedAt": {"type": "string", "format": "date-time", "nullable": true}, "confirmedBy": {"type": "string", "nullable": true}, "sequenceNumber": {"type": "integer", "description": "Sequence number for custom ordering of disbursement payments.\r\n0 means no custom ordering (uses default priority).\r\nHigher numbers indicate later execution order within the same payment method queue.", "format": "int32"}, "createdBy": {"type": "string", "nullable": true}, "updatedBy": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "PaymentRequestViewModelPaginatedResultViewModel": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32"}, "pagesCount": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestViewModel"}, "nullable": true}}, "additionalProperties": false}, "PaymentSubscriptionType": {"enum": ["SUBSCRIPTION1", "SUBSCRIPTION2", "SUBSCRIPTION3"], "type": "string"}, "PaymentTransactionHistoryViewModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "oldStatus": {"$ref": "#/components/schemas/TransactionStatus"}, "newStatus": {"$ref": "#/components/schemas/TransactionStatus"}, "resultCode": {"type": "string", "nullable": true}, "resultText": {"type": "string", "nullable": true}, "executedAt": {"type": "string", "format": "date-time"}, "transactionId": {"type": "string", "format": "uuid"}, "createdBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaymentTransactionType": {"enum": ["<PERSON><PERSON><PERSON><PERSON>", "AchPull", "AchInternal", "CardPull", "InstantPush", "WirePush"], "type": "string"}, "PaymentTransactionViewModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "sequenceNumber": {"type": "integer", "format": "int32"}, "provider": {"$ref": "#/components/schemas/PaymentProvider"}, "amount": {"type": "number", "format": "double"}, "discount": {"type": "number", "format": "double"}, "processingFees": {"type": "number", "format": "double", "nullable": true}, "currency": {"type": "string", "nullable": true}, "transactionNumber": {"type": "string", "nullable": true}, "publicTransactionNumber": {"type": "string", "nullable": true}, "referenceNumber": {"type": "string", "nullable": true}, "lastResultCode": {"type": "string", "nullable": true}, "metaData": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}, "date": {"type": "string", "format": "date"}, "executedAt": {"type": "string", "format": "date-time"}, "clearedAt": {"type": "string", "format": "date-time", "nullable": true}, "paymentMethod": {"$ref": "#/components/schemas/PaymentMethod"}, "transactionType": {"$ref": "#/components/schemas/PaymentTransactionType"}, "status": {"$ref": "#/components/schemas/TransactionStatus"}, "paymentRequestId": {"type": "string", "format": "uuid"}, "originatorAccountId": {"type": "string", "nullable": true}, "receiverAccountId": {"type": "string", "nullable": true}, "createdBy": {"type": "string", "nullable": true}, "updatedBy": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "PaymentTransactionViewModelPaginatedResultViewModel": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32"}, "pagesCount": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentTransactionViewModel"}, "nullable": true}}, "additionalProperties": false}, "PaymentWindow": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "durationMinutes": {"type": "integer", "format": "int32"}, "startTime": {"$ref": "#/components/schemas/TimeSpan"}, "isEnabled": {"type": "boolean"}, "activeDays": {"type": "array", "items": {"$ref": "#/components/schemas/DayOfWeek"}, "nullable": true}, "allowedPaymentMethods": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentMethod"}, "nullable": true}, "allowedPaymentTypes": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestType"}, "nullable": true}, "priority": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "PaymentWindowConfig": {"type": "object", "properties": {"isPaymentWindowEnabled": {"type": "boolean"}, "allowedPaymentSubscriptions": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentSubscriptionType"}, "nullable": true}, "affectedPaymentTypes": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestType"}, "nullable": true}, "defaultConfig": {"$ref": "#/components/schemas/DefaultPaymentConfig"}, "paymentWindows": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentWindow"}, "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RepaymentModel": {"type": "object", "properties": {"repays": {"$ref": "#/components/schemas/RepaysType"}, "nameOnAccount": {"type": "string", "nullable": true}, "routingNumber": {"type": "string", "nullable": true}, "accountNumber": {"type": "string", "nullable": true}, "autoApprove": {"type": "boolean", "nullable": true}, "paymentPlan": {"type": "string", "nullable": true}, "maxInvoiceAmount": {"type": "number", "format": "double", "nullable": true}, "autoApproveType": {"$ref": "#/components/schemas/AutoApproveType"}}, "additionalProperties": false}, "RepaysType": {"enum": ["Supplier", "<PERSON><PERSON><PERSON>"], "type": "string"}, "ReportType": {"enum": ["AionAchPaymentsSettlement", "AionLocDrawDisbursementsSettlement", "DrawServicingAionSettlement", "CbwLocDrawDisbursementsSettlement", "DrawServicingCbwSettlement", "CardPaymentsSettlement", "ArAdvanceTransactionReport", "ArAdvanceDisbursementsSettlement", "ArAdvanceCustomerPaymentSettlement", "DrawServicingManualPaymentSettlement"], "type": "string"}, "RetryFailedTransactionRequest": {"type": "object", "properties": {"customerBankAccountId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SortOrder": {"enum": ["asc", "desc"], "type": "string"}, "SubjectType": {"enum": ["Payable", "Draw"], "type": "string"}, "TimeSpan": {"type": "object", "properties": {"ticks": {"type": "integer", "format": "int64"}, "days": {"type": "integer", "format": "int32", "readOnly": true}, "hours": {"type": "integer", "format": "int32", "readOnly": true}, "milliseconds": {"type": "integer", "format": "int32", "readOnly": true}, "microseconds": {"type": "integer", "format": "int32", "readOnly": true}, "nanoseconds": {"type": "integer", "format": "int32", "readOnly": true}, "minutes": {"type": "integer", "format": "int32", "readOnly": true}, "seconds": {"type": "integer", "format": "int32", "readOnly": true}, "totalDays": {"type": "number", "format": "double", "readOnly": true}, "totalHours": {"type": "number", "format": "double", "readOnly": true}, "totalMilliseconds": {"type": "number", "format": "double", "readOnly": true}, "totalMicroseconds": {"type": "number", "format": "double", "readOnly": true}, "totalNanoseconds": {"type": "number", "format": "double", "readOnly": true}, "totalMinutes": {"type": "number", "format": "double", "readOnly": true}, "totalSeconds": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "TransactionListObj": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "accountId": {"type": "string", "nullable": true}, "achId": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double", "nullable": true}, "amountStr": {"type": "string", "nullable": true}, "balance": {"type": "number", "format": "double", "nullable": true}, "balanceStr": {"type": "string", "nullable": true}, "txnDate": {"type": "string", "format": "date-time", "nullable": true}, "displayDescription": {"type": "string", "nullable": true}, "transactionId": {"type": "string", "format": "uuid"}, "traceNumber": {"type": "string", "nullable": true}, "transactionType": {"type": "string", "nullable": true}, "providerStatus": {"type": "string", "nullable": true}, "accountNumber": {"type": "string", "nullable": true}, "transactionCode": {"type": "string", "nullable": true}, "rail": {"type": "string", "nullable": true}, "flags": {"type": "array", "items": {"type": "string"}, "nullable": true}, "schedule": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "clearing": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "TransactionListObjPaginatedResponse": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32", "nullable": true}, "offset": {"type": "integer", "format": "int32", "nullable": true}, "count": {"type": "integer", "format": "int32", "nullable": true}, "total": {"type": "integer", "format": "int32", "nullable": true}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionListObj"}, "nullable": true}}, "additionalProperties": false}, "TransactionStatus": {"enum": ["Placed", "Processing", "Processed", "Cleared", "Failed", "Error", "Canceled", "Scheduled", "Recalled", "Aborted", "Hold"], "type": "string"}, "TransactionStatusMessagePayload": {"type": "object", "properties": {"blueTapeTransactionNumber": {"type": "string", "nullable": true}, "externalTransactionNumber": {"type": "string", "nullable": true}, "externalTransactionStatus": {"type": "string", "nullable": true}, "errorCode": {"type": "string", "nullable": true}, "statusMessage": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateSequenceOrderResponseViewModel": {"type": "object", "properties": {"updatedCount": {"type": "integer", "description": "Number of payment requests that were updated", "format": "int32"}, "message": {"type": "string", "description": "Success message", "nullable": true}, "processedPaymentRequestIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "List of payment request IDs that were included in the update (including newly approved ones)", "nullable": true}}, "additionalProperties": false, "description": "Response model for sequence order update operation"}, "UpdateSequenceOrderViewModel": {"required": ["paymentRequestIds"], "type": "object", "properties": {"paymentRequestIds": {"maxItems": 100, "minItems": 1, "type": "array", "items": {"type": "string", "format": "uuid"}, "description": "List of payment request IDs in their desired order"}}, "additionalProperties": false, "description": "Request model for updating the sequence order of payment requests"}, "UserModel": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "sub": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "contactName": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "login": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"ApiKeyAuthScheme": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "API key used in the Authorization header.", "name": "X-API-KEY", "in": "header"}}}, "security": [{"ApiKeyAuthScheme": []}]}