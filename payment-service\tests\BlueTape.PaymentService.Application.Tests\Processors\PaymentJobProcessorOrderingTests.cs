using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;

namespace BlueTape.PaymentService.Application.Tests.Processors;

public class PaymentJobProcessorOrderingTests
{
    private readonly IPaymentWindowService _paymentWindowService = Substitute.For<IPaymentWindowService>();

    [Fact]
    public void PrioritizeCommands_ShouldUseCorrectOrdering_BasedOnCurrentLogic()
    {
        // Arrange
        _paymentWindowService.IsPrefundedFinalPayment(Arg.Any<PaymentRequestCommandEntity>())
            .Returns(false);

        var commands = new List<PaymentRequestCommandEntity>
        {
            CreateCommand(PaymentMethod.Ach, DateTime.UtcNow.AddMinutes(-10), 0), // Default ordering (sequence 0)
            CreateCommand(PaymentMethod.Ach, DateTime.UtcNow.AddMinutes(-5), 1),  // Custom ordering (sequence 1)
            CreateCommand(PaymentMethod.Wire, DateTime.UtcNow.AddMinutes(-15), 2), // Custom ordering (sequence 2)
            CreateCommand(PaymentMethod.Instant, DateTime.UtcNow.AddMinutes(-20), 0) // Default ordering (sequence 0)
        };

        // Act - Simulate the current ordering logic from PaymentJobProcessor.PrioritizeCommands
        var result = commands
            .OrderBy(x => x.PaymentRequest!.SequenceNumber == 0 ? 1 : 0) // Custom ordered items first
            .ThenBy(x => x.PaymentRequest!.SequenceNumber == 0 ? 0 : x.PaymentRequest.SequenceNumber) // Then by sequence number
            .ThenBy(x => GetPaymentMethodPriority(x.PaymentRequest!.PaymentMethod)) // Then by payment method priority
            .ThenBy(x => !_paymentWindowService.IsPrefundedFinalPayment(x)) // Then by prefunded final payment
            .ThenBy(x => x.PaymentRequest!.ConfirmedAt) // Then by confirmation time
            .ThenBy(x => x.PaymentRequest!.CreatedAt) // Finally by creation time
            .ToList();

        // Assert
        // Custom ordered items should come first (sequence 1, then sequence 2)
        Assert.Equal(1, result[0].PaymentRequest!.SequenceNumber);
        Assert.Equal(PaymentMethod.Ach, result[0].PaymentRequest!.PaymentMethod);

        Assert.Equal(2, result[1].PaymentRequest!.SequenceNumber);
        Assert.Equal(PaymentMethod.Wire, result[1].PaymentRequest!.PaymentMethod);

        // Then default ordered items (sequence 0) by payment method priority
        Assert.Equal(0, result[2].PaymentRequest!.SequenceNumber);
        Assert.Equal(PaymentMethod.Instant, result[2].PaymentRequest!.PaymentMethod); // Instant has higher priority

        Assert.Equal(0, result[3].PaymentRequest!.SequenceNumber);
        Assert.Equal(PaymentMethod.Ach, result[3].PaymentRequest!.PaymentMethod);
    }

    [Fact]
    public void PrioritizeCommands_ShouldPrioritizePrefundedFinalPayments()
    {
        // Arrange
        var commands = new List<PaymentRequestCommandEntity>
        {
            CreateCommand(PaymentMethod.Ach, DateTime.UtcNow.AddMinutes(-10), 0),
            CreateCommand(PaymentMethod.Ach, DateTime.UtcNow.AddMinutes(-5), 0)
        };

        // First command is prefunded final payment
        _paymentWindowService.IsPrefundedFinalPayment(commands[0])
            .Returns(true);
        _paymentWindowService.IsPrefundedFinalPayment(commands[1])
            .Returns(false);

        // Act
        var result = commands
            .OrderBy(x => x.PaymentRequest!.SequenceNumber == 0 ? 1 : 0)
            .ThenBy(x => x.PaymentRequest!.SequenceNumber == 0 ? 0 : x.PaymentRequest.SequenceNumber)
            .ThenBy(x => GetPaymentMethodPriority(x.PaymentRequest!.PaymentMethod))
            .ThenBy(x => !_paymentWindowService.IsPrefundedFinalPayment(x)) // Prefunded final payments have priority
            .ThenBy(x => x.PaymentRequest!.ConfirmedAt)
            .ThenBy(x => x.PaymentRequest!.CreatedAt)
            .ToList();

        // Assert
        Assert.True(_paymentWindowService.IsPrefundedFinalPayment(result[0])); // Prefunded final payment first
        Assert.False(_paymentWindowService.IsPrefundedFinalPayment(result[1])); // Regular payment second
    }

    [Fact]
    public void PrioritizeCommands_ShouldHandlePaymentMethodPriority()
    {
        // Arrange
        _paymentWindowService.IsPrefundedFinalPayment(Arg.Any<PaymentRequestCommandEntity>())
            .Returns(false);

        var commands = new List<PaymentRequestCommandEntity>
        {
            CreateCommand(PaymentMethod.Ach, DateTime.UtcNow.AddMinutes(-10), 0),       // Priority 5
            CreateCommand(PaymentMethod.Wire, DateTime.UtcNow.AddMinutes(-5), 0),       // Priority 3
            CreateCommand(PaymentMethod.Instant, DateTime.UtcNow.AddMinutes(-15), 0),   // Priority 3
            CreateCommand(PaymentMethod.SameDayAch, DateTime.UtcNow.AddMinutes(-20), 0) // Priority 4
        };

        // Act
        var result = commands
            .OrderBy(x => x.PaymentRequest!.SequenceNumber == 0 ? 1 : 0)
            .ThenBy(x => x.PaymentRequest!.SequenceNumber == 0 ? 0 : x.PaymentRequest.SequenceNumber)
            .ThenBy(x => GetPaymentMethodPriority(x.PaymentRequest!.PaymentMethod))
            .ThenBy(x => !_paymentWindowService.IsPrefundedFinalPayment(x))
            .ThenBy(x => x.PaymentRequest!.ConfirmedAt)
            .ThenBy(x => x.PaymentRequest!.CreatedAt)
            .ToList();

        // Assert - Instant and Wire should come first (priority 3), then SameDayAch (priority 4), then Ach (priority 5)
        Assert.True(result[0].PaymentRequest!.PaymentMethod == PaymentMethod.Instant || result[0].PaymentRequest!.PaymentMethod == PaymentMethod.Wire);
        Assert.True(result[1].PaymentRequest!.PaymentMethod == PaymentMethod.Instant || result[1].PaymentRequest!.PaymentMethod == PaymentMethod.Wire);
        Assert.Equal(PaymentMethod.SameDayAch, result[2].PaymentRequest!.PaymentMethod);
        Assert.Equal(PaymentMethod.Ach, result[3].PaymentRequest!.PaymentMethod);
    }

    [Fact]
    public void PrioritizeCommands_ShouldOrderByConfirmationTime_ForSameSequenceAndPaymentMethod()
    {
        // Arrange
        _paymentWindowService.IsPrefundedFinalPayment(Arg.Any<PaymentRequestCommandEntity>())
            .Returns(false);

        var olderTime = DateTime.UtcNow.AddMinutes(-10);
        var newerTime = DateTime.UtcNow.AddMinutes(-5);

        var commands = new List<PaymentRequestCommandEntity>
        {
            CreateCommand(PaymentMethod.Ach, newerTime, 0),  // Should be second
            CreateCommand(PaymentMethod.Ach, olderTime, 0)   // Should be first
        };

        // Act
        var result = commands
            .OrderBy(x => x.PaymentRequest!.SequenceNumber == 0 ? 1 : 0)
            .ThenBy(x => x.PaymentRequest!.SequenceNumber == 0 ? 0 : x.PaymentRequest.SequenceNumber)
            .ThenBy(x => GetPaymentMethodPriority(x.PaymentRequest!.PaymentMethod))
            .ThenBy(x => !_paymentWindowService.IsPrefundedFinalPayment(x))
            .ThenBy(x => x.PaymentRequest!.ConfirmedAt)
            .ThenBy(x => x.PaymentRequest!.CreatedAt)
            .ToList();

        // Assert
        Assert.Equal(olderTime, result[0].PaymentRequest!.ConfirmedAt); // Older confirmation first
        Assert.Equal(newerTime, result[1].PaymentRequest!.ConfirmedAt); // Newer confirmation second
    }

    private static PaymentRequestCommandEntity CreateCommand(PaymentMethod paymentMethod, DateTime confirmedAt, int sequenceNumber)
    {
        return new PaymentRequestCommandEntity
        {
            Id = Guid.NewGuid(),
            PaymentRequest = new PaymentRequestEntity
            {
                Id = Guid.NewGuid(),
                PaymentMethod = paymentMethod,
                ConfirmedAt = confirmedAt,
                CreatedAt = confirmedAt.AddMinutes(-1),
                SequenceNumber = sequenceNumber,
                Status = PaymentRequestStatus.Requested
            }
        };
    }

    private static int GetPaymentMethodPriority(PaymentMethod paymentMethod)
    {
        return paymentMethod switch
        {
            PaymentMethod.Instant => 3,
            PaymentMethod.Wire => 3,
            PaymentMethod.SameDayAch => 4,
            PaymentMethod.Ach => 5,
            _ => int.MaxValue
        };
    }
}
