[{"ContainingType": "BlueTape.PaymentService.API.Controllers.AdminController", "Method": "UpdateSequenceOrder", "RelativePath": "admin/paymentRequests/disbursementQueues/sequence", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BlueTape.PaymentService.API.ViewModels.UpdateSequenceOrderViewModel", "IsRequired": true}, {"Name": "updatedBy", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "BlueTape.PaymentService.API.ViewModels.UpdateSequenceOrderResponseViewModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.ReportController", "Method": "GenerateAllReportsForDateRange", "RelativePath": "all-reports", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "endDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "jsonReceivers", "Type": "System.String", "IsRequired": false}, {"Name": "reportType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.ReportController", "Method": "RunAllReports", "RelativePath": "all-reports-v2", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "today", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.ReportController", "Method": "GenerateAllReports", "RelativePath": "all/year/{year}/month/{month}/day/{day}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "year", "Type": "System.Int32", "IsRequired": true}, {"Name": "month", "Type": "System.Int32", "IsRequired": true}, {"Name": "day", "Type": "System.Int32", "IsRequired": true}, {"Name": "reportType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.AionDailyLimitsController", "Method": "GetDailyLimits", "RelativePath": "api/AionDailyLimits/{subscriptionType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "subscriptionType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "BlueTape.PaymentService.Domain.Models.AionDailyLimitConfig", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["Aion Daily Limits Testing"]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.AionDailyLimitsController", "Method": "CheckDailyLimit", "RelativePath": "api/AionDailyLimits/{subscriptionType}/check/{transactionType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "subscriptionType", "Type": "System.String", "IsRequired": true}, {"Name": "transactionType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["Aion Daily Limits Testing"]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.AionDailyLimitsController", "Method": "ResetDailyLimits", "RelativePath": "api/AionDailyLimits/{subscriptionType}/reset", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "subscriptionType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [], "Tags": ["Aion Daily Limits Testing"]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.AionDailyLimitsController", "Method": "SetDailyLimitExceeded", "RelativePath": "api/AionDailyLimits/{subscriptionType}/set/{transactionType}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "subscriptionType", "Type": "System.String", "IsRequired": true}, {"Name": "transactionType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [], "Tags": ["Aion Daily Limits Testing"]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.AionDailyLimitsController", "Method": "GetAllDailyLimits", "RelativePath": "api/AionDailyLimits/get-all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [], "Tags": ["Aion Daily Limits Testing"]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.AionDailyLimitsController", "Method": "ResetAllDailyLimits", "RelativePath": "api/AionDailyLimits/reset-all", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [], "Tags": ["Aion Daily Limits Testing"]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentWindowController", "Method": "GetConfig", "RelativePath": "api/PaymentWindow/config", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "BlueTape.PaymentService.Domain.Models.PaymentWindowConfig", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["Payment Window Configuration"]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentWindowController", "Method": "UpdateConfig", "RelativePath": "api/PaymentWindow/config", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "config", "Type": "BlueTape.PaymentService.Domain.Models.PaymentWindowConfig", "IsRequired": true}], "ReturnTypes": [], "Tags": ["Payment Window Configuration"]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentWindowController", "Method": "UpdateDuration", "RelativePath": "api/PaymentWindow/duration/{durationMinutes}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "durationMinutes", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [], "Tags": ["Payment Window Configuration"]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentWindowController", "Method": "GetNextWindow", "RelativePath": "api/PaymentWindow/next-window", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [], "Tags": ["Payment Window Configuration"]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentWindowController", "Method": "GetStatus", "RelativePath": "api/PaymentWindow/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [], "Tags": ["Payment Window Configuration"]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentWindowController", "Method": "TogglePaymentWindow", "RelativePath": "api/PaymentWindow/toggle", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "enabled", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [], "Tags": ["Payment Window Configuration"]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.CompaniesController", "Method": "GetForbiddenCompanies", "RelativePath": "Companies", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.CompanyService.Companies.CompanyModel, BlueTape.CompanyService, Version=1.3.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.CompaniesController", "Method": "AddCompanyToForbiddenList", "RelativePath": "Companies", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestViewModel", "Type": "BlueTape.PaymentService.API.ViewModels.Company.ForbidCompanyRequestViewModel", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "BlueTape.CompanyService.Companies.CompanyModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.CompaniesController", "Method": "RemoveCompanyFromForbiddenList", "RelativePath": "Companies/{companyId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.CompaniesController", "Method": "IsCompanyPaymentsForbidden", "RelativePath": "Companies/{companyId}/is-company-payments-forbidden", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.EmailNotificationController", "Method": "CustomerHaveToChooseDifferentPaymentMethod_V2", "RelativePath": "customer-have-to-choose-different-payment-method", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "payerId", "Type": "System.String", "IsRequired": false}, {"Name": "merchantName", "Type": "System.String", "IsRequired": false}, {"Name": "paymentRequestId", "Type": "System.Guid", "IsRequired": false}, {"Name": "transactionHistoryId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.HelperController", "Method": "GetErrorCodes", "RelativePath": "errorCodes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.HelperController", "Method": "HealthCheck", "RelativePath": "health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.LoanPaymentsController", "Method": "CreatePlacedOperationByInvoiceId", "RelativePath": "loanPayments/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "invoiceId", "Type": "System.String", "IsRequired": false}, {"Name": "drawId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "amount", "Type": "System.Decimal", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.NotificationsController", "Method": "GetPaymentRequestNotification", "RelativePath": "Notifications", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.PaymentService.API.ViewModels.PaymentRequestNotificationViewModel, BlueTape.PaymentService.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.NotificationsController", "Method": "GetById", "RelativePath": "Notifications/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "BlueTape.PaymentService.API.ViewModels.PaymentRequestNotificationViewModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.NotificationsController", "Method": "GetByPaymentRequestNotification", "RelativePath": "Notifications/payment-request/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.PaymentService.API.ViewModels.PaymentRequestNotificationViewModel, BlueTape.PaymentService.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.OperationsController", "Method": "CreatePlacedOperationByInvoiceId", "RelativePath": "Operations/invoice/{id}/add-placed", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.OperationsController", "Method": "GetById", "RelativePath": "Operations/invoice/{id}/is-operation-exist", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.OperationsController", "Method": "UpdatePlacedOperationByInvoiceId", "RelativePath": "Operations/invoice/{id}/update-placed", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.EmailNotificationController", "Method": "PaymentIsCreated", "RelativePath": "payment-is-created", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentRequestId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentRequestsController", "Method": "Get<PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "PaymentRequests", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DrawId", "Type": "System.String", "IsRequired": false}, {"Name": "Filter", "Type": "System.String", "IsRequired": false}, {"Name": "FlowTemplateCodes", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "From", "Type": "System.Nullable`1[[System.DateOnly, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "To", "Type": "System.Nullable`1[[System.DateOnly, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CustomerId", "Type": "System.String", "IsRequired": false}, {"Name": "SellerId", "Type": "System.String", "IsRequired": false}, {"Name": "PayableId", "Type": "System.String", "IsRequired": false}, {"Name": "PaymentRequestStatuses", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsConfirmed", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SortOrder", "Type": "System.String", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "Page", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Items", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "BlueTape.PaymentService.API.ViewModels.Base.PaginatedResultViewModel`1[[BlueTape.PaymentService.API.ViewModels.PaymentRequestViewModel, BlueTape.PaymentService.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentRequestsController", "Method": "Create", "RelativePath": "PaymentRequests", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createPaymentRequest", "Type": "BlueTape.PaymentService.API.ViewModels.CreatePaymentRequestViewModel", "IsRequired": true}, {"Name": "created<PERSON>y", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "BlueTape.PaymentService.API.ViewModels.PaymentRequestViewModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentRequestsController", "Method": "GetById", "RelativePath": "PaymentRequests/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "BlueTape.PaymentService.API.ViewModels.PaymentRequestViewModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentRequestsController", "Method": "CancelById", "RelativePath": "PaymentRequests/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentRequestsController", "Method": "ApprovePaymentRequest", "RelativePath": "paymentRequests/{id}/approve", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "created<PERSON>y", "Type": "System.String", "IsRequired": false}, {"Name": "requestViewModel", "Type": "BlueTape.PaymentService.API.ViewModels.PaymentApprovalRequestViewModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentRequestsController", "Method": "GetPaymentRequestCommand", "RelativePath": "PaymentRequests/{id}/commands", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[BlueTape.PaymentService.API.ViewModels.PaymentRequestCommandViewModel, BlueTape.PaymentService.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentRequestsController", "Method": "MarkPaymentRequestSucceeded", "RelativePath": "PaymentRequests/{id}/mark-payment-request-succeeded", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "BlueTape.PaymentService.API.ViewModels.MarkPaymentRequestSucceededViewModel", "IsRequired": true}, {"Name": "updatedBy", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "BlueTape.PaymentService.API.ViewModels.PaymentRequestViewModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentRequestsController", "Method": "PausePaymentRequest", "RelativePath": "paymentRequests/{id}/pause", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BlueTape.PaymentService.API.ViewModels.PausePaymentRequestViewModel", "IsRequired": true}, {"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "BlueTape.PaymentService.API.ViewModels.PaymentRequestViewModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentRequestsController", "Method": "RetryTransactionByPaymentRequestId", "RelativePath": "PaymentRequests/{id}/retry", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentRequestsController", "Method": "CreateTransaction", "RelativePath": "PaymentRequests/{id}/transactions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "createTransactionRequest", "Type": "BlueTape.PaymentService.API.ViewModels.CreatePaymentRequestTransactionViewModel", "IsRequired": true}, {"Name": "created<PERSON>y", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentRequestsController", "Method": "ExecuteTransaction", "RelativePath": "PaymentRequests/{id}/transactions/{transactionId}/execute", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "transactionId", "Type": "System.Guid", "IsRequired": true}, {"Name": "updatedBy", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentRequestsController", "Method": "RetryFailedTransaction", "RelativePath": "paymentRequests/{id}/transactions/{transactionId}/retry", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "transactionId", "Type": "System.Guid", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "BlueTape.PaymentService.API.ViewModels.PaymentTransactionViewModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentRequestsController", "Method": "RetryFailedTransaction", "RelativePath": "paymentRequests/{id}/transactions/{transactionId}/retry/pull-from-customer", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "transactionId", "Type": "System.Guid", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}, {"Name": "request", "Type": "BlueTape.PaymentService.API.ViewModels.RetryFailedTransactionRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BlueTape.PaymentService.API.ViewModels.PaymentTransactionViewModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentRequestsController", "Method": "GetAccount", "RelativePath": "PaymentRequests/accounts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentProvider", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Integrations.Aion.Accounts.AccountResponseObj, BlueTape.Integrations.Aion, Version=1.0.20.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.QueuesController", "Method": "GetDisbursementQueuesPaymentRequests", "RelativePath": "paymentRequests/disbursementQueues", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "provider", "Type": "System.String", "IsRequired": false}, {"Name": "subscriptionCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[BlueTape.PaymentService.API.ViewModels.PaymentRequestViewModel, BlueTape.PaymentService.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentRequestsController", "Method": "CancelByDrawId", "RelativePath": "PaymentRequests/draw/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentRequestsController", "Method": "IsPaymentRequestExists", "RelativePath": "paymentRequests/payable/{id}/is-payment-request-exist", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[BlueTape.PaymentService.API.ViewModels.PaymentRequestViewModel, BlueTape.PaymentService.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.PaymentRequestsController", "Method": "GetTotalAmount", "RelativePath": "paymentRequests/total-amount", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "includeProcessingStatus", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "BlueTape.PaymentService.API.ViewModels.PaymentRequestViewModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.QueueEventsController", "Method": "TestCommand", "RelativePath": "QueueEvents/command-event-processor/{commandId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "commandId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.QueueEventsController", "Method": "TriggerPerformOperation", "RelativePath": "QueueEvents/legacy/perform-operation/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.QueueEventsController", "Method": "TriggerOperationSync", "RelativePath": "QueueEvents/legacy/sync-operation/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.QueueEventsController", "Method": "TriggerNotificationConsumer", "RelativePath": "QueueEvents/notification-consumer", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "messagePayload", "Type": "BlueTape.PaymentService.Domain.Messages.NotificationMessagePayloadV2", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.QueueEventsController", "Method": "HandlePaymentRequestMessage", "RelativePath": "QueueEvents/payment-request", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "jsonElement", "Type": "System.Text.Json.JsonElement", "IsRequired": true}], "ReturnTypes": [{"Type": "BlueTape.PaymentService.API.ViewModels.PaymentRequestViewModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.QueueEventsController", "Method": "TriggerPaymentScheduledJob", "RelativePath": "QueueEvents/payment-scheduled-job", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.QueueEventsController", "Method": "TriggerTransactionStatusUpdateJob", "RelativePath": "QueueEvents/transaction-status-update-job", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "messagePayload", "Type": "BlueTape.PaymentService.Domain.Messages.TransactionStatusMessagePayload", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.QueueEventsController", "Method": "GetById", "RelativePath": "QueueEvents/transaction-status-update-scheduled-job", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.HelperController", "Method": "SendMessage", "RelativePath": "sendConnectorMessage", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BlueTape.PaymentService.Domain.Messages.ConnectorMessagePayload", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TestingController", "Method": "CancelLoanAutoCollection", "RelativePath": "tests/cancel-loan-auto-collection/{loanId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loanId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TestingController", "Method": "ChangePaymentSubscription", "RelativePath": "tests/change-payment-subscription", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentRequestId", "Type": "System.Guid", "IsRequired": false}, {"Name": "newSubscriptionType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TestingController", "Method": "CreateBunchOfPaymentRequest", "RelativePath": "tests/create-bunch-of-payment-requests", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BlueTape.PaymentService.API.ViewModels.CreateInvoiceRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TestingController", "Method": "CreateFactoringFlow", "RelativePath": "tests/create-factoring-payment-flow", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BlueTape.PaymentService.API.ViewModels.CreateInvoiceRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TestingController", "Method": "CreateIhcRepayment", "RelativePath": "tests/create-ihc-repayment", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BlueTape.PaymentService.API.ViewModels.CreateIhcRepaymentRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TestingController", "Method": "CreateInvoice", "RelativePath": "tests/create-invoice", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BlueTape.PaymentService.API.ViewModels.CreateInvoiceRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TestingController", "Method": "CreatePaymentRequest", "RelativePath": "tests/create-payment-request", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BlueTape.PaymentService.TestUtilities.Models.CreateInvoicePaymentV2Request", "IsRequired": true}, {"Name": "created<PERSON>y", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TestingController", "Method": "CreatePaymentRequestMessage", "RelativePath": "tests/create-payment-request-message", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BlueTape.PaymentService.API.ViewModels.CreateInvoiceRequest", "IsRequired": true}, {"Name": "type", "Type": "System.String", "IsRequired": false}, {"Name": "invoiceId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TestingController", "Method": "CreatePaymentRequest", "RelativePath": "tests/create-payment-request-old", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BlueTape.PaymentService.API.ViewModels.CreateInvoiceRequest", "IsRequired": true}, {"Name": "type", "Type": "System.String", "IsRequired": false}, {"Name": "invoiceId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TestingController", "Method": "CreateTradeCreditFlow", "RelativePath": "tests/create-trade-credit-payment-flow", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BlueTape.PaymentService.API.ViewModels.CreateInvoiceRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TestingController", "Method": "ExecutePaymentRequest", "RelativePath": "tests/execute-payment-request/{paymentRequestId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentRequestId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TestingController", "Method": "GenerateTabapayReport", "RelativePath": "tests/generate-tabapay-report/invoice/{invoiceIdOrNumber}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "invoiceIdOrNumber", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TestingController", "Method": "MoveStatus", "RelativePath": "tests/move-payment-request-status/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "status", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TestingController", "Method": "MoveBunchOfStatuses", "RelativePath": "tests/move-payment-request-status/last-week-payments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TransactionsController", "Method": "Get<PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "Transactions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "From", "Type": "System.Nullable`1[[System.DateOnly, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "To", "Type": "System.Nullable`1[[System.DateOnly, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "TransactionNumber", "Type": "System.String", "IsRequired": false}, {"Name": "ReferenceNumber", "Type": "System.String", "IsRequired": false}, {"Name": "Page", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Items", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "BlueTape.PaymentService.API.ViewModels.Base.PaginatedResultViewModel`1[[BlueTape.PaymentService.API.ViewModels.PaymentTransactionViewModel, BlueTape.PaymentService.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TransactionsController", "Method": "GetById", "RelativePath": "Transactions/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "BlueTape.PaymentService.API.ViewModels.PaymentTransactionViewModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TransactionsController", "Method": "CancelById", "RelativePath": "Transactions/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TransactionsController", "Method": "GetTransactionExecutionHistory", "RelativePath": "Transactions/{id}/payment-transaction-history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[BlueTape.PaymentService.API.ViewModels.PaymentTransactionHistoryViewModel, BlueTape.PaymentService.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.HelperController", "Method": "GeneratePublicTransactionNumber", "RelativePath": "transactions/{id}/public-transaction-number", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "generatedBy", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TransactionsController", "Method": "GetTransactionByAccountCode", "RelativePath": "Transactions/accountCode", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "AccountCodeType", "Type": "System.String", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "BlueTape.Utilities.Models.PaginatedResponse`1[[BlueTape.Integrations.Aion.Transactions.TransactionListObj, BlueTape.Integrations.Aion, Version=1.0.20.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "BlueTape.Common.ExceptionHandling.Models.ErrorModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BlueTape.PaymentService.API.Controllers.TransactionsController", "Method": "MarkCommandAsExecuted", "RelativePath": "Transactions/command/executed/{commandId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "commandId", "Type": "System.Guid", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}]