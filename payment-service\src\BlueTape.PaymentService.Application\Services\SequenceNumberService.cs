using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;

namespace BlueTape.PaymentService.Application.Services;

/// <summary>
/// Service for managing sequence numbers for custom payment ordering
/// </summary>
public class SequenceNumberService(
    IPaymentRequestRepository paymentRequestRepository,
    IUnitOfWork unitOfWork,
    IDateProvider dateProvider,
    ILogger<SequenceNumberService> logger) : ISequenceNumberService
{

    private readonly int _maxSequenceNumber = 100; // Maximum sequence number for custom ordering

    public async Task<int> UpdateSequenceNumbers(List<Guid> paymentRequestIds, string updatedBy, CancellationToken ct)
    {
        if (!paymentRequestIds.Any())
        {
            return 0;
        }

        try
        {
            // Validate that all payment requests can be reordered together
            var canReorder = await ValidateReorderingRestrictions(paymentRequestIds, ct);
            if (!canReorder)
            {
                throw new InvalidOperationException("Payment requests cannot be reordered together - they belong to different queues");
            }

            if (paymentRequestIds.Count > _maxSequenceNumber)
            {
                throw new InvalidOperationException($"Cannot reorder more than {_maxSequenceNumber} payments at once");
            }

            using var transaction = await unitOfWork.BeginTransactionAsync(ct);
            try
            {
                // Get all payment requests that user wants to reorder
                var paymentRequests = await paymentRequestRepository.GetByIds(paymentRequestIds, ct);

                // Only assign sequence numbers to the payments that user explicitly reordered
                var updatedCount = 0;
                for (int i = 0; i < paymentRequestIds.Count; i++)
                {
                    var paymentId = paymentRequestIds[i];
                    var payment = paymentRequests.FirstOrDefault(p => p.Id == paymentId);

                    if (payment != null)
                    {
                        payment.SequenceNumber = i + 1; // Start from 1
                        payment.UpdatedAt = dateProvider.CurrentDateTime;
                        payment.UpdatedBy = updatedBy;

                        await paymentRequestRepository.Update(payment, ct);
                        updatedCount++;
                    }
                }

                // Newly approved payments keep SequenceNumber = 0 (default ordering)
                // They will appear after custom ordered items in the queue

                await transaction.CommitAsync(ct);

                logger.LogInformation("Updated sequence numbers for {Count} manually reordered payment requests by {UpdatedBy}.",
                    updatedCount, updatedBy);

                return updatedCount;
            }
            catch
            {
                await transaction.RollbackAsync(ct);
                throw;
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating sequence numbers for payment requests");
            throw;
        }
    }

    public async Task<bool> ValidateReorderingRestrictions(List<Guid> paymentRequestIds, CancellationToken ct)
    {
        if (!paymentRequestIds.Any())
        {
            return true;
        }

        try
        {
            var paymentRequests = await paymentRequestRepository.GetByIds(paymentRequestIds, ct);

            if (paymentRequests.Count != paymentRequestIds.Count)
            {
                logger.LogWarning("Some payment requests not found during validation");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error validating reordering restrictions");
            return false;
        }
    }

    public async Task<int> ResetAllSequenceNumbers(string updatedBy, CancellationToken ct)
    {
        try
        {
            using var transaction = await unitOfWork.BeginTransactionAsync(ct);
            try
            {
                var updatedCount = await paymentRequestRepository.ResetAllSequenceNumbers(updatedBy, dateProvider.CurrentDateTime, ct);
                await transaction.CommitAsync(ct);

                logger.LogInformation("Reset sequence numbers for {Count} payment requests by {UpdatedBy}",
                    updatedCount, updatedBy);

                return updatedCount;
            }
            catch
            {
                await transaction.RollbackAsync(ct);
                throw;
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error resetting sequence numbers");
            throw;
        }
    }
}
