﻿using BlueTape.CompanyClient.Abstractions;
using BlueTape.CompanyService.Companies;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.PaymentService.Application.Abstractions.Processors;
using BlueTape.PaymentService.Application.Abstractions.Senders;
using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.Application.Abstractions.Services.External;
using BlueTape.PaymentService.Application.Constants;
using BlueTape.PaymentService.Application.Processors;
using BlueTape.PaymentService.Application.Tests.Constants;
using BlueTape.PaymentService.Application.Tests.Models;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Messages;
using BlueTape.PaymentService.Domain.Models;
using BlueTape.PaymentService.PaymentFlowTemplatesEngine.Abstractions;
using BlueTape.PaymentService.UnitOfWork.Abstractions;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using TinyHelpers.Extensions;
using IPaymentRequestCommandRepository = BlueTape.PaymentService.DataAccess.Abstractions.Repositories.IPaymentRequestCommandRepository;

namespace BlueTape.PaymentService.Application.Tests.Processors;

public class PaymentJobProcessorTests
{
    private readonly PaymentJobProcessor _paymentJobProcessor;

    private readonly IPaymentRequestCommandRepository _paymentExecutionHistoryRepositoryMock = Substitute.For<IPaymentRequestCommandRepository>();
    private readonly IUnitOfWork _unitOfWorkMock = Substitute.For<IUnitOfWork>();
    private readonly IPaymentFlowTemplatesEngine _paymentFlowTemplatesEngineMock = Substitute.For<IPaymentFlowTemplatesEngine>();
    private readonly IPaymentFlowServiceMessageSender _messageSenderMock = Substitute.For<IPaymentFlowServiceMessageSender>();
    private readonly IFailedCommandsProcessor _failedCommandsProcessor = Substitute.For<IFailedCommandsProcessor>();
    private readonly ICompanyHttpClient _companyHttpClient = Substitute.For<ICompanyHttpClient>();
    private readonly IAionServiceV2 _aionService = Substitute.For<IAionServiceV2>();
    private readonly IPaymentConfigService _paymentConfigService = Substitute.For<IPaymentConfigService>();
    private readonly IPaymentWindowService _paymentWindowService = Substitute.For<IPaymentWindowService>();
    private readonly IDateProvider _dateProvider = Substitute.For<IDateProvider>();
    private readonly ILogger<PaymentJobProcessor> _logger = Substitute.For<ILogger<PaymentJobProcessor>>();

    public PaymentJobProcessorTests()
    {
        _paymentJobProcessor = new PaymentJobProcessor(
            _paymentExecutionHistoryRepositoryMock,
            _unitOfWorkMock,
            _paymentFlowTemplatesEngineMock,
            _messageSenderMock,
            _failedCommandsProcessor,
            _companyHttpClient,
            _aionService,
            _paymentConfigService,
            _paymentWindowService,
            _dateProvider,
            _logger);
    }

    [Fact]
    public async Task Process_NoCommandsFound_NoDatabaseCallsPerformed()
    {
        // Setup mocks for new dependencies
        _dateProvider.CurrentDateTime.Returns(DateTime.Today.AddHours(10)); // Not midnight, so no daily limit reset
        _paymentConfigService.GetAllAionDailyLimitConfigs(default).Returns(new Dictionary<PaymentSubscriptionType, AionDailyLimitConfig>());
        _paymentWindowService.FilterCommandsByPaymentWindow(Arg.Any<List<PaymentRequestCommandEntity>>(), default)
            .Returns(callInfo => callInfo.Arg<List<PaymentRequestCommandEntity>>());
        _paymentWindowService.IsPaymentWindowActive(default).Returns(false);
        _paymentWindowService.GetPaymentWindowConfig(default).Returns(new PaymentWindowConfig());
        _paymentWindowService.GetNextPaymentWindowStartTime(default).Returns(DateTime.UtcNow.AddHours(1));

        _paymentExecutionHistoryRepositoryMock.GetItemsGroupedByPaymentRequestId(default).Returns(new List<IGrouping<Guid, PaymentRequestCommandEntity>>());
        _unitOfWorkMock.GetMostRecentNotEnoughBalanceHistoryItemsByTransactionIds(Arg.Any<IEnumerable<Guid>>(), default)
            .Returns(new List<PaymentTransactionHistoryEntity?>());
        _paymentFlowTemplatesEngineMock.GetStepNamesWithWaitForEligibleBalanceCondition()
            .Returns([StepName.PushToMerchantAdvanceAmount.ToString()]);

        await _paymentJobProcessor.Process(default);

        await _paymentExecutionHistoryRepositoryMock.Received(TimesCalled.Once).GetItemsGroupedByPaymentRequestId(default);
        await _failedCommandsProcessor.Received(TimesCalled.Once).ProcessFailedCommands(Arg.Is<IEnumerable<IGrouping<Guid, PaymentRequestCommandEntity>>>(x => x.IsEmpty()));
        _paymentFlowTemplatesEngineMock.Received(TimesCalled.Never).IsCommandSatisfiesExecutionConditions(Arg.Any<PaymentRequestCommandEntity>(), Arg.Any<IEnumerable<PaymentRequestCommandEntity>>());
        await _messageSenderMock.Received(TimesCalled.Never).SendMessages(Arg.Any<IEnumerable<ServiceBusMessageBt<PaymentJobMessagePayload>>>(), ct: default);
    }

    [Fact]
    public async Task Process_FailedCommandsTransactionsFound_NoDatabaseCallsPerformed()
    {
        // Setup mocks for new dependencies
        _dateProvider.CurrentDateTime.Returns(DateTime.Today.AddHours(10)); // Not midnight, so no daily limit reset
        _paymentConfigService.GetAllAionDailyLimitConfigs(default).Returns(new Dictionary<PaymentSubscriptionType, AionDailyLimitConfig>());
        _paymentWindowService.FilterCommandsByPaymentWindow(Arg.Any<List<PaymentRequestCommandEntity>>(), default)
            .Returns(callInfo => callInfo.Arg<List<PaymentRequestCommandEntity>>());
        _paymentWindowService.IsPaymentWindowActive(default).Returns(false);
        _paymentWindowService.GetPaymentWindowConfig(default).Returns(new PaymentWindowConfig());
        _paymentWindowService.GetNextPaymentWindowStartTime(default).Returns(DateTime.UtcNow.AddHours(1));

        var groupings = PaymentRequestGroupings.FailedCommandsGroupings;
        _paymentExecutionHistoryRepositoryMock.GetItemsGroupedByPaymentRequestId(default).Returns(groupings);
        _unitOfWorkMock.GetMostRecentNotEnoughBalanceHistoryItemsByTransactionIds(Arg.Any<IEnumerable<Guid>>(), default)
            .Returns(new List<PaymentTransactionHistoryEntity?>());
        _paymentFlowTemplatesEngineMock.GetStepNamesWithWaitForEligibleBalanceCondition()
            .Returns([StepName.PushToMerchantAdvanceAmount.ToString()]);

        var failedTransactionsJobApplicableGroupingsIds = groupings.Take(3).Select(x => x.Key);

        await _paymentJobProcessor.Process(default);

        await _failedCommandsProcessor.Received(TimesCalled.Once).ProcessFailedCommands(Arg.Is<IEnumerable<IGrouping<Guid, PaymentRequestCommandEntity>>>(x =>
            x.Select(y => y.Key).SequenceEqual(failedTransactionsJobApplicableGroupingsIds)));
    }


    [Fact]
    public async Task Process_PlacedCommandsFound_ChangesStatusToPendingAndSendsToQueue()
    {
        // Setup mocks for new dependencies
        _dateProvider.CurrentDateTime.Returns(DateTime.Today.AddHours(10)); // Not midnight, so no daily limit reset
        _paymentConfigService.GetAllAionDailyLimitConfigs(default).Returns(new Dictionary<PaymentSubscriptionType, AionDailyLimitConfig>());
        _paymentWindowService.FilterCommandsByPaymentWindow(Arg.Any<List<PaymentRequestCommandEntity>>(), default)
            .Returns(callInfo => callInfo.Arg<List<PaymentRequestCommandEntity>>());
        _paymentWindowService.IsPrefundedFinalPayment(Arg.Any<PaymentRequestCommandEntity>()).Returns(false);
        _paymentWindowService.IsPaymentWindowActive(default).Returns(false);
        _paymentWindowService.GetPaymentWindowConfig(default).Returns(new PaymentWindowConfig());
        _paymentWindowService.GetNextPaymentWindowStartTime(default).Returns(DateTime.UtcNow.AddHours(1));

        var commands = PaymentRequestGroupings.PlacedPaymentRequestCommandGroup;
        var transactions = commands.SelectMany(x => x).Select(x => x.Transaction).ToList();
        _paymentExecutionHistoryRepositoryMock.GetItemsGroupedByPaymentRequestId(default).Returns(commands);
        _paymentFlowTemplatesEngineMock.IsCommandSatisfiesExecutionConditions(Arg.Any<PaymentRequestCommandEntity>(), Arg.Any<IEnumerable<PaymentRequestCommandEntity>>()).Returns(true);
        _unitOfWorkMock.GetMostRecentNotEnoughBalanceHistoryItemsByTransactionIds(Arg.Any<IEnumerable<Guid>>(), default)
            .Returns(new List<PaymentTransactionHistoryEntity?>());
        _paymentFlowTemplatesEngineMock.GetStepNamesWithWaitForEligibleBalanceCondition()
            .Returns([StepName.PushToMerchantAdvanceAmount.ToString()]);

        await _paymentJobProcessor.Process(default);

        var paymentRequestCommands = commands.SelectMany(x => x.OrderBy(t => t.Transaction!.SequenceNumber));
        var commandsIds = paymentRequestCommands.Select(x => x.Id);
        paymentRequestCommands.ShouldAllBe(x => x.Status == CommandStatus.Pending && x.UpdatedBy == ApplicationConstants.PaymentJob);

        _paymentFlowTemplatesEngineMock.Received(transactions.Count).IsCommandSatisfiesExecutionConditions(Arg.Any<PaymentRequestCommandEntity>(), Arg.Any<IEnumerable<PaymentRequestCommandEntity>>());
        await _paymentExecutionHistoryRepositoryMock.Received(TimesCalled.Once).GetItemsGroupedByPaymentRequestId(default);
        await _unitOfWorkMock.PaymentRequestCommandRepository.Received(TimesCalled.Once)
            .UpdateRange(Arg.Any<IEnumerable<PaymentRequestCommandEntity>>(), default);
        await _unitOfWorkMock.Received(TimesCalled.Once).SaveAsync(default);
        await _messageSenderMock.Received(TimesCalled.Once).SendMessages(Arg.Is<IEnumerable<ServiceBusMessageBt<PaymentJobMessagePayload>>>
            (y => commandsIds.All(id => y.Select(p => p.MessageBody.ExecutableCommandId).Contains(id))), ct: default);
    }


    [Fact]
    public async Task Process_NotEnoughBalanceHistoryItemsFound_DoesNotSendItToTheQueue()
    {
        // Setup mocks for new dependencies
        _dateProvider.CurrentDateTime.Returns(DateTime.Today.AddHours(10)); // Not midnight, so no daily limit reset
        _paymentConfigService.GetAllAionDailyLimitConfigs(default).Returns(new Dictionary<PaymentSubscriptionType, AionDailyLimitConfig>());
        _paymentWindowService.FilterCommandsByPaymentWindow(Arg.Any<List<PaymentRequestCommandEntity>>(), default)
            .Returns(callInfo => callInfo.Arg<List<PaymentRequestCommandEntity>>());
        _paymentWindowService.IsPrefundedFinalPayment(Arg.Any<PaymentRequestCommandEntity>()).Returns(false);
        _paymentWindowService.IsPaymentWindowActive(default).Returns(false);
        _paymentWindowService.GetPaymentWindowConfig(default).Returns(new PaymentWindowConfig());
        _paymentWindowService.GetNextPaymentWindowStartTime(default).Returns(DateTime.UtcNow.AddHours(1));

        var commands = PaymentRequestGroupings.PlacedPaymentRequestCommandGroup;
        var transactions = commands.SelectMany(x => x).Select(x => x.Transaction).ToList();
        var transactionWithNotEnoughBalance = transactions.First();
        var commandWithNotEnoughBalance = commands.SelectMany(x => x).First(x => x.Transaction!.Id == transactionWithNotEnoughBalance!.Id);
        commandWithNotEnoughBalance.TransactionId = transactionWithNotEnoughBalance!.Id;
        _paymentExecutionHistoryRepositoryMock.GetItemsGroupedByPaymentRequestId(default).Returns(commands);
        _paymentFlowTemplatesEngineMock.IsCommandSatisfiesExecutionConditions(Arg.Any<PaymentRequestCommandEntity>(), Arg.Any<IEnumerable<PaymentRequestCommandEntity>>()).Returns(true);
        _unitOfWorkMock.GetMostRecentNotEnoughBalanceHistoryItemsByTransactionIds(Arg.Any<IEnumerable<Guid>>(), default)
            .Returns([new PaymentTransactionHistoryEntity { TransactionId = transactionWithNotEnoughBalance!.Id, CreatedAt = DateTime.UtcNow }]);
        _paymentFlowTemplatesEngineMock.GetStepNamesWithWaitForEligibleBalanceCondition()
            .Returns([StepName.PushToMerchantAdvanceAmount.ToString()]);

        await _paymentJobProcessor.Process(default);

        var paymentRequestCommands = commands.SelectMany(x => x.OrderBy(t => t.Transaction!.SequenceNumber));
        var commandsIds = paymentRequestCommands.Select(x => x.Id).Where(x => x != commandWithNotEnoughBalance.Id);
        paymentRequestCommands.Where(x => x.Id != commandWithNotEnoughBalance.Id)
            .ShouldAllBe(x => x.Status == CommandStatus.Pending && x.UpdatedBy == ApplicationConstants.PaymentJob);

        _paymentFlowTemplatesEngineMock.Received(transactions.Count).IsCommandSatisfiesExecutionConditions(Arg.Any<PaymentRequestCommandEntity>(), Arg.Any<IEnumerable<PaymentRequestCommandEntity>>());
        await _paymentExecutionHistoryRepositoryMock.Received(TimesCalled.Once).GetItemsGroupedByPaymentRequestId(default);
        await _unitOfWorkMock.PaymentRequestCommandRepository.Received(TimesCalled.Once)
            .UpdateRange(Arg.Any<IEnumerable<PaymentRequestCommandEntity>>(), default);
        await _unitOfWorkMock.Received(TimesCalled.Once).SaveAsync(default);
        await _messageSenderMock.Received(TimesCalled.Once).SendMessages(Arg.Is<IEnumerable<ServiceBusMessageBt<PaymentJobMessagePayload>>>
            (y => y.Select(p => p.MessageBody.ExecutableCommandId).SequenceEqual(commandsIds)), ct: default);
    }

    [Fact]
    public async Task Process_FoundCommandInExecutingStatus_DoesNotSendAnyMessagesToQueue()
    {
        // Setup mocks for new dependencies
        _dateProvider.CurrentDateTime.Returns(DateTime.Today.AddHours(10)); // Not midnight, so no daily limit reset
        _paymentConfigService.GetAllAionDailyLimitConfigs(default).Returns(new Dictionary<PaymentSubscriptionType, AionDailyLimitConfig>());
        _paymentWindowService.FilterCommandsByPaymentWindow(Arg.Any<List<PaymentRequestCommandEntity>>(), default)
            .Returns(callInfo => callInfo.Arg<List<PaymentRequestCommandEntity>>());
        _paymentWindowService.IsPrefundedFinalPayment(Arg.Any<PaymentRequestCommandEntity>()).Returns(false);
        _paymentWindowService.IsPaymentWindowActive(default).Returns(false);
        _paymentWindowService.GetPaymentWindowConfig(default).Returns(new PaymentWindowConfig());
        _paymentWindowService.GetNextPaymentWindowStartTime(default).Returns(DateTime.UtcNow.AddHours(1));

        var commands = PaymentRequestGroupings.ExecutingRequestHistoryGroup;
        _paymentExecutionHistoryRepositoryMock.GetItemsGroupedByPaymentRequestId(default).Returns(commands);
        _paymentFlowTemplatesEngineMock.IsCommandSatisfiesExecutionConditions(Arg.Any<PaymentRequestCommandEntity>(), Arg.Any<IEnumerable<PaymentRequestCommandEntity>>()).Returns(true);
        _unitOfWorkMock.GetMostRecentNotEnoughBalanceHistoryItemsByTransactionIds(Arg.Any<IEnumerable<Guid>>(), default)
            .Returns(new List<PaymentTransactionHistoryEntity?>());
        _paymentFlowTemplatesEngineMock.GetStepNamesWithWaitForEligibleBalanceCondition()
            .Returns([StepName.PushToMerchantAdvanceAmount.ToString()]);

        await _paymentJobProcessor.Process(default);

        await _messageSenderMock.Received(TimesCalled.Never).SendMessages(Arg.Any<IEnumerable<ServiceBusMessageBt<PaymentJobMessagePayload>>>(), ct: default);
    }

    [Fact]
    public async Task Process_HasCommandsInExecutedStatus_SendsOnlyPlacedCommandsToQueue()
    {
        // Setup mocks for new dependencies
        _dateProvider.CurrentDateTime.Returns(DateTime.Today.AddHours(10)); // Not midnight, so no daily limit reset
        _paymentConfigService.GetAllAionDailyLimitConfigs(default).Returns(new Dictionary<PaymentSubscriptionType, AionDailyLimitConfig>());
        _paymentWindowService.FilterCommandsByPaymentWindow(Arg.Any<List<PaymentRequestCommandEntity>>(), default)
            .Returns(callInfo => callInfo.Arg<List<PaymentRequestCommandEntity>>());
        _paymentWindowService.IsPrefundedFinalPayment(Arg.Any<PaymentRequestCommandEntity>()).Returns(false);
        _paymentWindowService.IsPaymentWindowActive(default).Returns(false);
        _paymentWindowService.GetPaymentWindowConfig(default).Returns(new PaymentWindowConfig());
        _paymentWindowService.GetNextPaymentWindowStartTime(default).Returns(DateTime.UtcNow.AddHours(1));

        var commands = PaymentRequestGroupings.PartiallyExecutedRequestHistoryGroup;
        var commandsIds = commands.SelectMany(x => x.OrderBy(t => t.Transaction!.SequenceNumber).Where(x => x.Transaction?.Status == TransactionStatus.Placed).Select(y => y.Id));
        _paymentExecutionHistoryRepositoryMock.GetItemsGroupedByPaymentRequestId(default).Returns(commands);
        _paymentFlowTemplatesEngineMock.IsCommandSatisfiesExecutionConditions(Arg.Any<PaymentRequestCommandEntity>(), Arg.Any<IEnumerable<PaymentRequestCommandEntity>>()).Returns(true);
        _unitOfWorkMock.GetMostRecentNotEnoughBalanceHistoryItemsByTransactionIds(Arg.Any<IEnumerable<Guid>>(), default)
            .Returns(new List<PaymentTransactionHistoryEntity?>());
        _paymentFlowTemplatesEngineMock.GetStepNamesWithWaitForEligibleBalanceCondition()
            .Returns([StepName.PushToMerchantAdvanceAmount.ToString()]);

        await _paymentJobProcessor.Process(default);

        _paymentFlowTemplatesEngineMock.Received(TimesCalled.Once).IsCommandSatisfiesExecutionConditions(Arg.Any<PaymentRequestCommandEntity>(), Arg.Any<IEnumerable<PaymentRequestCommandEntity>>());
        await _unitOfWorkMock.PaymentRequestCommandRepository.Received(TimesCalled.Once)
            .UpdateRange(Arg.Any<IEnumerable<PaymentRequestCommandEntity>>(), default);
        await _unitOfWorkMock.Received(TimesCalled.Once).SaveAsync(default);
        await _messageSenderMock.Received(TimesCalled.Once).SendMessages(Arg.Is<IEnumerable<ServiceBusMessageBt<PaymentJobMessagePayload>>>
            (y => y.Select(p => p.MessageBody.ExecutableCommandId).SequenceEqual(commandsIds)), ct: default);
    }

    [Fact]
    public async Task Process_RollBackCommands_HasPullFromMerchant_InDisabledStatus()
    {
        // Setup mocks for new dependencies
        _dateProvider.CurrentDateTime.Returns(DateTime.Today.AddHours(10)); // Not midnight, so no daily limit reset
        _paymentConfigService.GetAllAionDailyLimitConfigs(default).Returns(new Dictionary<PaymentSubscriptionType, AionDailyLimitConfig>());
        _paymentWindowService.FilterCommandsByPaymentWindow(Arg.Any<List<PaymentRequestCommandEntity>>(), default)
            .Returns(callInfo => callInfo.Arg<List<PaymentRequestCommandEntity>>());
        _paymentWindowService.IsPrefundedFinalPayment(Arg.Any<PaymentRequestCommandEntity>()).Returns(false);
        _paymentWindowService.IsPaymentWindowActive(default).Returns(false);
        _paymentWindowService.GetPaymentWindowConfig(default).Returns(new PaymentWindowConfig());
        _paymentWindowService.GetNextPaymentWindowStartTime(default).Returns(DateTime.UtcNow.AddHours(1));

        var commands = PaymentRequestGroupings.RollBackExecutedPullFromMerchantExist;
        var commandsIds = commands.SelectMany(x =>
            x.OrderBy(t => t.Transaction!.SequenceNumber)
                .Where(x => x.Transaction?.Status == TransactionStatus.Placed)
                .Select(y => y.Id));
        _paymentExecutionHistoryRepositoryMock.GetItemsGroupedByPaymentRequestId(default).Returns(commands);
        _paymentFlowTemplatesEngineMock.IsCommandSatisfiesExecutionConditions(Arg.Any<PaymentRequestCommandEntity>(),
            Arg.Any<IEnumerable<PaymentRequestCommandEntity>>()).Returns(true);
        _unitOfWorkMock.GetMostRecentNotEnoughBalanceHistoryItemsByTransactionIds(Arg.Any<IEnumerable<Guid>>(), default)
            .Returns(new List<PaymentTransactionHistoryEntity?>());

        _companyHttpClient.GetCompaniesByIdsAsync(Arg.Any<string[]>(), default).Returns(new List<CompanyModel>());
        _paymentFlowTemplatesEngineMock.GetStepNamesWithWaitForEligibleBalanceCondition()
            .Returns([StepName.PushToMerchantAdvanceAmount.ToString()]);

        await _paymentJobProcessor.Process(default);

        _paymentFlowTemplatesEngineMock.Received(1).IsCommandSatisfiesExecutionConditions
            (Arg.Any<PaymentRequestCommandEntity>(), Arg.Any<IEnumerable<PaymentRequestCommandEntity>>());
        await _messageSenderMock.Received(TimesCalled.Never).SendMessages(Arg.Is<IEnumerable<ServiceBusMessageBt<PaymentJobMessagePayload>>>
            (y => y.Select(p => p.MessageBody.ExecutableCommandId).SequenceEqual(commandsIds)), ct: default);
    }

    [Fact]
    public async Task Process_AllCommandsInPendingStatus_SendsOnlyCommandsToQueueButDoesNotCallDatabase()
    {
        // Setup mocks for new dependencies
        _dateProvider.CurrentDateTime.Returns(DateTime.Today.AddHours(10)); // Not midnight, so no daily limit reset
        _paymentConfigService.GetAllAionDailyLimitConfigs(default).Returns(new Dictionary<PaymentSubscriptionType, AionDailyLimitConfig>());
        _paymentWindowService.FilterCommandsByPaymentWindow(Arg.Any<List<PaymentRequestCommandEntity>>(), default)
            .Returns(callInfo => callInfo.Arg<List<PaymentRequestCommandEntity>>());
        _paymentWindowService.IsPrefundedFinalPayment(Arg.Any<PaymentRequestCommandEntity>()).Returns(false);
        _paymentWindowService.IsPaymentWindowActive(default).Returns(false);
        _paymentWindowService.GetPaymentWindowConfig(default).Returns(new PaymentWindowConfig());
        _paymentWindowService.GetNextPaymentWindowStartTime(default).Returns(DateTime.UtcNow.AddHours(1));

        var commands = PaymentRequestGroupings.PendingRequestHistoryGroup;
        var commandsIds = commands.SelectMany(x => x.OrderBy(t => t.Transaction!.SequenceNumber).Select(y => y.Id)).ToList();
        _paymentExecutionHistoryRepositoryMock.GetItemsGroupedByPaymentRequestId(default).Returns(commands);
        _paymentFlowTemplatesEngineMock.IsCommandSatisfiesExecutionConditions(Arg.Any<PaymentRequestCommandEntity>(), Arg.Any<IEnumerable<PaymentRequestCommandEntity>>()).Returns(true);
        _unitOfWorkMock.GetMostRecentNotEnoughBalanceHistoryItemsByTransactionIds(Arg.Any<IEnumerable<Guid>>(), default)
            .Returns(new List<PaymentTransactionHistoryEntity?>());
        _paymentFlowTemplatesEngineMock.GetStepNamesWithWaitForEligibleBalanceCondition()
            .Returns([StepName.PushToMerchantAdvanceAmount.ToString()]);

        await _paymentJobProcessor.Process(default);

        _paymentFlowTemplatesEngineMock.Received(commandsIds.Count).IsCommandSatisfiesExecutionConditions(Arg.Any<PaymentRequestCommandEntity>(), Arg.Any<IEnumerable<PaymentRequestCommandEntity>>());
        await _messageSenderMock.Received(TimesCalled.Once).SendMessages(Arg.Is<IEnumerable<ServiceBusMessageBt<PaymentJobMessagePayload>>>
            (y => y.Select(p => p.MessageBody.ExecutableCommandId).SequenceEqual(commandsIds)), ct: default);
    }
}